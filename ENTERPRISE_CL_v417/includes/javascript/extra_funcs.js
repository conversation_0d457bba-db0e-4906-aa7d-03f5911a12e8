function contador (campo, cuentacampo, limite) {
                   
        if (campo.value.length > limite) {
        campo.value = campo.value.substring(0, limite);
        } else {
        cuentacampo.value = limite - campo.value.length;
        }
}

//------------------------------------------------------------------------                      
                
function isNumber(texto) {
                
        if(isNaN(texto)) {
                alert("Debe ingresar un Celular válido (Numérico)");
                window.document.tmc.phone.focus();
                return false; 
        }

}
        
function isEmpty(texto) {
                
        if(texto == "") { 
                return false;
        } else {
                return true;

        }

}

//-----------------------------------------------------------------------
                
function reset () {
                
        document.tmc.reset();

}

function testlargo(texto) {
		var maxCharMobil = document.getElementById("maxSizeMobilNumber").value;
        if((texto.length < maxCharMobil) || (texto.length > maxCharMobil)){
                alert("Debe ingresar un Celular "+maxCharMobil+" digitos");
                window.document.tmc.phone.focus();
                return false;   
        }       
}               
        
//-----------------------------------------------------------------------
        
function isMessage(text) { 
        
        if(text == "") {
                alert("Debe ingresar un Mensaje");
                window.document.tmc.msgtext.focus();
                return false;
        }
        
}

function isGroup(text) {

        if(text == "") {   
                alert("Debe seleccionar un Grupo");
                window.document.tmc.idGroup.focus();
                return false;
        }

}

//-----------------------------------------------------------------------

function isPhone (text) {

        if(isEmpty(text) == false) {
                alert("Debe ingresar un Celular");
                window.document.tmc.phone.focus();
                return false;
        }

        if(testlargo(text) == false) {
                return false;
        }

        if(isNumber(text) == false) {
                return false;
        }

        return true;
}

function isDateAfter (sendDateAfter, dateNow) {

        if(sendDateAfter < dateNow) {
                alert("La Fecha seleccionada es menor a la actual ("+dateNow+")");
                window.document.tmc.sendDateAfter.focus();
                return false;
        }
}


function isDateFromTo (sendDateFrom, sendDateTo) {


        if(sendDateFrom > sendDateTo){
                alert("La Fecha (Hasta) debe ser mayor a la Fecha (Desde)");
                window.document.tmc.sendDateFrom.focus();
                return false;
        }

}

function nuevoAjax(){

        var xmlhttp = false;
                try {
                        xmlhttp = new ActiveXObject("Msxml2.XMLHTTP");
                } catch (e) {
                try {
                        xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
                } catch (E) {
                        xmlhttp = false;
                }
        }

        if (!xmlhttp && typeof XMLHttpRequest!='undefined') {
                xmlhttp = new XMLHttpRequest();
        }

        return xmlhttp;
}

function cargarCombo(id, data, idMarcado, textDefault) {
	$('#' + id).html('');
	if (data != null) {
		if (textDefault != "") {
			var op = $('<option/>');
			op.val(0);
			op.html(textDefault);
			$('#' + id).append(op);
		}

		for (var i in data) {
			var fila = data[i];
			var op = $('<option/>');
			op.val(fila.id);
			op.html(fila.nombre);
			$('#' + id).append(op);
		}
		$('#' + id).val(idMarcado);
	}
}


function selExpresionRegular(key){
	var result = "";
	switch (key) {
		case "DATETIME-HMS": result = "^(\\d{4})(-)(0[1-9]|1[0-2])(-)(0[1-9]|[1-2][0-9]|3[0-1])( )([0-1][0-9]|2[0-3])(:)([0-4][0-9]|5[0-9])(:)([0-4][0-9]|5[0-9])$"; break;
		case "DATETIME-HM": result = "^(\\d{4})(-)(0[1-9]|1[0-2])(-)(0[1-9]|[1-2][0-9]|3[0-1])( )([0-1][0-9]|2[0-3])(:)([0-4][0-9]|5[0-9])$"; break;
		case "DATE": result = "^(\\d{4})(-)(0[1-9]|1[0-2])(-)(0[1-9]|[1-2][0-9]|3[0-1])$"; break;
		case "PHONE_CL": result = "^56[0-9]{9}$"; break;
		case "PHONE_PE": result = "^51[0-9]{9}$"; break;
		case "PHONE_MX": result = "^(521|52)[0-9]{10}$"; break;
		case "NOMBRE": result = "^[a-zA-Z ñÑ-]{1,100}$"; break;
		case "LET_NUM_ESP": result = "^[a-zñÑ0-9 ]{1,100}$"; break;
		case "USER": result = "^[0-9a-zA-Z_]{5,30}$"; break;
		case "ACTIVE": result = "^(y|n)$"; break;
		case "MAIL": result = "^[a-zA-Z0-9_\\-\\.~]{2,}@[a-zA-Z0-9_\\-\\.~]{2,}\\.[a-zA-Z]{2,4}$"; break;
	}
	return result;
}

function validaFormatoPhone(e){
	var text = e.value;
	var contry = "CL";
	contry = document.getElementById("rexPais").value;
	var key = "PHONE_"+contry;
	var rex = new RegExp(selExpresionRegular(key),"gi");
	if(validateRex(rex, text) === 0){
		alert("El formato del telefono es incorrecto.");
		return false;
	}else{
		return true;
	}
}

function validateRex(rex,texto){
	var arr = texto.match(rex);
	var cont = 0;
	if(arr !== null){
		cont = arr.length;
	}
	return cont;
}

function parseStausUser(status){
	var statusUser = "";
	switch (status) {
		case "PENDING":
			statusUser = "PENDIENTE";
			break;
		case "CANCELED":
			statusUser = "CANCELADO";
			break;
		case "PAUSED":
			statusUser = "PAUSADO";
			break;
		case "SENDING":
			statusUser = "ENVIANDO";
			break;
		case "FINISHED":
			statusUser = "TERMINADO";
			break;
		default :
			statusUser = "DESCONOCIDO";
	}
	return statusUser;
}
/**
 * 
 * @param {string} id = id del div donde se mostrara el mensaje
 * @param {string} message = mensaje a pintar
 * @param {int} type = opciones de imagen a adjuntar (1=alert, 2 = confirmacion, 3=error)
 * @returns void
 */
function showMessageWithImage(id, message, type) {
    var div = $("<div/>", {"class": "divMessage"});
    div.html("");
    let sourceImage='';
    if(message !== null && message.length > 0){
        switch (type) {
            case 1:
                sourceImage = 'images/icons/alert.png';
                break;
            case 2:
                sourceImage = 'images/icons/confirm.png';
                break;
            case 3:
                sourceImage = 'images/icons/error.png';
                break;
            default:
                break;
	}
        if(sourceImage.length>0){
            var img = $('<img/>');
            img.attr('src',sourceImage);
            div.append(img);
        }
    } else {
        message = "";
    }
    div.append(message);
    $('#'+id).html(div);
}
/**
 * 
 * @param {string} id : objeto a mostrar/oculatar
 * @param {boolean} mostar : estado que se asignara (true = visible false = oculto) 
 * @returns void
 */
function visualizarObjeto(id, mostar){
	var obj = $('#'+id);
	if(mostar){
		obj.show();
	}else{
		obj.hide();
	}
}

function parsetextToDate(text){
	var arr = text.split(' ');
	var arrFecha = arr[0].split('-');
	var arrHora = arr[1].split(':');
	var date = new Date(arrFecha[0], (parseInt(arrFecha[1])-1), arrFecha[2], arrHora[0], arrHora[1], arrHora[2]);
	return date;
}

function generateDateCalendars(resta){
	var dt = new Date();
	var month = dt.getMonth()+1;
	if(month<10)month='0'+month;
	var day = dt.getDate() - resta;
	if(day<10)day='0'+day;
	var year = dt.getFullYear();
	var tfecha = year+'-'+month+'-'+day;
	return tfecha;
}

function validateDateLessToday(date){
	var dateAux = date.split("-");
	var today = new Date();
	var x=new Date(dateAux[0],dateAux[1]-1,dateAux[2],today.getHours(),today.getMinutes(),today.getSeconds(),today.getMilliseconds());
	if (x > today)
		return true;
	else
		return false;
}

function validateDateLessYerterday(date){
	var dateAux = date.split("-");
	var today = new Date();
	var x=new Date(dateAux[0],dateAux[1]-1,dateAux[2],today.getHours(),today.getMinutes(),today.getSeconds(),today.getMilliseconds());
	if (x >= today)
		return true;
	else
		return false;
}

function validateDateInicioMayorqueTermino(date,date2){
	var dateAux = date.split("-");
        var dateAuxTo = date2.split("-");
        
	var x=new Date(dateAux[0],dateAux[1]-1,dateAux[2]);
        var y=new Date(dateAuxTo[0],dateAuxTo[1]-1,dateAuxTo[2]);

        if (x > y || x == y)
		return true;
	else
		return false;
}

function parseTextDateFromGMT(textDate, offsetValue, inversor){
	var fullDate = textDate.split(' ');
	var arrDate = fullDate[0].split('-');
	var arrTime = fullDate[1].split(':');
	var now = new Date(parseInt(arrDate[0]) ,parseInt(arrDate[1])-1,parseInt(arrDate[2]),parseInt(arrTime[0]),parseInt(arrTime[1]),parseInt(arrTime[2]));
	var date = new Date(now.getTime() + offsetValue * 60 * 60 * 1000 * inversor);
	var year = date.getFullYear();
	var length = 2;
	year = completeLength(year,4);
	var month = date.getMonth()+1;
	month = completeLength(month,length);
	var day = date.getDate();
	day = completeLength(day,length);
	var hour = date.getHours();
	hour = completeLength(hour,length);
	var minute = date.getMinutes();
	minute = completeLength(minute,length);
	var second = date.getSeconds();
	second = completeLength(second,length);
	return year+'-'+month+'-'+day+' '+hour+':'+minute+':'+second;
}

function completeLength(value, length){
	var result = ''+value;
	while(result.length < length){
		result = '0'+result;
	}
	return result;
}
//Solo permite introducir números.
function soloNumeros(e){
    var key = window.event ? e.which : e.keyCode;
    if (key < 48 || key > 57) {
        //Usando la definición del DOM level 2, "return" NO funciona.
        e.preventDefault();
    }
}
