const msg_alert = 1;
const msg_ok = 2;
const msg_error = 3;

const IdTxtAdmin = 'txtAdmin';
const idCboCompanyFilter='cboCompany-filterListCampaing';
const idbtnBuscarFilter = 'btnBuscar-filterListCampaing';
const idtxtDataFilter = 'txtData-filterListCampaing';

const iddivMessage = 'divMessage-listCampaing';
const iddivImgLoading='divImgLoading-listCampaing';

const idcboRowsPageTable = 'cboRowsPage-tableListCampaing';
const idhddPageTable = 'hddPage-tableListCampaing';
const iddivPagesTable = 'divPages-tableListCampaing';
const idbtnAtrasTable = 'btnAtras-tableListCampaing';
const idbtnSiguienteTable = 'btnSiguiente-tableListCampaing';

const idDivTable = 'div-tableListCampaing';
const idDivErrorTable = 'divError-listCampaing';
const idBodyTable = 'bodyTable-tableListCampaing';

$(document).ready(function(){
	cargaInicial();
	eventos();
});

function cargaInicial() {
	$('#'+idcboRowsPageTable).val(10);
	cargarSelectCompany();
	cargarTablaCampaign();
}

function eventos() {
	click();
	change();
}


function change() {
	$('#'+idCboCompanyFilter).change(function () {
		$('#'+idhddPageTable).val(1);
		cargarTablaCampaign();
	});
	$('#'+idcboRowsPageTable).change(function () {
		$('#'+idhddPageTable).val(1);
		cargarTablaCampaign();
	});
}

function click() {
	$('#'+idbtnBuscarFilter).click(function () {
		$('#'+idhddPageTable).val(1);
		cargarTablaCampaign();
	});
	
}

function cargarSelectCompany() {
	$.ajax({
		url: "ajaxfuncs/general/listCompany.php",
		contentType: "application/json",
		success: function (data, textStatus, jqXHR) {
			if (data.result == "OK") {
				cargarCombo(idCboCompanyFilter, data.data, 0, 'Todas');
			} else {
				console.log(data.message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log("error al cargar las compañias");
		}
	});
}

function cargarTablaCampaign() {
	var company = $('#'+idCboCompanyFilter).val();
	var txtfilter = $("#"+idtxtDataFilter).val();
	var page = $('#'+idhddPageTable).val();
	var rows = $('#'+idcboRowsPageTable).val();

	if (company == null)
		company = 0;
	if (txtfilter == null)
		txtfilter = "";
	if (page == null)
		page = 1;

	var params = "page=" + page;
	params += "&company=" + company;
	params += "&txtfilter=" + txtfilter;
	params += "&rows=" + rows;

	$.ajax({
		url: "ajaxfuncs/campaign/listPendingCampaign.php?" + params,
		contentType: "application/json",
		beforeSend: function (xhr) {
			mostarLoading(true);
		},
		success: function (data, textStatus, jqXHR) {
			if (data.result == null) {
				data = JSON.parse(data);
			}
			if (data.result == "OK") {
				crearBtnHojas(data.totalRows);
				agregarDataListCampaign(data.data);
				
				$('#'+idDivTable).show();
				$('#'+idDivErrorTable).hide();
			} else {
				console.log(data.message);
				$('#'+idDivTable).hide();
				$('#'+idDivErrorTable).show();
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$('#'+idDivTable).hide();
			$('#'+idDivErrorTable).show();
		},
		complete: function (jqXHR, textStatus) {
			mostarLoading(false);
		}
	});
}

function agregarDataListCampaign(data) {
	$('#'+idBodyTable).html('');
	if (data != null) {
		for (var i in data) {
			var fila = data[i];
			var tr = $('<tr/>');

			if (i % 2 == 0) {
				tr.addClass('trPar');
			} else {
				tr.addClass('trInpar');
			}
                        
                        var tdCompany = $('<td/>').html(fila.id);
			tdCompany.css('min-width','100px');
			tdCompany.css('max-width','100px');
			tr.append(tdCompany);
                        
			var tdCompany = $('<td/>').html(fila.company);
			tdCompany.css('min-width','100px');
			tdCompany.css('max-width','100px');
			tr.append(tdCompany);
			
			var tdName = $('<td/>').html(fila.nombre);
			tdName.css('min-width','200px');
			tdName.css('max-width','200px');
			tr.append(tdName);
			
			var tdStart = $('<td/>').html(fila.datestart);
			tdStart.css('min-width','150px');
			tdStart.css('max-width','150px');
			tr.append(tdStart);
			
			var tdEnd = $('<td/>').html(fila.dateend);
			tdEnd.css('min-width','150px');
			tdEnd.css('max-width','150px');
			tr.append(tdEnd);
			
			var tdAdvance = $('<td/>').html(fila.proceced+"/"+fila.total);
			tdAdvance.css('min-width','50px');
			tdAdvance.css('max-width','50px');
			tr.append(tdAdvance);
			
			var tdStatus = $('<td/>').html(parseStausUser(fila.estado));
			tdStatus.css('min-width','100px');
			tdStatus.css('max-width','100px');
			tr.append(tdStatus);
			/*
			var td7 = $('<td/>').html(fila.reagendar);
			tdCompany.css('min-width','100px');
			tdCompany.css('max-width','100px');
			tr.append(td7);
			*/
			var tdUser = $('<td/>').html(fila.user);
			tdUser.css('min-width','100px');
			tdUser.css('max-width','100px');
			tr.append(tdUser);
			
			var tdAction = $('<td/>');
			tdAction.css('min-width','100px');
			tdAction.css('max-width','100px');
			var admin = $('#'+IdTxtAdmin);
			if(admin=1){
				var divPlay= $('<div/>',{'class':'containerbtnImg33'});
				if(fila.estado==='PAUSED'){
					var imgPlay = $('<img/>',{'src':'images/icons/play.png'});
					imgPlay.attr('onClick', 'resendCampaign(' + fila.id + ');');
					imgPlay.attr('class', 'btnImg');
					imgPlay.attr('title', 'Activar');
					divPlay.append(imgPlay);
				}else{
					divPlay.html('&nbsp;');
				}
				tdAction.append(divPlay);
				
				var divPause= $('<div/>',{'class':'containerbtnImg33'});
				if(fila.estado==='SENDING'){
					var imgPause = $('<img/>',{'src':'images/icons/pause.png'});
					imgPause.attr('onClick', 'pauseCampaign(' + fila.id + ');');
					imgPause.attr('class', 'btnImg');
					imgPause.attr('title', 'Detener');
					divPause.append(imgPause);
				}else{
					divPause.html('&nbsp;');
				}
				tdAction.append(divPause);
				
				var divCancel= $('<div/>',{'class':'containerbtnImg33'});
				if(fila.estado==='PAUSED'|| fila.estado==='PENDING'){
					var imgCancel = $('<img/>',{'src':'images/icons/cancel.png'});
					imgCancel.attr('onClick', 'cancelCampaign(' + fila.id + ');');
					imgCancel.attr('class', 'btnImg');
					imgCancel.attr('title', 'Cancelar');
					divCancel.append(imgCancel);
				}else{
					divCancel.html('&nbsp;');
				}
				tdAction.append(divCancel);
			}
			tr.append(tdAction);

			$('#'+idBodyTable).append(tr);
		}
	}
}

function resendCampaign(id){
	mostrarMessage('');
	if (confirm("¿Está Seguro de REANUDAR el envio de la campaña?")) {
		$.ajax({
			url: "ajaxfuncs/campaign/resendCampaign.php?id=" + id,
			contentType: "application/json",
			beforeSend: function (xhr) {
				mostarLoading(true);
			},
			success: function (data, textStatus, jqXHR) {
				if (data.result == null) {
					data = JSON.parse(data);
				}
				if (data.result == "OK") {
					mostrarMessage(data.message, msg_ok);
					cargarTablaCampaign();
				} else {
					mostrarMessage(data.message,msg_error);
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error al enviar el cambio de estado a enviando.");
			},
			complete: function (jqXHR, textStatus) {
				mostarLoading(false);
			}
		});
	}
}

function pauseCampaign(id){
	mostrarMessage('');
	if (confirm("¿Está Seguro de PAUSAR la campaña?")) {
		$.ajax({
			url: "ajaxfuncs/campaign/pauseCampaign.php?id=" + id,
			contentType: "application/json",
			beforeSend: function (xhr) {
				mostarLoading(true);
			},
			success: function (data, textStatus, jqXHR) {
				if (data.result == null) {
					data = JSON.parse(data);
				}
				if (data.result == "OK") {
					mostrarMessage(data.message, msg_ok);
					cargarTablaCampaign();
				} else {
					mostrarMessage(data.message,msg_error);
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error al enviar el cambio de estado a pausado.");
			},
			complete: function (jqXHR, textStatus) {
				mostarLoading(false);
			}
		});
	}
}

function cancelCampaign(id){
	mostrarMessage('');
	if (confirm("¿Está Seguro de cancelar la campaña?")) {
		$.ajax({
			url: "ajaxfuncs/campaign/cancelCampaign.php?id=" + id,
			contentType: "application/json",
			beforeSend: function (xhr) {
				mostarLoading(true);
			},
			success: function (data, textStatus, jqXHR) {
				if (data.result == null) {
					data = JSON.parse(data);
				}
				if (data.result == "OK") {
					mostrarMessage(data.message, msg_ok);
					cargarTablaCampaign();
				} else {
					mostrarMessage(data.message,msg_error);
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error al enviar la eliminacion del registro.");
			},
			complete: function (jqXHR, textStatus) {
				mostarLoading(false);
			}
		});
	}
}

function mostarLoading(mostar){
	visualizarObjeto(iddivImgLoading, mostar);
}

function mostrarMessage(message, tipo = 0){
	if(message!=null && message.length>0){
		showMessageWithImage(iddivMessage,message, tipo);
	}else{
		$('#'+iddivMessage).html('');
	}
}

function crearBtnHojas(total) {
	var cboRowsPage = $('#'+idcboRowsPageTable);
	var hddPage = $('#'+idhddPageTable);
	var divPages = $('#'+iddivPagesTable);
	var btnAtras = $('#'+idbtnAtrasTable);
	var btnSiguiente = $('#'+idbtnSiguienteTable);
	
	var rows_x_page = cboRowsPage.val();
	if (rows_x_page == null)
		rows_x_page = 10;
	var maxpages = total / rows_x_page;
	var init = 0, saltos = 2, viewPages = 5;
	var txtPage = hddPage.val();
	divPages.html('');
	var page = parseInt(txtPage);
	maxpages = Math.trunc(maxpages);
	var diff = total % rows_x_page;
	if (diff > 0)
		maxpages += 1;
	btnAtras.hide();
	btnSiguiente.hide();
	init = page;
	var flag = true;
	var flagsaltos = saltos;
	while (flag) {
		if (flagsaltos == 0 || init <= 1)
		{
			flag = false;
		} else {
			init--;
			flagsaltos--;
		}
	}
	var index = 0;
	var clase = "";
	for (index = init; index <= maxpages; index++) {
		if (page == index) {
			clase = "buttom_selected";
		} else {
			clase = "buttom";
		}
		var btn = $('<span/>', {"class": clase, "onclick": "loadPage(" + index + ")"}).html(index);
		divPages.append(btn);
		var fin = index;
		if (fin == (page + saltos)) {
			index = maxpages;
		}
	}
	if ((fin) < maxpages) {
		btnSiguiente.show();
	}
	if (init > 1) {
		btnAtras.show();
	}
}

function loadPage(page) {
	mostrarMessage('');
	$('#'+idhddPageTable).val(page);
	cargarTablaCampaign();
}

