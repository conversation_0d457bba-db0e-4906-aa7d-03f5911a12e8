const msg_alert = 1;
const msg_ok = 2;
const msg_error = 3;

const idHddIdCompany = 'hddIdCompany-create';
const idViewCompany = 'viewCompany';
const idTxtNombre = 'txtNombre-create';
const idCboCompany = 'cboCompany-create';
const idTxtFechaInicio = 'txtFechaInicio-create';
const idImgStartDate = 'imgStartDate-create';
const idImgEndDate = 'imgEndDate-create';
const idTxtFechaTermino = 'txtFechaTermino-create';
const idTxtMessage = 'txtMessage-create';
const idTxtFile = 'txtFile-create';

const idBtnSave = 'btnSave-create';
const idBtnCancel = 'btnCancel-create';
const idBtnClean = 'btnClean-create';

const idDivMessage = 'divMessage-create';
const idDivImgLoading = 'divImgLoading-create';

$(document).ready(function () {
	cargaInicial();
	eventos();
});

function cargaInicial() {
	mostarLoading(false);
	configDatePicker();
	cargarSelectCompany();
}

function cargarSelectCompany() {
	//var viewCompany = $('#'+idViewCompany).val();
	//if(viewCompany==='1'){
	$.ajax({
		url: "ajaxfuncs/general/listCompany.php",
		contentType: "application/json",
		success: function (data, textStatus, jqXHR) {
			if (data.result == "OK") {
				var idCompany = 0;
				var id = $('#'+idHddIdCompany).val();
				if(id > 0){
					idCompany = id;
				}
				cargarCombo(idCboCompany, data.data, idCompany, 'Todas');
			} else {
				console.log(data.message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log("error al cargar las compañias");
		}
	});
	//}
}

function configDatePicker() {
	var fecha = new Date();
	var anio = parseInt(fecha.getFullYear());
	var mes = (parseInt(fecha.getMonth())+1);
	var dia = parseInt(fecha.getDate());
	var hour = parseInt(fecha.getHours());
	var minute = parseInt(fecha.getMinutes());
	var txtDate = anio+'-'+(mes<10?'0'+mes:mes)+'-'+(dia<10?'0'+dia:dia)+' '+(hour<10?'0'+hour:hour)+':'+(minute<10?'0'+minute:minute);
	$('#'+idTxtFechaInicio).val(txtDate);
	$('#'+idTxtFechaTermino).val(txtDate);
	var year = fecha.getFullYear()-1;
	Calendar.setup({inputField: idTxtFechaInicio, range: [year, 2999],
		ifFormat: "%Y-%m-%d %H:%M",
		showsTime: true,
		timeFormat: "24",
		button: idImgStartDate
	});
	Calendar.setup({inputField: idTxtFechaTermino, range: [year, 2999],
		ifFormat: "%Y-%m-%d %H:%M",
		showsTime: true,
		timeFormat: "24",
		button: idImgEndDate
	});
	
}

function eventos() {
	click();
}

function click() {
	$('#' + idBtnSave).click(function () {
		saveCampaign();
	});
	$('#' + idBtnClean).click(function () {
		limpiarCampos();
	});
}

function mostarLoading(mostar) {
	visualizarObjeto(idDivImgLoading, mostar);
}

function limpiarCampos() {
	$('#' + idTxtNombre).val('');
	$('#' + idTxtFechaInicio).val('');
	$('#' + idTxtFechaTermino).val('');
	$('#' + idTxtMessage).val('');
	$('#' + idTxtFile).val('');
	$('#' + idCboCompany).val(0);
}

function saveCampaign() {
	var data = {};
	data.nombre = $('#' + idTxtNombre).val();
	data.fechaInicio = $('#' + idTxtFechaInicio).val()+":00";
	data.fechaTermino = $('#' + idTxtFechaTermino).val()+":00";
	data.message = $('#' + idTxtMessage).val();
	data.idcompany = $('#' + idCboCompany).val();

	var files = $('#' + idTxtFile)[0].files;
	if (files != null) {
		data.file = files[0];
	}
	cargarCampania(data);

}

function cargarCampania(data) {
	if (validarDatos(data)) {
		var form = parseDataCampaignToDataForm(data);
		$.ajax({
			url: "ajaxfuncs/campaign/loadCampaignByFile.php",
			type: 'POST',
			data: form,
			processData: false,
			contentType: false,
			cache: false,
			beforeSend: function (xhr) {
				mostarLoading(true);
			},
			success: function (data, textStatus, jqXHR) {
				
				if (data.result == "OK") {
					mostrarMessage(data.message, msg_ok);
					limpiarCampos();
				} else {
					if(data.code==1006){
						var datalist = data.data;
						var array = Object.values(datalist)
						for (var index = 0; index < array.length; index++) {
							mostrarMessage(array[index], msg_error);
						}
					}
					mostrarMessage(data.message, msg_error);
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error enviar los datos de la insercion.");
			},
			complete: function (jqXHR, textStatus) {
				mostarLoading(false);
			}
		});
	}
}

function validarDatos(data) {
	mostrarMessage('');
	var contErr = 0;
	var response = false;
	var rex = new RegExp(selExpresionRegular("LET_NUM_ESP"), "gi");
	if (validateRex(rex, data.nombre.trim()) == 0) {
		var msgText = "Ingrese un nombre valido solo con letras, numeros y espacions de largo 1 a 100 caracteres (ej: nombre campaña 1).";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
//	var viewCompany = $('#' + idViewCompany).val();
//	if (viewCompany === '1') {
	if (data.idcompany == 0) {
		var msgText = "Seleccione una compañia";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
//	}
	var textDateStart = data.fechaInicio.trim();
	var rex = new RegExp(selExpresionRegular("DATETIME-HMS"), "gi");
	if (validateRex(rex, textDateStart) == 0) {
		var msgText = "Ingrese una fecha de inicio valida (ej: 2017-12-31 23:59).";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
	var textDateEnd = data.fechaTermino.trim();
	if (validateRex(rex, textDateEnd) == 0) {
		var msgText = "Ingrese una fecha de termino valida (ej: 2017-12-31 23:59:59).";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
	var dateNow = new Date();
	console.log("now: "+dateNow.getTime());
	var dateStart = parsetextToDate(textDateStart);
	console.log("start: "+dateStart.getTime());
	if(dateStart <= dateNow){
		var msgText = "La fecha de inicio debe ser mayor que la fecha actual.";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
	
	var dateEnd = parsetextToDate(textDateEnd);
	console.log("end: "+dateEnd.getTime());
	if(dateEnd < dateStart){
		var msgText = "La fecha de termino debe ser mayor que la fecha de inicio.";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
	if (data.message != null) {
		if (data.message.length == 0) {
			var msgText = "Ingrese algun texto de mensaje.";
			mostrarMessage(msgText, msg_error);
			contErr++;
		}
	} else {
		var msgText = "Ingrese algun texto de mensaje.";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
	if (data.file == null) {
		var msgText = "Seleccione un archivo a cargar.";
		mostrarMessage(msgText, msg_error);
		contErr++;
	}
	if (contErr == 0) {
		response = true;
	}
	return response;
}

function mostrarMessage(message, tipo = 0) {
	if (message != null && message.length > 0) {
		showMessageWithImage(idDivMessage, message, tipo);
	} else {
		$('#' + idDivMessage).html('');
}
}

function parseDataCampaignToDataForm(data) {
	var form = new FormData();
	form.append('nombre', data.nombre);
	form.append('idcompany', data.idcompany);
	form.append('fechainicio', data.fechaInicio);
	form.append('fechatermino', data.fechaTermino);
	form.append('message', data.message);
	form.append('file', data.file);
	return form;
}