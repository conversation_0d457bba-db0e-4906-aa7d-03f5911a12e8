const idBtnModificar = "btnModificar";
const idHddIdAccount = "hddIdAccount";
const idLogin = "login";
const idOldpassword="oldpassword";
const idPassword1="password1";
const idPassword2="password2";
const idResultado = "resultado";
const idLoading = "loading";

$(document).ready(function(){
	$('#'+idBtnModificar).click(function(){
		changePassword();
	});
});

function changePassword(){
	var login = $('#'+idLogin).val();
	var idAccount = $('#'+idHddIdAccount).val()
	var pass1 = $('#'+idPassword1).val();
	var pass2 = $('#'+idPassword2).val();
	var old = $('#'+idOldpassword).val();

	var data = {};
	data.pass1 = pass1;
	data.pass2 = pass2;
	data.old = old;

	if (validateDataUsere(data)){
		if(pass1 === pass2){
		var jsonData = {
			"id": idAccount
			, "username":login
			, "password": pass1
			, "oldPassword": old
		};
		
		var token = sessionStorage.getItem("token");
		var config = JSON.parse( sessionStorage.getItem('config') );
		$.ajax({
			url: config.urlServiceAccount + "/api/user/changePass",
			type: 'POST',
			contentType: "application/json",
			data: JSON.stringify(jsonData),
			headers: {
				"Authorization": token
			},
			beforeSend: function (xhr) {
				$('#'+idLoading).html("loading...");
			},
			success: function (data, textStatus, jqXHR) {
				if (data.code == 0) {
//					$('#divListUser').show();
//					$('#divError').hide();
				}
				$('#'+idResultado).html(data.message); 
			},
			error: function (jqXHR, textStatus, errorThrown) {
				comnsole.log("error");
//				$('#divListUser').hide();
//				$('#divError').show();
			},
			complete: function (jqXHR, textStatus) {
				$('#'+idLoading).html('');
			}
		});
		
		
	}else{
		$('#'+idResultado).html("La contraseñas nuevas no coinciden.");
	}

}}

	function validateDataUsere(data) {
	
	var response = false ;
	var contErr=0;
	var regular = /^[0-9a-zA-Z]{5,15}$/;	
	if(!regular.test(data.pass1)){
	$('#'+idResultado).html("Ingrese Password solo con letras o numeros largo de 5 a 15 caracteres");
		contErr++;
	}

	if(!regular.test(data.pass2)){
	$('#'+idResultado).html("INgrese Password solo con letras o numeros largo de 5 a 15 caracteres");
		contErr++;
	}

//	var rex = new RegExp(selExpresionRegulare("PASS"),"gi");
	//if(validateRexe(rex, data.pass1) == 0){
	//	$('#'+idResultado).html("Ingrese password solo con letras y numeros");
	//	contErr++;
	//}
//
//	var rex = new RegExp(selExpresionRegulare("PASS"),"gi");
//	if(validateRexe(rex, data.pass2) == 0){
//		$('#'+idResultado).html("Ingrese password solo con letras y numeros");
//		contErr++;
//	}
//
//	var rex = new RegExp(selExpresionRegulare("OLD"),"gi");
//	if(validateRexe(rex, data.old) == 0){
//		$('#'+idResultado).html("Su Actual Password sin caracteres especiales hola");
//		contErr++;
//	}
///	
//		
	if(contErr==0){
		response = true;
	}
	return response;
}

//function validateRexe(rex,texto){
//	var arr = texto.match(rex);
///	var cont = 0;
//	if(arr !== null){
//		cont = arr.length;
//	}
//	return cont;
//}



//function selExpresionRegulare(key){
///	var result = "";
//	switch (key) {
//		case "PASS": result = "^[0-9a-zA-Z]$"; break;
//		case "OLD": result = "[a-zA-Z0-9]$";break;
//		}
//	return result;
//}
	




