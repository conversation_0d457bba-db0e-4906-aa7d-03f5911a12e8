
$(document).ready(function () {
	$('#divError').hide();
	$('#divListUser').hide();
	$('#divEditUser').hide();
	cargaInicial();
	eventos();
})

function cargaInicial() {
	$('#cboRowsPage').val(10);
	cargarTablaUsuario();
	cargarSelectCompany();
	cargarSelectProfile();
	cargarSelectStatus();
	cargarSelectTimeZone();
}

function eventos() {
	click();
	change();
}

function click() {
	$('#btnNuevoUser').click(function () {
		$('#divMessage').html('');
		$('#titleLegendEditUser').html('Creaci&oacute;n de usuarios');
		limpiarEditUser();
		var id = $('#hddCompany').val();
		$('#cboEditCompany').val(id);
		$('#divEditUser').show();
		$('#txtEditLogin').focus();
	});
	$('#btnEditCancel').click(function(){
		$('#divEditUser').hide();
		limpiarEditUser();
	});
	$('#btnEditSave').click(function () {
		var data = {};
		data.iduser = $('#txtEditIdUser').val();
		data.idcompany = $('#cboEditCompany').val();
		data.username = $('#txtEditLogin').val();
		data.nombres = $('#txtEditNombres').val();
		data.apellidos = $('#txtEditApellidos').val();
		data.mail = $('#txtEditMail').val();
		data.telefono = $('#txtEditTelefono').val();
		data.unidad = $('#txtEditUnidad').val();
		data.idtimezone = $('#cboEditTimeZone').val();
		data.activo = $('#cboEditStatus').val();
		data.idperfil = $('#cboEditProfile').val();

		if ($('#txtEditIdUser').val() == "0") {
			insertarusuario(data);
		} else {
			actualizarUsuario(data);
		}
	});

	$('#btnBuscarUser').click(function () {
		$('#txtPage').val(1);
		cargarTablaUsuario();
	});
	$('#btnEditClean').click(function () {
		limpiarEditUser();
	});
	$('#btnAtras').click(function(){
		arrowPage("-");
	});
	$('#btnSiguiente').click(function(){
		arrowPage("+");
	});
}

function change() {
	$('#cboFilterPerfil').change(function () {
		$('#txtPage').val(1);
		cargarTablaUsuario();
	});
	$('#cboFilterCompany').change(function () {
		$('#txtPage').val(1);
		cargarTablaUsuario();
	});
	$('#cboRowsPage').change(function(){
		$('#txtPage').val(1);
		cargarTablaUsuario();
	});
}

function cargarTablaUsuario() {
	var company = $('#cboFilterCompany').val();
	var profile = $('#cboFilterPerfil').val();
	var txtfilter = $("#txtFilterData").val();
	var page = $('#txtPage').val();
	var rows = $('#cboRowsPage').val();
	
	if (company == null)
		company = 0;
	if (profile == null)
		profile = 0;
	if (txtfilter == null)
		txtfilter = "";
	if(page==null)page = 1;

	var params = "page=" + page;
	params += "&company=" + company;
	params += "&profile=" + profile;
	params += "&txtfilter=" + txtfilter;
	params += "&rows=" +rows;

	$.ajax({
		url: "ajaxfuncs/maintenance/listUser.php?" + params,
		contentType: "application/json",
		beforeSend: function (xhr) {
			$('#divImgLoading').show();
		},
		success: function (data, textStatus, jqXHR) {
			if(data.result==null){
				data = JSON.parse(data);
			} 
			if (data.result == "OK") {
				crearBtnHojas(data.totalRows);
				agregarDataUsuarios(data.data);
				$('#divListUser').show();
				$('#divError').hide();
			} else {
				console.log(data.message);
				$('#divListUser').hide();
				$('#divError').show();
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			$('#divListUser').hide();
			$('#divError').show();
		},
		complete: function (jqXHR, textStatus) {
			$('#divImgLoading').hide();
		}
	});
}

function cargarSelectCompany() {

	$.ajax({
		url: "ajaxfuncs/general/listCompany.php",
		contentType: "application/json",
		success: function (data, textStatus, jqXHR) {
			if (data.result == "OK") {
				cargarCombo('cboFilterCompany', data.data, 0, 'Todas');
				cargarCombo('cboEditCompany', data.data, 0, 'Todas');
			} else {
				console.log(data.message);
			}

		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log("error al cargar las compañias");
		}
	});
}

function cargarSelectProfile() {

	$.ajax({
		url: "ajaxfuncs/general/listProfilePorUser.php",
		contentType: "application/json",
		success: function (data, textStatus, jqXHR) {
			if (data.result == "OK") {
				cargarCombo('cboEditProfile', data.data, 0, 'Todos');
				cargarCombo('cboFilterPerfil', data.data, 0, 'Todos');
			} else {
				console.log(data.message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log("error al cargar los perfiles");
		}
	});
}

function cargarSelectStatus() {
	var data = [
		{"id": "Y", "nombre": "ACTIVO"}
		, {"id": "N", "nombre": "INACTIVO"}
	];
	cargarCombo('cboEditStatus', data, "N", '');
}

function cargarSelectTimeZone() {
	$.ajax({
		url: "ajaxfuncs/general/listTimeZone.php",
		contentType: "application/json",
		success: function (data, textStatus, jqXHR) {
			if (data.result == "OK") {
				cargarCombo('cboEditTimeZone', data.data, 0, 'Todos');
			} else {
				console.log(data.message);
			}
		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log("error al cargar los time zone.");
		}
	});
}

function agregarDataUsuarios(data) {
	$('#bodyListUser').html('');
	if (data != null) {
		for (var i in data) {
			var fila = data[i];
			var tr = $('<tr/>');

			if (i % 2 == 0) {
				tr.addClass('trPar');
			} else {
				tr.addClass('trInpar');
			}
			
			var tdusername = $('<td/>').html(fila.username);
			tdusername.css('min-width','70px');
			tdusername.css('max-width','70px');
			tr.append(tdusername);
			
			var tdnombres = $('<td/>').html(fila.nombres);
			tdnombres.css('min-width','100px');
			tdnombres.css('max-width','100px');
			tr.append(tdnombres);
			
			var tdapellidos = $('<td/>').html(fila.apellidos);
			tdapellidos.css('min-width','100px');
			tdapellidos.css('max-width','100px');
			tr.append(tdapellidos);
			
			var tdmail = $('<td/>').html(fila.mail);
			tdmail.css('min-width','150px');
			tdmail.css('max-width','150px');
			tr.append(tdmail);
			
			var tdtelefono = $('<td/>').html(fila.telefono);
			tdtelefono.css('min-width','100px');
			tdtelefono.css('max-width','100px');
			tr.append(tdtelefono);
			
			var tduunidad = $('<td/>').html(fila.unidad);
			tduunidad.css('min-width','100px');
			tduunidad.css('max-width','100px');
			tr.append(tduunidad);
			
			var tdactivo;
			if (fila.activo == "Y") {
				tdactivo = $('<td/>').html("ACTIVO");
			} else {
				tdactivo = $('<td/>').html("INACTIVO");
			}
			tdactivo.css('min-width','100px');
			tdactivo.css('max-width','100px');
			tr.append(tdactivo);
			
			var tdcompany = $('<td/>').html(fila.company);
			tdcompany.css('min-width','100px');
			tdcompany.css('max-width','100px');
			tr.append(tdcompany);
			
			var tdtemezone = $('<td/>').html(fila.timezone);
			tdtemezone.css('min-width','150px');
			tdtemezone.css('max-width','150px');
			tr.append(tdtemezone);
			
			var tdperfil = $('<td/>').html(fila.perfil);
			tdperfil.css('min-width','100px');
			tdperfil.css('max-width','100px');
			tr.append(tdperfil);
			
			var btnEdit = $('<button/>');
			btnEdit.html('Editar');
			btnEdit.attr('onClick', 'editUser(' + fila.iduser + ');');
			var tdedicion = $('<td/>').append(btnEdit);
			tdedicion.css('min-width','100px');
			tdedicion.css('max-width','100px');
			tr.append(tdedicion);
			
			var btnReset = $('<button/>');
			btnReset.html('Reset');
			btnReset.attr('onClick', 'resetPass(' + fila.iduser + ');');
			var tdreset = $('<td/>').append(btnReset);
			tdreset.css('min-width','100px');
			tdreset.css('max-width','100px');
			tr.append(tdreset);
			
			$('#bodyListUser').append(tr);
		}
	}
}

function resetPass(id){
	$('#divMessage').html('');
	var token = sessionStorage.getItem("token");
	var config = JSON.parse( sessionStorage.getItem('config') );
	
	$.ajax({
			url: config.urlServiceAccount + "/api/user/resetPass/"+id,
			type: 'POST',
			contentType: "application/json;charset=UTF-8",
			headers: {
				"Authorization": token
			},
			beforeSend: function (xhr) {
				$('#divImgLoading').show();
			},
			success: function (data, textStatus, jqXHR) {
				var img;
				if (data.code == 0) {
					img = $("<img/>",{"src":"images/icons/confirm.png"});
					$('#divMessage').append(img);
				}else{
					img = $("<img/>",{"src":"images/icons/error.png"});
				}
				$('#divMessage').append(data.message);
				$('#divMessage').show();
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error al cargar los time zone.");
			},
			complete: function (jqXHR, textStatus) {
				$('#divImgLoading').hide();
			}
		});
}

function editUser(id) {
	$('#divMessage').html('');
	if (id > 0) {
		$.ajax({
			url: "ajaxfuncs/maintenance/userById.php?idUser=" + id,
			contentType: "application/json",
			beforeSend: function (xhr) {
				$('#divImgLoading').show();
			},
			success: function (data, textStatus, jqXHR) {
				if (data.result == "OK") {
					$('#titleLegendEditUser').html('Edici&oacute;n de usuarios');
					cargarUsuarioParaEditar(data.data);
					$('#divEditUser').show();
					$('#txtEditLogin').focus();
				} else {
					console.log(data.message);
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error al cargar los time zone.");
			},
			complete: function (jqXHR, textStatus) {
				$('#divImgLoading').hide();
			}
		});
	}
}

function cargarUsuarioParaEditar(data) {
	if (data != null) {
		for (var i in data) {
			var fila = data[i];
			$('#txtEditIdUser').val(fila.iduser);
			$('#cboEditCompany').val(fila.idcompany);

			$('#txtEditLogin').val(fila.username);
			$('#txtEditNombres').val(fila.nombres);
			$('#txtEditApellidos').val(fila.apellidos);
			$('#txtEditMail').val(fila.mail);
			$('#txtEditTelefono').val(fila.telefono);
			$('#txtEditUnidad').val(fila.unidad);

			$('#cboEditTimeZone').val(fila.idtimezone);
			$('#cboEditStatus').val(fila.activo);
			$('#cboEditProfile').val(fila.idperfil);
		}
	}
}

function limpiarEditUser() {
	$('#txtEditIdUser').val(0);
	$('#cboEditCompany').val(0);
	$('#txtEditLogin').val('');
	$('#txtEditNombres').val('');
	$('#txtEditApellidos').val('');
	$('#txtEditMail').val('');
	$('#txtEditTelefono').val('');
	$('#txtEditUnidad').val('');
	$('#cboEditTimeZone').val(0);
	$('#cboEditStatus').val('N');
	$('#cboEditProfile').val(0);
	$('#divMessage').html('');
}

function actualizarUsuario(dataUser) {
	if (validateDataUser(dataUser)) {
		var form = parseDataUserToDataForm(dataUser);
		$.ajax({
			url: "ajaxfuncs/maintenance/updateUser.php",
			type: "POST",
			data:form,
			processData: false,
			contentType: false,
			dataType: 'json',
			beforeSend: function (xhr) {
				$('#divImgLoading').show();
			},
			success: function (data, textStatus, jqXHR) {
				var img;
				if (data.result == "OK") {
					cargarTablaUsuario();
					limpiarEditUser();
					$('#divEditUser').hide();
					img = $("<img/>",{"src":"images/icons/confirm.png"});
					$('#divMessage').append(img);
				} else {
					img = $("<img/>",{"src":"images/icons/error.png"});
				}
				$('#divMessage').append(data.message);
				$('#divMessage').show();
				
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error al cargar los time zone.");
				$('#divEditUser').show();
			},
			complete: function (jqXHR, textStatus) {
				$('#divImgLoading').hide();
			}
		});
	}
}

function insertarusuario(dataUser) {
	if (validateDataUser(dataUser)) {
		var form = parseDataUserToDataForm(dataUser);
		$.ajax({
			url: "ajaxfuncs/maintenance/insertUser.php",
			type: 'POST',
			data: form,
			processData: false,
			contentType: false,
			beforeSend: function (xhr) {
				$('#divImgLoading').show();
			},
			success: function (data, textStatus, jqXHR) {
				var img;
				if (data.result == "OK") {
					cargarTablaUsuario();
					limpiarEditUser();
					$('#divEditUser').hide();
					img = $("<img/>",{"src":"images/icons/confirm.png"});
					$('#divMessage').append(img);
				} else {
					img = $("<img/>",{"src":"images/icons/error.png"});
				}
				$('#divMessage').append(data.message);
				$('#divMessage').show();
			},
			error: function (jqXHR, textStatus, errorThrown) {
				console.log("error al enviar los datos del usuario.");
			},
			complete: function (jqXHR, textStatus) {
				$('#divImgLoading').hide();
			}
		});
	}
}

function validateDataUser(data) {
	
	var response = false;
	var contErr=0;
	$('#divMessage').html('');
	
	if (data.idCompany <= 0) {
		mostrarMessage("Seleccione una compa&ntilde;ia", 3);
		contErr++;
	}
	
	if (data.idtimezone <= 0) {
		mostrarMessage("Seleccione una Zona Horaria.", 3);
		contErr++;
	}
	
	if (data.idperfil <= 0) {
		mostrarMessage("Seleccione un perfil", 3);
		contErr++;
	} 
	var rex = new RegExp(selExpresionRegular("USER"),"gi");
	if(validateRex(rex, data.username) == 0){
		mostrarMessage("Ingrese un login valido solo con letras y numeros de largo 5 a 30 caracteres (ej: user2).", 3);
		contErr++;
	}
	
	var rex = new RegExp(selExpresionRegular("NOMBRE"),"gi");
	if(validateRex(rex, data.nombres) == 0){
		mostrarMessage("Ingrese un nombre valido solo con letras y espacios de largo 1 a 100 caracteres (Ej: 'nombre nombre' )", 3);
		contErr++;
	}
	
	var rex = new RegExp(selExpresionRegular("NOMBRE"),"gi");
	if(validateRex(rex, data.apellidos) == 0){
		mostrarMessage("Ingrese un apellidos valido solo con letras y espacios de largo 1 a 100 caracteres (Ej: 'apellido apellido' )", 3);
		contErr++;
	}
	
	var rex = new RegExp(selExpresionRegular("MAIL"), "gi");
	if(validateRex(rex, data.mail) == 0){
		mostrarMessage("Ingrese un mail valido en formato mail.-_2@mail.-_2.com", 3);
		contErr++;
	}
	var pais = $('#rexPais').val();
	var mascara = $('#placeHolderFormatMobilNumber').val();
	if (data.telefono != ""){
		var rex = new RegExp(selExpresionRegular("PHONE_"+pais),"gi");
		if(validateRex(rex, data.telefono) == 0){
			mostrarMessage("Ingrese un telefono valido en formato "+mascara, 3);
			contErr++;
		}
	}
	
	if (data.unidad != null){
		var rex = new RegExp(selExpresionRegular("NOMBRE"),"gi");
		if(validateRex(rex, data.unidad) == 0){
			mostrarMessage("Ingrese un unidad valida solo letras y espacios de largo 1 a 100 caracteres (EJ: administracion)", 3);
			contErr++;
		}
	}
	
	var rex = new RegExp(selExpresionRegular("ACTIVE"),"gi");
	if(validateRex(rex, data.activo) == 0){
		mostrarMessage("Seleccione un estado valido", 3);
		contErr++;
	}
	
	if(contErr==0){
		response = true;
	}
	return response;
}

function mostrarMessage(error, type) {
	var img;
	switch (type) {
		case 1:
			img = "<img src=\"images/icons/alert.png\">";
			break;
		case 2:
			img = "<img src=\"images/icons/confirm.png\">";
			break;
		case 3:
			img = "<img src=\"images/icons/error.png\">";
			break;
		default:
			img = "<img src=\"images/icons/error.png\">";
			break;
	}
	var div = $("<div/>", {"class": "divMessage"});
	div.append(img);
	div.append(error);
	$('#divMessage').append(div);
}

function parseDataUserToDataForm(dataUser){
	var form = new FormData();
	form.append('iduser',dataUser.iduser);
	form.append('idcompany',dataUser.idcompany);
	form.append('username',dataUser.username);
	form.append('nombres',dataUser.nombres);
	form.append('apellidos',dataUser.apellidos);
	form.append('mail',dataUser.mail);
	form.append('telefono',dataUser.telefono);
	form.append('unidad',dataUser.unidad);
	form.append('idtimezone',dataUser.idtimezone);
	form.append('activo',dataUser.activo);
	form.append('idperfil',dataUser.idperfil);
	return form;
}

function arrowPage(valor){
	var txtPage = $('#txtPage').val();
	var page = 0;
	if(valor=="+"){
		//siguiente
		page = parseInt(txtPage);
		page += 1;
		$('#txtPage').val(page);
	}else{
		//atras
		page = parseInt(txtPage);
		page -= 1;
		if(page < 1) page = 1;
		$('#txtPage').val(page);
	}
	cargarTablaUsuario();
}

function crearBtnHojas(total){
	var rows_x_page = $('#cboRowsPage').val();
	if(rows_x_page==null)rows_x_page=10;
	var maxpages = total/rows_x_page;
	var init = 0, saltos = 2, viewPages = 5;
	var txtPage = $('#txtPage').val();
	$('#btnPages').html('');
	var page = parseInt(txtPage);
	maxpages = Math.trunc(maxpages);
	var diff = total%rows_x_page;
	if(diff > 0) maxpages += 1;
	
	$('#btnAtras').hide();
	$('#btnSiguiente').hide();
	
	init = page;
	var flag = true;
	var flagsaltos = saltos;
	while(flag){
		if(flagsaltos == 0 || init <= 1)
		{
			flag= false;
		}else{
			init --;
			flagsaltos--;
		}
	}
	var index=0;
	var clase ="";
	for(index=init; index <= maxpages; index++){
		if (page == index) {
			clase = "buttom_selected";
		} else {
			clase = "buttom";
		}
		var btn = $('<span/>', {"class": clase, "onclick": "loadPage(" + index + ")"}).html(index);
		$('#btnPages').append(btn);
		var fin = index;
		if(fin == (page+saltos) ){
			index = maxpages;
		}
	}
	if( (fin) < maxpages){
		$('#btnSiguiente').show();
	}
	if(init > 1){
		$('#btnAtras').show();
	}
}

function loadPage(page){
	$('#divMessage').html('');
	$('#txtPage').val(page);
	cargarTablaUsuario();
}