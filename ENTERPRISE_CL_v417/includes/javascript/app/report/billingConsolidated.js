const idCboStaus = "cboStaus";
const idCboInputMode = "cboInputMode";
const idCboCompany = "cboCompany";
const idHeadTable = "headTable";
const idBodyTable = "bodyTable";
const idFooterTable = "footerTable";
const idTxtnumberPhone = "txtnumberPhone";
const idTxtDateFrom = "txtDateFrom";
const idTxtDateTo = "txtDateTo";
const idBtnDateFrom = "btnDateFrom";
const idBtnDateTo = "btnDateTo";
const idCboUser = "cboUser";
const idTxtCampaign = 'campaignId';

const idBtnFind = "btnFind";
const idBtnClean = "btnClean";
const btnGenerateConsolidatedCsv = "btnGenerateConsolidatedCsv";
const btnGenerateDetailCsv = "btnGenerateDetailCsv";

const idHddActualPage = "hddActualPage";
const idBtnBackPage = "btnPageAtras";
const idDivPages = "btnLinkPages";
const idBtnNextPage = "btnPageSiguiente";
const idCboRowsPage = "cboRowsPage";

const idHddIdAccount = "hddIdAccount";
const idHddIdCompany = "hddIdCompany";
const idDivMessage = "divMessage";
const idOffset = "offset";
const idTxtIdProfile = "txtIdProfile";
const idCboSoporte = "cboSoporte";

const idImgLoading = "imgLoading";

$(document).ready(function () {
    initLoad();
    controlEvent();
});

function initLoad() {
    var tfecha = generateYesterdayDate();
    viewHtmlObject(idImgLoading, false);
    $('#' + idHddActualPage).val(1);
    $('#' + idCboRowsPage).val(10);
    $('#' + idTxtDateFrom).val(tfecha);
    $('#' + idTxtDateTo).val(tfecha);
    var profile = $('#' + idTxtIdProfile).val();
    if (profile === '1' || profile === '5') {
        $('.tdSoporte').show();
    } else {
        $('.tdSoporte').hide();
    }
    loadCbo(idCboInputMode, idImgLoading, idDivMessage, "api/list/inputMode");

    configBtnDates(idTxtDateFrom, idBtnDateFrom);
    configBtnDates(idTxtDateTo, idBtnDateTo);

    let promiseLoadCompany = loadCboPromise(idImgLoading, idDivMessage, "api/list/company");
    promiseLoadCompany.done(function (responseData) {
        if (responseData.code === 0) {
            var idCompany = $('#' + idHddIdCompany).val();
            loadCombobox(idCboCompany, responseData.data, idCompany, 'Seleccione...');

            let idUser = $('#' + idHddIdAccount).val();
            loadCboUser(idCboUser, idUser, idCompany, idDivMessage, idImgLoading);
        } else {
            showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
        }
    });
}

function controlEvent() {
    clickEvents();
    changeEvents();
    keyEvents();
}

function clickEvents() {
    $('#' + idBtnFind).click(function () {
        $('#' + idHddActualPage).val(1);
        loadTableReport();
    });

    $('#' + btnGenerateConsolidatedCsv).click(function () {
        generateCsvReportConsolidated(btnGenerateConsolidatedCsv);
    });

    $('#' + btnGenerateDetailCsv).click(function () {
        generateCsvReportDetail(btnGenerateDetailCsv);
    });

}

function changeEvents() {
    $('#' + idCboCompany).change(function () {
        let idUser = $('#' + idHddIdAccount).val();
        let idCompany = $('#' + idCboCompany).val();
        loadCboUser(idCboUser, idUser, idCompany, idDivMessage, idImgLoading);
    });
}

function keyEvents() {
    $('#' + idTxtCampaign).keypress(function (e) {
        keyOnlyNumbers(e);
    });
}


function generateCsvReportDetail(idBtnGenerate) {
    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    if (validateFields()) {
        var json = fillJsonData();
        $.ajax({
            url: config.urlServiceReport + "/api/mt/billing/detail/queue/csv/full",
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(json),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
                disableHtmlObject(idBtnGenerate, true);
                showMessageWithImage(idDivMessage, "Generando...", getTypeMsgDefault());
            },
            success: function (responseData, textStatus, jqXHR) {
                if (responseData.code === 0) {
                    let message = responseData.message + ' con el codigo de seguimiento ' + responseData.data;
                    showMessageWithImage(idDivMessage, message, getTypeMsgSuccess());
                } else {
                    showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
                disableHtmlObject(idBtnGenerate, false);
            }
        });
    }
}

function generateCsvReportConsolidated(idBtnGenerate) {
    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    if (validateFields()) {
        var json = fillJsonData();
        $.ajax({
            url: config.urlServiceReport + "/api/mt/billing/consolidated/queue/csv",
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(json),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
                disableHtmlObject(idBtnGenerate, true);
                showMessageWithImage(idDivMessage, "Generando...", getTypeMsgDefault());
            },
            success: function (responseData, textStatus, jqXHR) {
                if (responseData.code === 0) {
                    let message = responseData.message + ' con el codigo de seguimiento ' + responseData.data;
                    showMessageWithImage(idDivMessage, message, getTypeMsgSuccess());
                } else {
                    showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
                disableHtmlObject(idBtnGenerate, false);
            }
        });
    }
}

function loadTableReport() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    if (validateFields()) {
        var json = fillJsonData();
        $.ajax({
            url: config.urlServiceReport + "/api/mt/billing/consolidated/data",
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(json),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
                disableHtmlObject(idBtnFind, true);
                showMessageWithImage(idDivMessage, "Generando...", getTypeMsgDefault());
            },
            success: function (responseData, textStatus, jqXHR) {
                if (responseData.code === 0) {
                    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
                    fillDataTableReportConsolidated(responseData.data, idHeadTable, idBodyTable, idFooterTable);
                } else {
                    showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
                disableHtmlObject(idBtnFind, false);
            }
        });
    }
}

function validateFields() {
    let message = "";
    showMessageWithImage(idDivMessage, message, getTypeMsgDefault());
    var dateFrom = $('#' + idTxtDateFrom).val();
    var dateTo = $('#' + idTxtDateTo).val();

    if (validateDateInicioMayorqueTermino(dateFrom, dateTo)) {
        message = "La fecha de inicio debe ser menor o igual a la fecha de termino.";
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    if (validateDateLessYerterday(dateFrom)) {
        message = "La fecha de termino debe ser menor o igual al dia de ayer.";
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    if (validateDateLessYerterday(dateTo)) {
        message = "La fecha de termino debe ser menor o igual al dia de ayer.";
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    return true;
}

function fillJsonData() {
    var idSoporte = $('#' + idCboSoporte).val();
    if (idSoporte === null) {
        idSoporte = 0;
    }

    var idCompany = $('#' + idCboCompany).val();
    if (idCompany === null) {
        idCompany = 0;
    }


    var idAccount = $('#' + idCboUser).val();
    if (idAccount === null) {
        idAccount = 0;
    }

    var idInputMode = $('#' + idCboInputMode).val();
    if (idInputMode === null) {
        idInputMode = 0;
    }

    var idCampaign = $('#' + idTxtCampaign).val();
    if (idCampaign === null) {
        idCampaign = '';
    }

    var dateFrom = $('#' + idTxtDateFrom).val();
    if (dateFrom === null) {
        dateFrom = '';
    } else {
        dateFrom += ' 00:00:00';
    }

    var dateTo = $('#' + idTxtDateTo).val();
    if (dateTo === null) {
        dateTo = '';
    } else {
        dateTo += ' 23:59:59';
    }

    var json = {
        'page': 0,
        'size': 10,
        'filters': {
            'idAccount': idAccount,
            'idCompany': idCompany,
            'idInputMode': idInputMode,
            'dateFrom': dateFrom,
            'dateTo': dateTo,
            'idSoporte': idSoporte,
            'idCampaign': idCampaign
        }
    };
    return json;
}

