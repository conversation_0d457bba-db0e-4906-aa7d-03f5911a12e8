const idHddCompanyId = "hddCompanyId";
const idHddAccountId = "hddAccountId";
const idDivMessage = 'divMessage';
const idImgLoading = 'imgLoading';
const idHddReLoadTable = "hddReLoadTable";

const idTxtDateFromFilter = 'txtDateFrom-filter';
const idBtnDateFromFilter = 'btnDateFrom-filter';
const idTxtDateToFilter = 'txtDateTo-filter';
const idBtnDateToFilter = 'btnDateTo-filter';
const idCboCompanyFilter = 'cboCompany-filter';
const idCboAccountFilter = 'cboAccount-filter';
const idTxtDataFilter = 'txtData-filter';
const idCboStatusFilter = "cboStatus-filter";
const idBtnSearchFilter = 'btnSearch-filter';


const idDivTable = 'divTable';
const idHddPage = 'hddPage';
const idBtnBackPage = 'btnBackPage';
const idDivPages = 'divPages';
const idBtnNextPage = 'btnNextPage';
const idCboRowsPage = 'cboRowsPage';

const idHeadTable = 'headTable';
const idBodyTable = 'bodyTable';

//const typeMsgAlert = 1;
//const typeMsgSuccess = 2;
//const typeMsgError = 3;
//const typeMsgDefault = 4;
const timerValue = 10000;
const idOffset = "offset";

let loadData = 0;
let idTimeout = 0;
let test = false;

$(document).ready(function () {
    initLoad();
    eventClick();
    eventChange();
});

function initLoad() {
    viewHtmlObject(idImgLoading, false);
    $('#' + idCboRowsPage).val(10);

    let date = new Date();
    let day = addDigitLeft(date.getDate(), 2, '0');
    let month = addDigitLeft(date.getMonth() + 1, 2, '0');
    let year = date.getFullYear();
    let txtDateNow = year + '-' + month + '-' + day;
    $('#' + idTxtDateFromFilter).val(txtDateNow);
    $('#' + idTxtDateToFilter).val(txtDateNow);

    var promiseLoadCompany = getDataSelectCompany();

    configBtnDates(idTxtDateFromFilter, idBtnDateFromFilter);
    configBtnDates(idTxtDateToFilter, idBtnDateToFilter);

    promiseLoadCompany.done(function (responseData) {
        if (responseData.code === 0) {
            var id = $('#' + idHddCompanyId).val();
            loadCombobox(idCboCompanyFilter, responseData.data, id, 'Seleccione...');
            loadSelectUser();
        } else {
            showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
        }
    });
    loadSelectStatus();

//    promiseLoadCompany.fail(function () {
//        showMessageWithImage(idDivMessage, "error al cargar las compañias", typeMsgError);
//    });
//    promiseLoadCompany.always(function () {
//        viewHtmlObject(idImgLoading, true);
//    });

//    loadData = 1;
//    loadTable();
}

function eventChange() {
    $('#' + idCboCompanyFilter).change(function () {
        loadSelectUser();
    });
    $('#' + idCboRowsPage).change(function () {
        $('#' + idHddPage).val(1);
        loadData = 1;
        loadTable();
    });
}

function eventClick() {
    $('#btnTest').click(function () {
        if (test) {
            viewHtmlObject(idImgLoading, true);
            test = false;
        } else {
            viewHtmlObject(idImgLoading, false);
            test = true;
        }
    });
    $('#' + idBtnSearchFilter).click(function () {
        $('#' + idHddPage).val(1);
        loadData = 1;
        loadTable();
    });
    $('#' + idBtnBackPage).click(function () {
        var page = $('#' + idHddPage).val();
        page--;
        $('#' + idHddPage).val(page);
        loadData = 1;
        loadTable();
    });
    $('#' + idBtnNextPage).click(function () {
        var page = $('#' + idHddPage).val();
        page++;
        $('#' + idHddPage).val(page);
        loadData = 1;
        loadTable();
    });

}

function getDataSelectCompany() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    return $.ajax({
        url: config.urlServiceGeneral + "/api/list/company",
        type: 'GET',
        contentType: "application/json;charset=UTF-8",
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(idImgLoading, true);
        },
        error: function () {
            showMessageWithImage(idDivMessage, "error al cargar las compañias", getTypeMsgError());
        },
        complete: function () {
            viewHtmlObject(idImgLoading, false);
        }
    });
}

function loadSelectUser() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    var idCompany = $('#' + idCboCompanyFilter).val();
    return $.ajax({
        url: config.urlServiceAccount + "/api/user/list/" + idCompany,
        type: 'GET',
        contentType: "application/json;charset=UTF-8",
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(idImgLoading, true);
        },
        success: function (responseData) {
            if (responseData.code === 0) {
                var id = $('#' + idHddAccountId).val();
                let dataCombo = parseDataUser(responseData.data);
                loadCombobox(idCboAccountFilter, dataCombo, id, 'Todos');
            } else {
                showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
            }
        },
        error: function () {
            showMessageWithImage(idDivMessage, "error al cargar los usuarios", getTypeMsgError());
        },
        complete: function () {
            viewHtmlObject(idImgLoading, false);
        }
    });
}

function loadSelectStatus() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    var idCompany = $('#' + idCboCompanyFilter).val();
    return $.ajax({
        url: config.urlServiceGeneral + "/api/list/status/queueReport",
        type: 'GET',
        contentType: "application/json;charset=UTF-8",
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(idImgLoading, true);
        },
        success: function (responseData) {
            if (responseData.code === 0) {
                loadCombobox(idCboStatusFilter, responseData.data, 0, 'Todos');
            } else {
                showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
            }
        },
        error: function () {
            showMessageWithImage(idDivMessage, "error al cargar los usuarios", getTypeMsgError());
        },
        complete: function () {
            viewHtmlObject(idImgLoading, false);
        }
    });
}

function getFilterData() {
    var dateFrom = $('#' + idTxtDateFromFilter).val();
    var dateTo = $('#' + idTxtDateToFilter).val();
    var companyId = $('#' + idCboCompanyFilter).val();
    var accountId = $('#' + idCboAccountFilter).val();
    var textData = $('#' + idTxtDataFilter).val();
    var statusId = $('#' + idCboStatusFilter).val();
    var page = $('#' + idHddPage).val();
    var rows = $('#' + idCboRowsPage).val();

    if (companyId === null)
        companyId = 0;
    if (accountId === null)
        accountId = 0;
    if (statusId === null)
        statusId = 0;
    if (page === null)
        page = 1;
    //esto es para ver la data sin el +1 agregado al crear los botones de paginacion
    
    if (dateFrom === null) {
        dateFrom = '';
    } else {
        dateFrom += ' 00:00:00';
    }
    
    if (dateTo === null) {
        dateTo = '';
    } else {
        dateTo += ' 23:59:59';
    }
    
    page--;
    var json = {
        "page": page,
        "size": rows,
        "filters": {
            "dateFrom": dateFrom ,
            "dateTo": dateTo ,
            "idAccount": accountId,
            "idCompany": companyId,
            "idStatus": statusId
        }
    }
    return json;
}

function getDataTable() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    var jsonData = getFilterData();
    return $.ajax({
        url: config.urlServiceReport + "/api/queueReport/list",
        type: 'POST',
        contentType: "application/json;charset=UTF-8",
        data: JSON.stringify(jsonData),
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(idImgLoading, true);
        }, error: function () {
            showMessageWithImage(idDivMessage, 'Error al solicitar los datos del reporte', getTypeMsgError());
        }, complete: function () {
            viewHtmlObject(idImgLoading, false);
        }
    });
}

function loadTable() {
    // esto es para eliminar los timer que esten corriendo y evitar 2 cargas
    clearTimeout(idTimeout);

    var promisedataTable = getDataTable();

    promisedataTable.done(function (resultData) {
        if (resultData.code === 0) {
            sessionStorage.setItem("totalRows", JSON.stringify(resultData.countRows));
            sessionStorage.setItem("newDataQueueReport", JSON.stringify(resultData.data));
            createHeaderTable(resultData.data);
            createSheetButton(resultData.countRows, idCboRowsPage, idHddPage, idDivPages, idBtnBackPage, idBtnNextPage);
            addListData();
            loadData = 0;
            //se deja un nuevo timer que ira recargando la vista cada x tiempo
            idTimeout = setTimeout(loadTable, timerValue);
        } else {
            showMessageWithImage(idDivMessage, resultData.message, getTypeMsgError());
        }
    });
}

function createHeaderTable(data){
    
    $('#'+idHeadTable).html('');
    let th = $('<th/>');
    let tr = $('<tr/>');
    
    th = $('<th/>');
    th.html('CODE');
    th.css('min-width','100px');
    th.css('max-width','100px');
    tr.append(th);
    
    th = $('<th/>');
    th.html('USUARIO');
    th.css('min-width','150px');
    th.css('max-width','150px');
    tr.append(th);
    
    th = $('<th/>');
    th.html('FECHA');
    th.css('min-width','200px');
    th.css('max-width','200px');
    tr.append(th);
    
    th = $('<th/>');
    th.html('ESTADO');
    th.css('min-width','100px');
    th.css('max-width','100px');
    tr.append(th);
    
    th = $('<th/>');
    th.html('PROGRESO');
    th.css('min-width','150px');
    th.css('max-width','150px');
    tr.append(th);
    
    th = $('<th/>');
    th.html('ACCION');
    th.css('min-width','100px');
    th.css('max-width','100px');
    tr.append(th);
    
    $('#'+idHeadTable).append(tr);
}

function addListData() {
    var newDataText = sessionStorage.getItem('newDataQueueReport');
    var newData = JSON.parse(newDataText);

    var oldDataText = sessionStorage.getItem('oldDataQueueReport');
    var oldData = JSON.parse(oldDataText);

    var maxRows = $('#' + idCboRowsPage).val();
    maxRows = parseInt(maxRows);
    var rows = 0;
    let oldIndex = 0;
    if (newData !== null) {
        if (loadData === 1) {
            $('#' + idBodyTable).html('');
        }
        for (var indexRow in newData) {
            var rowData = newData[indexRow];
            if (loadData === 1) {
                createRowData(rowData, indexRow, oldIndex);
            } else {
                var oldRowData = oldData[indexRow];
                if (oldRowData !== null) {
                    updateRowData(rowData, oldRowData, indexRow);
                } else {
                    if (rows < maxRows) {
                        createRowData(rowData, indexRow, oldIndex);
                    }
                }

            }
            rows++;
            oldIndex=indexRow;
        }
        sessionStorage.setItem("oldDataQueueReport", newDataText);
    }
}
function updateRowData(newRowData, oldRowData, indexRow) {
    if (
            oldRowData === null ||
            oldRowData.status === 'INITIAL' ||
            oldRowData.status === 'QUEUED' ||
            oldRowData.status === 'PROCESSING'
            ) {
        console.log("actualizando tabla " + indexRow);
        var tdStatus = $('#tdStatus_' + indexRow);
        tdStatus.html(newRowData.status);
        var tdProgress = $('#tdProgress_' + indexRow);
        tdProgress.html(newRowData.process + '/' + newRowData.total);
        var tdAction = $('#tdAction_' + indexRow);
        tdAction = putActionButton(tdAction, newRowData);
    }
}

function putActionButton(tdAction, rowData) {
    tdAction.html('');

    var divCancel = $('<div/>', {'class': 'containerbtnImg33'});
    if (rowData.status === 'INICIAL' ||
            rowData.status === 'ENCOLADO' ||
            rowData.status === 'PROCESANDO'
            ) {
        var imgCancel = $('<img/>', {'src': 'images/icons/cancel.png'});
        imgCancel.attr('onClick', 'cancelReport(' + rowData.id + ');');
        imgCancel.attr('class', 'btnImg');
        imgCancel.attr('title', 'Cancelar');
        divCancel.append(imgCancel);
    } else {
        divCancel.html('&nbsp;');
    }
    tdAction.append(divCancel);

    var divDowload = $('<div/>', {'class': 'containerbtnImg33'});
    if (rowData.status === 'PROCESADO' ||
            rowData.status === 'DESCARGADO'
            ) {
        var imgDowload = $('<img/>', {'src': 'images/icons/download.png'});
        imgDowload.attr('onClick', 'dowloadReport(' + rowData.id + ');');
        imgDowload.attr('class', 'btnImg');
        imgDowload.attr('title', 'Descargar');
        divDowload.append(imgDowload);
    } else {
        divDowload.html('&nbsp;');
    }

    tdAction.append(divDowload);
    return tdAction;
}

function createRowData(rowData, indexRow, oldIndex) {
    console.log("creando tabla " + indexRow);
    let tr = $('<tr/>');
    let td = $('<td/>');
    tr.prop('id', 'tr_' + rowData.id);

    if (indexRow % 2 === 0) {
        tr.addClass('trPar');
    } else {
        tr.addClass('trInpar');
    }

    td = $('<td/>').html(rowData.id);
    td.prop('id', 'tdCode_' + rowData.id);
    td.css('min-width', '100px');
    td.css('max-width', '100px');
    tr.append(td);

    td = $('<td/>').html(rowData.nameAccount);
    td.prop('id', 'tdNameAccount_' + rowData.id);
    td.css('min-width', '150px');
    td.css('max-width', '150px');
    tr.append(td);

    td = $('<td/>').html(rowData.createdDate);
    td.prop('id', 'tdCreatedDate_' + rowData.id);
    td.css('min-width', '200px');
    td.css('max-width', '200px');
    tr.append(td);

    td = $('<td/>').html(rowData.status);
    td.prop('id', 'tdStatus_' + rowData.id);
    td.css('min-width', '100px');
    td.css('max-width', '100px');
    tr.append(td);

    td = $('<td/>').html(rowData.process + '/' + rowData.total);
    td.prop('id', 'tdProgress_' + rowData.id);
    td.css('min-width', '150px');
    td.css('max-width', '150px');
    tr.append(td);

    td = $('<td/>');
    td.prop('id', 'tdAction_' + rowData.id);
    td.css('min-width', '100px');
    td.css('max-width', '100px');
    td = putActionButton(td, rowData);
    tr.append(td);
    
    if(indexRow > oldIndex) {
        $('#' + idBodyTable).prepend(tr);
    }else{
        $('#' + idBodyTable).append(tr);
    }
    
}

function dowloadReport(id) {
    var config = JSON.parse(sessionStorage.getItem('config'));
    window.location.href = config.urlServiceReport +"/api/download/file/"+id;
}

function cancelReport(id) {
    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
    if (confirm("¿Está Seguro de cancelar el reporte?")) {
        var token = sessionStorage.getItem("token");
        var config = JSON.parse(sessionStorage.getItem('config'));
        let jsonData = {
            "id": id,
            "status": "CANCELED"
        };
        $.ajax({
            url: config.urlServiceReport + "/api/queueReport/update/status",
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(jsonData),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
            },

            success: function (resultData, textStatus, jqXHR) {
                if (resultData.code === 0) {
                    showMessageWithImage(idDivMessage, resultData.message, getTypeMsgSuccess());
                    loadTable();
                } else {
                    showMessageWithImage(idDivMessage, resultData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "error al enviar la cancelacion del registro.", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
            }
        });
    }
}

function loadPage(page) {
    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
    $('#' + idHddPage).val(page);
    loadData = 1;
    loadTable();
}

