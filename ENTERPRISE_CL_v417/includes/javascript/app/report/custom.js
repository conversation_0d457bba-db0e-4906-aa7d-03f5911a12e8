const idCboStaus = "cboStaus";
const idCboInputMode = "cboInputMode";
const idCboCompany = "cboCompany";
const idHeadTable = "headTable";
const idBodyTable = "bodyTable";
const idFooterTable = "footerTable";
const idTxtnumberPhone = "txtnumberPhone";
const idTxtDateFrom = "txtDateFrom";
const idTxtDateTo = "txtDateTo";
const idBtnDateFrom = "btnDateFrom";
const idBtnDateTo = "btnDateTo";
const idCboUser = "cboUser";
const idTxtCodeclient = 'txtCodeclient';
const idTxtCampaign = 'campaignId';

const idBtnFind = "btnFind";
const idBtnClean = "btnClean";
const idBtnGeneratePageCsv = "btnGeneratePageCsv";
const idBtnGenerateAllCsv = "btnGenerateAllCsv";
const idTxtParam = "txtParam";

const idHddActualPage = "hddActualPage";
const idBtnBackPage = "btnPageAtras";
const idDivPages = "btnLinkPages";
const idBtnNextPage = "btnPageSiguiente";
const idCboRowsPage = "cboRowsPage";
const idBodyCustomParameter = "bodyCustomParameter";
const idHddIdAccount = "hddIdAccount";
const idHddIdCompany = "hddIdCompany";  // ID para consultar report personalizado
const idDivMessage = "divMessage";
const idOffset = "offset";

idReport = 100;

const idImgLoading = "imgLoading";

$(document).ready(function () {
    initLoad();
    controlEvent();
});

function initLoad() {
    var textDate = generateYesterdayDate();
    viewHtmlObject(idImgLoading, false);
    $('#' + idHddActualPage).val(1);
    $('#' + idCboRowsPage).val(10);
    $('#' + idTxtDateFrom).val(textDate);
    $('#' + idTxtDateTo).val(textDate);

    loadCbo(idCboInputMode, idImgLoading, idDivMessage, "api/list/inputMode");
    loadCbo(idCboStaus, idImgLoading, idDivMessage, "api/list/status/report");

    configBtnDates(idTxtDateFrom, idBtnDateFrom);
    configBtnDates(idTxtDateTo, idBtnDateTo);

    let promiseLoadCompany = loadCboPromise(idImgLoading, idDivMessage, "api/list/company");
    promiseLoadCompany.done(function (responseData) {
        if (responseData.code === 0) {
            var id = $('#' + idHddIdCompany).val();
            loadCombobox(idCboCompany, responseData.data, id, 'Seleccione...');
            loadDataCustomParameter();
            let idUser = $('#' + idHddIdAccount).val();
            let idCompany = $('#' + idCboCompany).val();
            loadCboUser(idCboUser, idUser, idCompany, idDivMessage, idImgLoading);
        } else {
            showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
        }
    });
}

function controlEvent() {
    clickEvents();
    changeEvents();
    keyEvents();
}


function clickEvents() {
    $('#' + idBtnFind).click(function () {
        $('#' + idHddActualPage).val(1);
        loadTableReport();
    });

    $('#' + idBtnGeneratePageCsv).click(function () {
        generateCsvReportDetail("page", idBtnGeneratePageCsv);
    });

    $('#' + idBtnGenerateAllCsv).click(function () {
        generateCsvReportDetail("full", idBtnGenerateAllCsv);
    });

    $('#' + idBtnBackPage).click(function () {
        var cant = $('#' + idHddActualPage).val();
        cant--;
        $('#' + idHddActualPage).val(cant);
        loadTableReport();
    });

    $('#' + idBtnNextPage).click(function () {
        var cant = $('#' + idHddActualPage).val();
        cant++;
        $('#' + idHddActualPage).val(cant);
        loadTableReport();
    });
}

function changeEvents() {
    $('#' + idCboRowsPage).change(function () {
        loadTableReport();
    });
    $('#' + idCboCompany).change(function () {
        let idUser = $('#' + idHddIdAccount).val();
        let idCompany = $('#' + idCboCompany).val();
        loadCboUser(idCboUser, idUser, idCompany, idDivMessage, idImgLoading);
        loadDataCustomParameter();
    });
}

function keyEvents() {
    $('#' + idTxtCampaign).keypress(function (e) {
        keyOnlyNumbers(e);
    });
}

function loadPage(page) {
    $('#' + idHddActualPage).val(page);
    loadTableReport();
}


function generateCsvReportDetail(type, idBtnGenerate) {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    if (validateFields()) {
        var json = fillJsonData(type);
        $.ajax({
            url: config.urlServiceReport + "/api/mt/custom/detail/queue/csv/" + type,
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(json),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
                disableHtmlObject(idBtnGenerate, true);
                showMessageWithImage(idDivMessage, "Generando...", getTypeMsgDefault());
            },
            success: function (responseData, textStatus, jqXHR) {
                if (responseData.code === 0) {
                    let message = responseData.message + ' con el codigo de seguimiento ' + responseData.data;
                    showMessageWithImage(idDivMessage, message, getTypeMsgSuccess());
                } else {
                    showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
                disableHtmlObject(idBtnGenerate, false);
            }
        });
    }
}

//function dowloadReport(code) {
//    var token = sessionStorage.getItem("token");
//    var config = JSON.parse(sessionStorage.getItem('config'));
//    var promise = $.ajax({
//        url: config.urlServiceReport + "/api/mt/custom/download/" + code,
//        type: 'GET',
//        headers: {
//            "Authorization": token
//        },
//        beforeSend: function (xhr) {
//            viewHtmlObject(idImgLoading, false);
//        },
//        success: function (result, textStatus, jqXHR) {
//            window.location(result.data);
////			var hiddenElement = document.createElement('a');
////			hiddenElement.href = 'data:text/csv;charset=utf-8,' + encodeURI(data);
////			hiddenElement.target = '_blank';
////			var d = new Date();
////			var tdate = d.getTime();
////			hiddenElement.download = 'rpt_detalle_personalizado_'+tdate+'.csv';
////			hiddenElement.style.display = "none";
////			document.body.appendChild(hiddenElement);
////			hiddenElement.click();
//        },
//        error: function (jqXHR, textStatus, errorThrown) {
//            showMessageWithImage(idDivMessage, "Error al enviar la peticion", typeMsgError);
//        },
//        complete: function (jqXHR, textStatus) {
//            viewHtmlObject(idImgLoading, false);
//        }
//    });
//}



function loadDataCustomParameter() {
    var idCompany = $('#' + idCboCompany).val();
    if (idCompany === null)
        idCompany = 0;
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    $.ajax({
        url: config.urlServiceReport + "/api/mt/custom/list/field/" + idCompany,
        type: 'GET',
        contentType: "application/json;charset=UTF-8",
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(idImgLoading, true);
        },
        success: function (responseData, textStatus, jqXHR) {
            if (responseData.code === 0) {
                idReport = responseData.data.code;
                fillFieldsCustomParameter(responseData.data.listData);
                showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
            } else {
                showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());

        },
        complete: function () {
            viewHtmlObject(idImgLoading, false);
        }
    });
}

function fillFieldsCustomParameter(data) {
    $('#' + idBodyCustomParameter).html('');
    if (data !== null) {
        for (var index in data) {
            var element = data[index];
            var tr = $('<tr/>');
            var hdd = $('<input/>', {'id': 'param-' + index + '-code', 'type': 'hidden'}).val(element.code);
            var text = $('<input/>', {'id': 'param-' + index + '-value', 'type': 'text'}).val('');
            var tdlabel = $('<td/>', {'class': 'td20'}).html(element.label);
            var tdinput = $('<td/>', {'class': 'td30'}).append(hdd).append(text);
            var tdvacio = $('<td/>', {'class': 'td50'});
            tr.append(tdlabel);
            tr.append(tdinput);
            tr.append(tdvacio);
            $('#' + idBodyCustomParameter).append(tr);
        }
    }
}

function loadTableReport() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    if (validateFields()) {
        var json = fillJsonData();
        $.ajax({
            url: config.urlServiceReport + "/api/mt/custom/detail/data",
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(json),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
                disableHtmlObject(idBtnFind, true);
                showMessageWithImage(idDivMessage, "Generando...", getTypeMsgDefault());
            },
            success: function (responseData, textStatus, jqXHR) {
                if (responseData.code === 0) {
                    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
                    createSheetButton(responseData.countRows, idCboRowsPage, idHddActualPage, idDivPages, idBtnBackPage, idBtnNextPage);
                    fillDataTableReportDetail(responseData.data, idHeadTable, idBodyTable, idFooterTable);
                } else {
                    showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
                disableHtmlObject(idBtnFind, false);
            }
        });
    }
}

function validateFields() {
    let message = '';
    showMessageWithImage(idDivMessage, message, getTypeMsgDefault());
    let idCompany = $('#' + idCboCompany).val();
    let dateFrom = $('#' + idTxtDateFrom).val();
    let dateTo = $('#' + idTxtDateTo).val();

    if (commonValidateEndDateGreaterThanStartDate(dateFrom, dateTo) === false) {
        message = 'La fecha de inicio debe ser menor o igual a la fecha de termino.';
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    if (commonValidateDateLessYesterday(dateFrom) === false) {
        message = 'La fecha de inicio debe ser menor o igual al dia de ayer.';
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    if (commonValidateDateLessYesterday(dateTo) === false) {
        message = 'La fecha de termino debe ser menor o igual al dia de ayer.';
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    if (idCompany === null || parseInt(idCompany) === 0) {
        message = 'Debe seleccionar una compañia';
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    return true;
}

function fillJsonData(type) {
    var idCompany = $('#' + idCboCompany).val();
    if (idCompany === null) {
        idCompany = 0;
    }

    var idAccount = $('#' + idCboUser).val();
    if (idAccount === null) {
        idAccount = 0;
    }

    var idInputMode = $('#' + idCboInputMode).val();
    if (idInputMode === null) {
        idInputMode = 0;
    }

    var idStatus = $('#' + idCboStaus).val();
    if (idStatus === null) {
        idStatus = 0;
    }

    var clientCode = $('#' + idTxtCodeclient).val();
    if (clientCode === null) {
        clientCode = '';
    }

    var idCampaign = $('#' + idTxtCampaign).val();
    if (idCampaign === null) {
        idCampaign = '';
    }

    var dateFrom = $('#' + idTxtDateFrom).val();
    if (dateFrom === null) {
        dateFrom = '';
    } else {
        dateFrom += ' 00:00:00';
    }

    var dateTo = $('#' + idTxtDateTo).val();
    if (dateTo === null) {
        dateTo = '';
    } else {
        dateTo += ' 23:59:59';
    }

    var mobileNumber = $('#' + idTxtnumberPhone).val();
    if (mobileNumber === null) {
        mobileNumber = '';
    }

    var param1 = $('#' + idTxtParam + 1).val();
    if (param1 === null) {
        param1 = '';
    }

    var param2 = $('#' + idTxtParam + 2).val();
    if (param2 === null) {
        param2 = '';
    }

    var actPage = $('#' + idHddActualPage).val();
    if (actPage === null) {
        actPage = 0;
    }

    if (actPage > 0) {
        actPage--;
    }

    var size = $('#' + idCboRowsPage).val();
    if (size === null) {
        size = 0;
    }

    var listCustomParam = [];
    let param;
    for (var index = 0; index < 3; index++) {
        var code = $('#param-' + index + '-code').val();
        var value = $('#param-' + index + '-value').val();
        param = {
            "code": code,
            "value": value
        };
        listCustomParam.push(param);
    }

    var json = {
        'idReport': idReport,
        'page': actPage,
        'size': size,
        'filters': {
            'numberMobil': mobileNumber,
            'idAccount': idAccount,
            'idCompany': idCompany,
            'idStatus': idStatus,
            'idInputMode': idInputMode,
            'dateFrom': dateFrom,
            'dateTo': dateTo,
            'clientCode': clientCode,
            'idCampaign': idCampaign
        },
        'list': listCustomParam
    };
    return json;
}


