const idCboStaus = "cboStaus";
const idCboInputMode = "cboInputMode";
const idCboCompany = "cboCompany";
const idHeadTable = "headTable";
const idBodyTable = "bodyTable";
const idFooterTable = "footerTable";
const idTxtnumberPhone = "txtnumberPhone";
const idTxtDateFrom = "txtDateFrom";
const idTxtDateTo = "txtDateTo";
const idBtnDateFrom = "btnDateFrom";
const idBtnDateTo = "btnDateTo";
const idCboUser = "cboUser";
const idTxtCodeclient = 'txtCodeclient';
const idTxtCampaign = 'campaignId';

const idBtnFind = "btnFind";
const idBtnClean = "btnClean";
const idBtnGeneratePageCsv = "btnGeneratePageCsv";
const idBtnGenerateAllCsv = "btnGenerateAllCsv";

const idHddActualPage = "hddActualPage";
const idBtnBackPage = "btnPageAtras";
const idDivPages = "btnLinkPages";
const idBtnNextPage = "btnPageSiguiente";
const idCboRowsPage = "cboRowsPage";
const idHddIdAccount = "hddIdAccount";
const idHddIdCompany = "hddIdCompany";
const idDivMessage = "divMessage";
const idOffset = "offset";

const idImgLoading = "imgLoading";
const idTxtIdProfile = "txtIdProfile";
const idCboSoporte = "cboSoporte";

$(document).ready(function () {
    initLoad();
    controlEvent();
});

function initLoad() {
    var tfecha = generateYesterdayDate();
    viewHtmlObject(idImgLoading, false);
    $('#' + idHddActualPage).val(1);
    $('#' + idCboRowsPage).val(10);
    $('#' + idTxtDateFrom).val(tfecha);
    $('#' + idTxtDateTo).val(tfecha);

    var profile = $('#' + idTxtIdProfile).val();
    if (profile === '1' || profile === '5') {
        $('.tdSoporte').show();
    } else {
        $('.tdSoporte').hide();
    }

    loadCbo(idCboInputMode, idImgLoading, idDivMessage, "api/list/inputMode");
    loadCbo(idCboStaus, idImgLoading, idDivMessage, "api/list/status/report");
    
    configBtnDates(idTxtDateFrom, idBtnDateFrom);
    configBtnDates(idTxtDateTo, idBtnDateTo);

    let promiseLoadCompany = loadCboPromise(idImgLoading, idDivMessage, "api/list/company");
    promiseLoadCompany.done(function (responseData) {
        if (responseData.code === 0) {
            var idCompany = $('#' + idHddIdCompany).val();
            loadCombobox(idCboCompany, responseData.data, idCompany, 'Seleccione...');
            let idUser = $('#' + idHddIdAccount).val();
//            let idCompany = $('#' + idCboCompany).val();
            loadCboUser(idCboUser, idUser, idCompany, idDivMessage, idImgLoading);
        } else {
            showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
        }
    });
}

function controlEvent() {
    clickEvents();
    changeEvents();
    keyEvents();
}

function clickEvents() {
    $('#' + idBtnFind).click(function () {
        $('#' + idHddActualPage).val(1);
        loadTableReport();
    });

    $('#' + idBtnGeneratePageCsv).click(function () {
        var page = $('#' + idHddActualPage).val();
        generateCsvReportDetail('page', idBtnGeneratePageCsv);
    });

    $('#' + idBtnGenerateAllCsv).click(function () {
        generateCsvReportDetail('full', idBtnGenerateAllCsv);
    });

    $('#' + idBtnBackPage).click(function () {
        var cant = $('#' + idHddActualPage).val();
        cant--;
        $('#' + idHddActualPage).val(cant);
        loadTableReport();
    });

    $('#' + idBtnNextPage).click(function () {
        var cant = $('#' + idHddActualPage).val();
        cant++;
        $('#' + idHddActualPage).val(cant);
        loadTableReport();
    });
}

function changeEvents() {
    $('#' + idCboRowsPage).change(function () {
        loadTableReport();
    });
    $('#' + idCboCompany).change(function () {
        loadCboUser();
        let idUser = $('#' + idHddIdAccount).val();
        let idCompany = $('#' + idCboCompany).val();
        loadCboUser(idCboUser, idUser, idCompany, idDivMessage, idImgLoading);
    });
}

function keyEvents() {
    $('#' + idTxtCampaign).keypress(function (e) {
        keyOnlyNumbers(e);
    });
}

function loadPage(page) {
    $('#' + idHddActualPage).val(page);
    loadTableReport();
}

function generateCsvReportDetail(type, idBtnGenerate) {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    if (validateFields()) {
        var json = fillJsonData();
        $.ajax({
            url: config.urlServiceReport + "/api/mt/billing/detail/queue/csv/" + type,
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(json),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
                disableHtmlObject(idBtnGenerate, true);
                showMessageWithImage(idDivMessage, "Generando...", getTypeMsgDefault());
            },
            success: function (responseData, textStatus, jqXHR) {
                if (responseData.code === 0) {
                    let message = responseData.message + ' con el codigo de seguimiento ' + responseData.data;
                    showMessageWithImage(idDivMessage, message, getTypeMsgSuccess());
                } else {
                    showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
                disableHtmlObject(idBtnGenerate, false);
            }
        });
    }
}

function loadTableReport() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    if (validateFields()) {
        var json = fillJsonData();
        $.ajax({
            url: config.urlServiceReport + "/api/mt/billing/detail/data",
            type: 'POST',
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify(json),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                viewHtmlObject(idImgLoading, true);
                disableHtmlObject(idBtnFind, true);
                showMessageWithImage(idDivMessage, "Generando...", getTypeMsgDefault());
            },
            success: function (responseData, textStatus, jqXHR) {
                if (responseData.code === 0) {
                    createSheetButton(responseData.countRows, idCboRowsPage, idHddActualPage, idDivPages, idBtnBackPage, idBtnNextPage);
                    fillDataTableReportDetail(responseData.data, idHeadTable, idBodyTable, idFooterTable);
                    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
                } else {
                    showMessageWithImage(idDivMessage, responseData.message, getTypeMsgError());
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
            },
            complete: function (jqXHR, textStatus) {
                viewHtmlObject(idImgLoading, false);
                disableHtmlObject(idBtnFind, false);
            }
        });
    }
}

function validateFields() {
    let message = '';
    showMessageWithImage(idDivMessage, "", getTypeMsgDefault());
    var dateFrom = $('#' + idTxtDateFrom).val();
    var dateTo = $('#' + idTxtDateTo).val();

    if (commonValidateEndDateGreaterThanStartDate(dateFrom, dateTo) === false) {
        message = 'La fecha de inicio debe ser menor o igual a la fecha de termino.';
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    if (commonValidateDateLessYesterday(dateFrom) === false) {
        message = 'La fecha de termino debe ser menor o igual al dia de ayer.';
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    if (commonValidateDateLessYesterday(dateTo) === false) {
        message = 'La fecha de termino debe ser menor o igual al dia de ayer.';
        showMessageWithImage(idDivMessage, message, getTypeMsgError());
        return false;
    }
    return true;
}

function fillJsonData() {
    var idSoporte = $('#' + idCboSoporte).val();
    if (idSoporte === null) {
        idSoporte = 0;
    }
    
    var idCompany = $('#' + idCboCompany).val();
    if (idCompany === null) {
        idCompany = 0;
    }

    var idAccount = $('#' + idCboUser).val();
    if (idAccount === null) {
        idAccount = 0;
    }

    var idInputMode = $('#' + idCboInputMode).val();
    if (idInputMode === null) {
        idInputMode = 0;
    }

    var idStatus = $('#' + idCboStaus).val();
    if (idStatus === null) {
        idStatus = 0;
    }

    var clientCode = $('#' + idTxtCodeclient).val();
    if (clientCode === null) {
        clientCode = '';
    }
    var idCampaign = $('#' + idTxtCampaign).val();
    if (idCampaign === null) {
        idCampaign = '';
    }
    var dateFrom = $('#' + idTxtDateFrom).val();
    if (dateFrom === null) {
        dateFrom = '';
    } else {
        dateFrom += ' 00:00:00';
    }

    var dateTo = $('#' + idTxtDateTo).val();
    if (dateTo === null) {
        dateTo = '';
    } else {
        dateTo += ' 23:59:59';
    }

    var mobilNumber = $('#' + idTxtnumberPhone).val();
    if (mobilNumber === null) {
        mobilNumber = '';
    }

    var actPage = $('#' + idHddActualPage).val();
    if (actPage === null) {
        actPage = 0;
    }

    if (actPage > 0) {
        actPage--;
    }

    var size = $('#' + idCboRowsPage).val();
    if (size === null) {
        size = 0;
    }

    var json = {
        'page': actPage,
        'size': size,
        'filters': {
            'numberMobil': mobilNumber,
            'idAccount': idAccount,
            'idCompany': idCompany,
            'idStatus': idStatus,
            'idInputMode': idInputMode,
            'dateFrom': dateFrom,
            'dateTo': dateTo,
            'clientCode': clientCode,
            'idCampaign': idCampaign, 
            'idSoporte': idSoporte
        }
    };
    return json;
}
