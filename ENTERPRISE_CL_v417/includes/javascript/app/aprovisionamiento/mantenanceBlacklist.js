const idCboRowsPage = 'cboRowsPage-tableBlacklist';
const idHddPage = 'hddPage-tableBlacklist';
//const idRexPais='rexPais';
//const idPlaceHolderFormatMobilNumber = 'placeHolderFormatMobilNumber';
const idDivImgLoading = 'divImgLoading-blacklist';
const idDivError = 'divError-tableBlacklist';
const idDivMessage = 'divMessage-blacklist';
const idDivTable = 'div-tableBlacklist';
const idDivBodyTable='bodyTable-tableBlacklist';
const idBtnAtras = "btnAtras-tableBlacklist";
const idBtnSiguiente = "btnSiguiente-tableBlacklist";
const idDivPages = 'divPages-tableBlacklist';

const idCboCompanyCreate = "cboCompany-create";
const idTxtNumeroCreate = "txtNumero-create";
const idTxtMotivoCreate = "txtMotivo-create";
const idBtnCleanCreate = 'btnClean-create';
const idBtnCancelCreate = 'btnCancel-create';
const idBtnSaveCreate = 'btnSave-create';
const idDivCreate = 'div-create';

const idCboCompanyFilter = 'cboCompany-filterBlacklist';
const idBtnBuscarFilter = 'btnBuscar-filterBlacklist';
const idBtnNuevoFilter = 'btnNuevo-filterBlacklist';
const idTxtDataFilter = "txtData-filterBlacklist";


$(document).ready(function () {
    $('#'+idDivCreate).hide();
    $('#'+idDivImgLoading).hide();
    $('#'+idDivError).hide();
    cargaInicial();
    eventos();
});

function cargaInicial() {
    $('#' + idCboRowsPage).val(10);
    cargarSelectCompany();
    cargarTablaBlackList();
}

function eventos() {
    click();
    change();
}

function change() {
    $('#' + idCboCompanyFilter).change(function () {
        $('#' + idHddPage).val(1);
        cargarTablaBlackList();
    });
    $('#' + idCboRowsPage).change(function () {
        $('#' + idHddPage).val(1);
        cargarTablaBlackList();
    });
}

function click() {
    $('#'+idBtnBuscarFilter).click(function () {
        $('#' + idHddPage).val(1);
        cargarTablaBlackList();
    });
    $('#'+idBtnNuevoFilter).click(function () {
        $('#'+idDivMessage).html('');
        limpiarCreate();
        $('#'+idDivCreate).show();
        $('#'+idTxtNumeroCreate).focus();
    });

    $('#'+idBtnCleanCreate).click(function () {
        $('#'+idDivMessage).html('');
        limpiarCreate();
    });

    $('#'+idBtnCancelCreate).click(function () {
        $('#'+idDivMessage).html('');
        limpiarCreate();
        $('#'+idDivCreate).hide();
    });

    $('#'+idBtnSaveCreate).click(function () {
        insertarRegistro();
    });

}

function obtainDataInsert(){
    var data = {};
    data.companyId = $('#'+idCboCompanyCreate).val();
    data.number = $('#'+idTxtNumeroCreate).val();
    data.detail = $('#'+idTxtMotivoCreate).val();
    return data;
}

function insertarRegistro() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    var dataInsert = obtainDataInsert();
    if (validateDataRegistro(dataInsert)) {
        $.ajax({
            url: config.urlServiceBlacklist + "/api/blacklist/insert",
            contentType: "application/json",
            type: 'POST',
            data: JSON.stringify(dataInsert),
            headers: {
                "Authorization": token
            },
            beforeSend: function (xhr) {
                $('#'+idDivImgLoading).show();
            },
            success: function (resultData, textStatus, jqXHR) {
                if (resultData.code == 0) {
                    limpiarCreate();
                    printMessage(idDivMessage,resultData.message,0);
                    $('#'+idDivCreate).hide();
                    cargarTablaBlackList();
                } else {
                    printMessage(idDivMessage,resultData.message,1);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                printMessage(idDivMessage,"error al insertar el registro.",1);
            },
            complete: function (jqXHR, textStatus) {
                $('#'+idDivImgLoading).hide();
            }
        });
    }
}

function validateDataRegistro(data) {
    var response = false;
    var contErr = 0;
    $('#'+idDivMessage).html('');

    if (data.companyId <= 0) {
        mostrarMessage("Seleccione una compa&ntilde;ia", 3);
        contErr++;
    }
    
    var config = JSON.parse(sessionStorage.getItem('config'));
    var codeContry = config.rexTypeNumberMobile;
    var rex = new RegExp(selExpresionRegular("PHONE_" + codeContry), "gi");
    if (validateRex(rex, data.number) == 0) {
        mostrarMessage("Ingrese un numero de telefono valido numeros en formato internacional de largo "+config.sizeNumberMobile+" Ej:("+config.placeholderNumberMobile+")", 3);
        contErr++;
    }
    
    var rex = new RegExp(selExpresionRegular("LET_NUM_ESP"), "gi");
    if (validateRex(rex, data.detail) == 0) {
        mostrarMessage("Ingrese un motivo valido solo con letras, numeros y espacios de largo 1 a 100 caracteres (Ej: 'motivo a ingresar' )", 3);
        contErr++;
    }

    if (contErr == 0) {
        response = true;
    }
    return response;
}

function parseDataRegistroToDataForm(data) {
    var form = new FormData();
    form.append('idcompany', data.idcompany);
    form.append('motivo', data.motivo);
    form.append('number', data.number);
    return form;
}

function mostrarMessage(error, type) {
    var img;
    switch (type) {
        case 1:
            img = "<img src=\"images/icons/alert.png\">";
            break;
        case 2:
            img = "<img src=\"images/icons/confirm.png\">";
            break;
        case 3:
            img = "<img src=\"images/icons/error.png\">";
            break;
        default:
            img = "<img src=\"images/icons/error.png\">";
            break;
    }
    var div = $("<div/>", {"class": "divMessage"});
    div.append(img);
    div.append(error);
    $('#'+idDivMessage).append(div);
}

function limpiarCreate() {
    $('#'+idCboCompanyCreate).val(0);
    $('#'+idTxtNumeroCreate).val('');
    $('#'+idTxtMotivoCreate).val('');
}

function cargarTablaBlackList() {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    var jsonData = cargarParametros();
    $.ajax({
        //url: "ajaxfuncs/aprovisionamiento/listBlackList.php?" + params,
        url: config.urlServiceBlacklist + "/api/blacklist/",
        contentType: "application/json",
        type: "POST",
        data: JSON.stringify(jsonData),
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            $('#'+idDivImgLoading).show();
        },
        success: function (resultData, textStatus, jqXHR) {
            if (resultData.code == null) {
                resultData = JSON.parse(resultData);
            }
            if (resultData.code == 0) {
                crearBtnHojas(resultData.countRows);
                agregarDataBlackList(resultData.data);
                $('#'+idDivTable).show();
                $('#'+idDivError).hide();
                printMessage(idDivMessage,"",-1);
            } else {
                console.log(resultData.message);
                $('#'+idDivTable).hide();
                printMessage(idDivMessage,resultData.message,1);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            $('#'+idDivTable).hide();
            $('#'+idDivError).show();
        },
        complete: function (jqXHR, textStatus) {
            $('#'+idDivImgLoading).hide();
        }
    });
}

function cargarParametros(){
    
    var idCompany = $('#'+idCboCompanyFilter).val();
    if(idCompany == null){idCompany=0};
    
    var filterText = $('#'+idTxtDataFilter).val();
    if(filterText == null){filterText=""};
    
    var actPage = $('#'+idHddPage).val();
    if(actPage == null ) actPage = 0;
    if(actPage>0) actPage--;
    
    var size = $('#'+idCboRowsPage).val();
    if(size == null ) size = 0;
        
    var json = {
	"page":actPage,
	"size":size,
	"filters":{
            "textFilter":filterText,
            "idCompany":idCompany
        }
    };
    return json;
}

function agregarDataBlackList(data) {
    $('#'+idDivBodyTable).html('');
    if (data != null) {
        for (var i in data) {
            var fila = data[i];
            var tr = $('<tr/>');

            if (i % 2 == 0) {
                tr.addClass('trPar');
            } else {
                tr.addClass('trInpar');
            }
            var tdcompany = $('<td/>').html(fila.companyName);
            tdcompany.css('min-width', '100px');
            tdcompany.css('max-width', '100px');
            tr.append(tdcompany);

            var tdnumber = $('<td/>').html(fila.number);
            tdnumber.css('min-width', '100px');
            tdnumber.css('max-width', '100px');
            tr.append(tdnumber);

            var tddetail = $('<td/>').html(fila.detail);
            tddetail.css('min-width', '250px');
            tddetail.css('max-width', '250px');
            tr.append(tddetail);

            var tddate = $('<td/>').html(fila.date);
            tddate.css('min-width', '150px');
            tddate.css('max-width', '150px');
            tr.append(tddate);

            var tduser = $('<td/>').html(fila.userName);
            tduser.css('min-width', '100px');
            tduser.css('max-width', '100px');
            tr.append(tduser);


            var btnDelete = $('<span/>');
            btnDelete.html('X');
            btnDelete.attr('onClick', 'deleteNumber(' + fila.id + ');');
            btnDelete.attr('class', 'buttom');
            var tddelete = $('<td/>').append(btnDelete);
            tddelete.css('min-width', '100px');
            tddelete.css('max-width', '100px');
            tr.append(tddelete);

            $('#'+idDivBodyTable).append(tr);
        }
    }
}

function crearBtnHojas(total) {
    var rowsXpage = $('#'+idCboRowsPage).val();
    if (rowsXpage == null)
        rowsXpage = 10;
    var maxPages = total / rowsXpage;
    var init = 0, saltos = 2, viewPages = 5;
    var txtPage = $('#'+idHddPage).val();
    $('#'+idDivPages).html('');
    var page = parseInt(txtPage);
    maxPages = Math.trunc(maxPages);
    var diff = total % rowsXpage;
    if (diff > 0)
        maxPages += 1;


    $('#'+idBtnAtras).hide();
    $('#'+idBtnSiguiente).hide();

    init = page;
    var flag = true;
    var flagsaltos = saltos;
    while (flag) {
        if (flagsaltos == 0 || init <= 1)
        {
            flag = false;
        } else {
            init--;
            flagsaltos--;
        }
    }
    var index = 0;
    var clase = "";
    for (index = init; index <= maxPages; index++) {
        if (page == index) {
            clase = "buttom_selected";
        } else {
            clase = "buttom";
        }
        var btn = $('<span/>', {"class": clase, "onclick": "loadPage(" + index + ")"}).html(index);
        $('#'+idDivPages).append(btn);
        var fin = index;
        if (fin == (page + saltos)) {
            index = maxPages;
        }
    }
    if ((fin) < maxPages) {
        $('#'+idBtnSiguiente).show();
    }
    if (init > 1) {
        $('#'+idBtnAtras).show();
    }
}

function loadPage(page) {
    $('#'+idDivMessage).html('');
    $('#'+idHddPage).val(page);
    cargarTablaBlackList();
}

function deleteNumber(id) {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    
    $('#'+idDivMessage).html('');
    if (confirm("¿Está Seguro de eliminar este numero del blacklist.?")) {
        $.ajax({
            url: config.urlServiceBlacklist + "/api/blacklist/remove/"+id,
            type: "GET",
            contentType: "application/json",
            headers: {
               "Authorization": token
            },
            //url: "ajaxfuncs/aprovisionamiento/deleteBlackList.php?id=" + id,
            //type: 'POST',
            //contentType: "application/json",
            beforeSend: function (xhr) {
                $('#'+idDivImgLoading).show();
            },
            success: function (resultData, textStatus, jqXHR) {
                if (resultData.code == null) {
                    resultData = JSON.parse(resultData);
                }
                if (resultData.code == 0) {
                    cargarTablaBlackList();
                    $('#'+idDivMessage).hide();
                } else {
                    printMessage(idDivMessage,resultData.message,1);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                printMessage(idDivMessage,"error al enviar la eliminacion del registro.",1);
            },
            complete: function (jqXHR, textStatus) {
                $('#'+idDivImgLoading).hide();
            }
        });
    }
}

function cargarSelectCompany() {
    $.ajax({
        url: "ajaxfuncs/general/listCompany.php",
        contentType: "application/json",
        success: function (data, textStatus, jqXHR) {
            if (data.result == "OK") {
                cargarCombo('cboCompany-create', data.data, 0, 'Todas');
                cargarCombo('cboCompany-filterBlacklist', data.data, 0, 'Todas');
            } else {
                console.log(data.message);
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log("error al cargar las compañias");
        }
    });
}

function printMessage(id,message,typeError){
    var img;
    if(typeError==0){
        img = $("<img/>", {"src": "images/icons/confirm.png"});
    }else if(typeError == 1){
        img = $("<img/>", {"src": "images/icons/error.png"});
    }
    $('#'+idDivMessage).html('')
    $('#'+idDivMessage).append(img);
    var span = $('<span>').append(message);
    $('#'+idDivMessage).append(span);
    $('#'+idDivMessage).show();
}
