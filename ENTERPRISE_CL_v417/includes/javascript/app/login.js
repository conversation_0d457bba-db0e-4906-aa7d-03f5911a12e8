$(document).ready(function(){
	loadConfigJavascript();
});

function loadConfigJavascript(){
	$.ajax({
		url: "ajaxfuncs/general/ajaxConfig.php",
		type: 'GET',
		processData: false,
		contentType: "application/json;charset=UTF-8",
		success: function (data, textStatus, jqXHR) {
			sessionStorage.setItem ('config', data);
			//console.log(JSON.parse(data));
			//console.log(sessionStorage.getItem('config'));
		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log("error");
		}
	});	
}
