const typeMsgAlert = 1;
const typeMsgSuccess = 2;
const typeMsgError = 3;
const typeMsgDefault = 4;

function getTypeMgsAlert(){
    return typeMsgAlert;
}
function getTypeMsgSuccess(){
    return typeMsgSuccess;
}
function getTypeMsgError(){
    return typeMsgError;
}
function getTypeMsgDefault(){
    return typeMsgDefault;
}

function parseDataUser(data) {
    let array = [];
    for (var index in data) {
        let item = data[index];
        let element = {};
        element.id = item.id;
        element.description = item.username;
        array.push(element);
    }
    return array;
}

function configBtnDates(idInput, idBtn) {
    let date = new Date();
    let year = date.getFullYear();
    initYear = year - 50;
    endYear = year + 50;
    Calendar.setup({inputField: idInput, range: [initYear, endYear],
        ifFormat: "%Y-%m-%d",
        showsTime: false,
        timeFormat: "24",
        button: idBtn
    });
}

function loadCboPromise(idImgLoading, idDivMessage, pathService) {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    return $.ajax({
        url: config.urlServiceGeneral + "/" + pathService,
        type: 'GET',
        contentType: "application/json;charset=UTF-8",
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(idImgLoading, true);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            showMessageWithImage(idDivMessage, "Error al enviar la peticion", getTypeMsgError());
        },
        complete: function (jqXHR, textStatus) {
            viewHtmlObject(idImgLoading, false);
        }
    });
}

function loadCboUser(cboId, selectedUserId, companyId, divMessageId, imgLoadingId) {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    $.ajax({
        url: config.urlServiceAccount + "/api/user/list/" + companyId,
        type: 'GET',
        contentType: "application/json;charset=UTF-8",
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(imgLoadingId, true);
        },
        success: function (responseData, textStatus, jqXHR) {
            if (responseData.code === 0) {
                
                let dataCombo = parseDataUser(responseData.data);
                loadCombobox(cboId, dataCombo, selectedUserId, 'Todos');
                showMessageWithImage(divMessageId, "", typeMsgDefault);

            } else {
                showMessageWithImage(divMessageId, responseData.message, getTypeMsgError());
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            showMessageWithImage(divMessageId, "Error al enviar la peticion", getTypeMsgError());
        },
        complete: function (jqXHR, textStatus) {
            viewHtmlObject(imgLoadingId, false);
        }
    });
}

function loadCbo(cboId, imgLoadingId, divMessageId, pathService) {
    var token = sessionStorage.getItem("token");
    var config = JSON.parse(sessionStorage.getItem('config'));
    $.ajax({
        url: config.urlServiceGeneral + "/"+ pathService,
        type: 'GET',
        contentType: "application/json;charset=UTF-8",
        headers: {
            "Authorization": token
        },
        beforeSend: function (xhr) {
            viewHtmlObject(imgLoadingId, true);
        },
        success: function (responseData, textStatus, jqXHR) {
            if (responseData.code === 0) {
                loadCombobox(cboId, responseData.data, 0, 'Todos');
                showMessageWithImage(divMessageId, "", getTypeMsgDefault());
            } else {
                showMessageWithImage(divMessageId, responseData.message, getTypeMsgError());
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            showMessageWithImage(divMessageId, "Error al enviar la peticion", getTypeMsgError());
        },
        complete: function (jqXHR, textStatus) {
            viewHtmlObject(imgLoadingId, false);
        }
    });
}

function loadCombobox(id, data, selectedId, textDefault) {
    $('#' + id).html('');
    let found = false;
    if (data !== null) {
        if (textDefault !== "") {
            var op = $('<option/>');
            op.val(0);
            op.html(textDefault);
            $('#' + id).append(op);
        }

        for (var i in data) {
            var fila = data[i];
            var op = $('<option/>');
            if (fila.id === parseInt(selectedId)) {
                found = true;
            }
            op.val(fila.id);
            op.html(fila.description);
            $('#' + id).append(op);
        }
        if (found) {
            $('#' + id).val(selectedId);
        } else {
            $('#' + id).val(0);
        }

    }
}

function addDigitLeft(number, maximumDigits, addedCharacter) {
    let response = number.toString();
    while (response.length < maximumDigits) {
        response = addedCharacter + response;
    }
    return response;
}

//function viewHtmlObject(id, status) {
//    if (status) {
//        $('#' + id).show();
//    } else {
//        $('#' + id).hide;
//    }
//}

function viewHtmlObject(id, status) {
    if (status) {
        $('#' + id).css('display', 'block');
    } else {
        $('#' + id).css('display', 'none');
    }
}

function disableHtmlObject(id, status) {
    if (status) {
        $('#' + id).prop('disabled', true);
    } else {
        $('#' + id).prop('disabled', false);
    }
}

function keyOnlyNumbers(e) {
    var key = window.event ? e.which : e.keyCode;
    if (key < 48 || key > 57) {
        //Usando la definición del DOM level 2, "return" NO funciona.
        e.preventDefault();
    }
}

function generateTodayDate() {
    var dt = new Date();
    var month = dt.getMonth() + 1;
    var day = dt.getDate();
    var year = dt.getFullYear();
    var textfecha = year + '-' + addDigitLeft(month, 2, '0' )  + '-' + addDigitLeft(day, 2, '0');
    return textfecha;
}

function generateYesterdayDate() {
    var dt = new Date();
    dt = addDayToDate(dt,-1);
    var month = dt.getMonth() + 1;
    var day = dt.getDate();
    var year = dt.getFullYear();
    var textfecha = year + '-' + addDigitLeft(month, 2, '0' )  + '-' + addDigitLeft(day, 2, '0');
    return textfecha;
}

function showMessageWithImage(id, message, type) {
    var div = $("<div/>", {"class": "divMessage"});
    let sourceImage = '';
    if (message !== null && message.length > 0) {
        switch (type) {
            case 1:
                sourceImage = 'images/icons/alert.png';
                break;
            case 2:
                sourceImage = 'images/icons/confirm.png';
                break;
            case 3:
                sourceImage = 'images/icons/error.png';
                break;
            default:
                break;
        }
        if (sourceImage.length > 0) {
            var img = $('<img/>');
            img.attr('src', sourceImage);
            div.append(img);
        }
    } else {
        message = "";
    }
    div.append(message);
    $('#' + id).html('');
    $('#' + id).append(div);
}

function createSheetButton(total, idCboRowsPage, idHddPage, idDivPages, idBtnBack, idBtnNext) {

    var rowsXpage = $('#' + idCboRowsPage).val();
    if (rowsXpage === null)
        rowsXpage = 10;
    var maxPages = total / rowsXpage;
    var init = 0, skip = 2, viewPages = 5;
    var txtPage = $('#' + idHddPage).val();
    $('#' + idDivPages).html('');
    var page = parseInt(txtPage);
    maxPages = Math.trunc(maxPages);
    var diff = total % rowsXpage;
    if (diff > 0)
        maxPages += 1;
    $('#' + idBtnBack).hide();
    $('#' + idBtnNext).hide();
    init = page;
    var flag = true;
    var skipFlag = skip;
    while (flag) {
        if (skipFlag === 0 || init <= 1)
        {
            flag = false;
        } else {
            init--;
            skipFlag--;
        }
    }
    var index = 0;
    var classText = "";
    for (index = init; index <= maxPages; index++) {
        if (page === index) {
            classText = "buttom_selected";
        } else {
            classText = "buttom";
        }
        var btn = $('<span/>', {"class": classText, "onclick": "loadPage(" + index + ")"}).html(index);
        $('#' + idDivPages).append(btn);
        var fin = index;
        if (fin === (page + skip)) {
            index = maxPages;
        }
    }
    if ((fin) < maxPages) {
        $('#' + idBtnNext).show();
    }
    if (init > 1) {
        $('#' + idBtnBack).show();
    }
}

//function configureButtontDate(idInput, idBtn) {
//    let modYear = 10;
//    let fecha = new Date();
//    let year = fecha.getFullYear();
//    let initYear = year - modYear;
//    let endYear = year + modYear;
//    Calendar.setup({inputField: idInput, range: [initYear, endYear],
//        ifFormat: "%Y-%m-%d",
//        showsTime: false,
//        timeFormat: "24",
//        button: idBtn
//    });
//}

function commonValidateDateLessYesterday(textDate) {
//    var dateAux = date.split("-");
//    var today = new Date();
//    var x = new Date(dateAux[0], dateAux[1] - 1, dateAux[2], today.getHours(), today.getMinutes(), today.getSeconds(), today.getMilliseconds());
//    if (x > today)
//        return true;
//    else
//        return false;
    let today = new Date();
    let yesterday = addDayToDate(today, -1);
    let date = arrayToDate(textDate.split("-"), today);
    if (date.getTime() < yesterday.getTime() || date.getTime() === yesterday.getTime())
        return true;
    else
        return false;
}

function commonValidateDateLessThanToday(textDate) {
    let today = new Date();
    today.setHours(0,0,0,0);
    let date = arrayToDate(textDate.split("-"));
//    var dateAux = date.split("-");
//    var x = new Date(dateAux[0], dateAux[1] - 1, dateAux[2], today.getHours(), today.getMinutes(), today.getSeconds(), today.getMilliseconds());
    if (date.getTime() < today.getTime() || date.getTime() === today.getTime())
        return true;
    else
        return false;
}

function commonValidateEndDateGreaterThanStartDate(startDate, endDate) {
    var start = arrayToDate(startDate.split("-"), null);
    var end = arrayToDate(endDate.split("-"), null);
    if (end.getTime() > start.getTime() || end.getTime() === start.getTime())
        return true;
    else
        return false;
}

function arrayToDate(array) {
    let year = parseInt(array[0]);
    let month = parseInt(array[1]);
    let day = parseInt(array[2]);
    //se le resta 1 ya que en el objeto Date de javascript los meses van de 0 a 11
    month--;
    let newDate = new Date(year, month, day, 0, 0, 0, 0);
    return newDate;
}
function addDayToDate(today, daysAdd){
    let newDate=today;
    newDate.setDate(newDate.getDate() + daysAdd);
    return newDate;
}

function fillDataTableReportDetail(data, idHeadTable, idBodyTable, idFooterTable) {
    var headers = data.headers;
    var body = data.body;
    $('#' + idHeadTable).html('');
    $('#' + idBodyTable).html('');
    $('#' + idFooterTable).html('');
    var trHeader = $('<tr/>');
    var row = '';

    for (var item in headers) {
        var header = headers[item];
        var thHeader = $('<th/>').html(header.name);
        thHeader.css("min-width", header.width + "px");
        thHeader.css("max-width", header.width + "px");
        trHeader.append(thHeader);
    }
    $('#' + idHeadTable).append(trHeader);

    var textClass = "";
    for (var rowIndex in body) {
        var trBody = $('<tr/>');
        row = body[rowIndex].cells;
        for (var cellIndex in row) {
            var width = headers[cellIndex].width;
            var tdBody = $('<td/>').html(row[cellIndex]);
            if (rowIndex % 2 === 0) {
                textClass = "table-list-tr";
            } else {
                textClass = "";
            }
            tdBody.addClass(textClass);
            tdBody.css("min-width", width + "px");
            tdBody.css("max-width", width + "px");
            trBody.append(tdBody);
        }
        $('#' + idBodyTable).append(trBody);
    }
}

function fillDataTableReportConsolidated(data, idHeadTable, idBodyTable, idFooterTable) {
    var headers = data.headers;
    var body = data.body;
    $('#' + idHeadTable).html('');
    $('#' + idBodyTable).html('');
    $('#' + idFooterTable).html('');
    var trHeader = $('<tr/>');
    var row = '';

    for (var item in headers) {
        var header = headers[item];
        var thHeader = $('<th/>').html(header.name);
        thHeader.css("min-width", header.width + "px");
        thHeader.css("max-width", header.width + "px");
        trHeader.append(thHeader);
    }
    $('#' + idHeadTable).append(trHeader);
    var totales = body.length - 1;
    var textClass = "";
    for (var rowIndex in body) {
        if (rowIndex < totales) {
            var trBody = $('<tr/>');
            row = body[rowIndex].cells;

            for (var cellIndex in row) {
                var width = headers[cellIndex].width;
                var tdBody = $('<td/>').html(row[cellIndex]);
                if (rowIndex % 2 === 0) {
                    textClass = "table-list-tr";
                } else {
                    textClass = "";
                }
                tdBody.addClass(textClass);
                tdBody.css("min-width", width + "px");
                tdBody.css("max-width", width + "px");
                trBody.append(tdBody);
            }
        }
        $('#' + idBodyTable).append(trBody);
    }

    var trFooter = $('<tr/>');
    row = body[totales].cells;
    for (var cellIndex in row) {
        var width = headers[cellIndex].width;
        var tdFooter = $('<th/>').html(row[cellIndex]);
        tdFooter.css("min-width", width + "px");
        tdFooter.css("max-width", width + "px");
        trFooter.append(tdFooter);
    }
    $('#' + idFooterTable).append(trFooter);
}