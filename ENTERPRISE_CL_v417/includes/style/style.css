/*@import url(http://fonts.googleapis.com/css?family=Open+Sans+Condensed:700);*/
@import url(https://fonts.googleapis.com/css?family=Open+Sans+Condensed:700);
/*@import url(http://fonts.googleapis.com/css?family=Merriweather+Sans:300);*/
@import url(https://fonts.googleapis.com/css?family=Merriweather+Sans:300);

body {
	font: 10px normal ;
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	
	margin: 0;
	padding: 0;
}

h1 {
	font: 4.7em normal;
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	color: #333;
	margin: 0;
	padding: 5px 0;
}

h1 small{
	font: 0.2em normal;
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	text-transform:uppercase;
	letter-spacing: 1.5em;
	display: block;
	color: #fff;
}

.table-list {
	text-align: left;
}

.table-list th {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	font-weight: bold;
	text-shadow: 0.1em 0.1em #000;
	text-align: left;
	color:#fff;
	background-color: #009ed7;
	padding: 10px;
}

.table-list td {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color:#666;
	text-shadow: 0.1em 0.1em #f6f6f6;
	border-collapse: collapse;
	padding: 10px;
	vertical-align: top;
}

.table-list-td-error {
	background-color: #d6e5b2;
}

.table-list-tr { 
	background-color: #d2dfe4;
}

.table-general { 
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color: #59574c;
}

.table-general td { 
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color: #666;
	height: 30px;
	text-align:left;
}

.loggin {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	width:150px; 
	height: 40px;
	line-height: 13px;
	background-color:#777777;
	padding-top: 6px;
	padding-bottom: 15px;
	padding-left: 10px;
	padding-right: 15px;
	color:#FFF;
	border-bottom-right-radius: 15px;
	border-bottom-left-radius: 0px;
	border-top-right-radius: 15px;
	border-top-left-radius: 15px;
}

/*		////////////////////////////////////
				TEXTO
		////////////////////////////////////	*/
		

.text-title {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 20px;
	text-shadow: 0.1em 0.1em #fafafa;
}

.text-copyright {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color:#CCC;
}

.container-text-title {
	/*font-family: 'Merriweather Sans', sans-serif;*/	
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 25px; 
	color:#009ed7;
}

.text-company {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 25px; 
	color:#009ed7;
}

b {
	font-weight: bold;
	/*font-size: 11px;*/
}



a:link {text-decoration: none; color: #009ed7; cursor: pointer; border: 0px;}
a:visited {text-decoration: none; color: #009ed7; cursor: pointer; border: 0px; }
a:active {text-decoration: none; color: #009ed7; cursor: pointer; border: 0px;  }
a:hover {text-decoration: underline; color: #009ed7; cursor: pointer; border: 0px; }

/*		////////////////////////////////////
				FORMULARIO
		////////////////////////////////////	*/

input, textarea, select {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color:#666;
    border:1px solid #ddd;
	padding: 3px 10px 3px 10px;
	background-color: #ddd;
	outline:none;
}

input[type="text"]:hover {
    background: #ccc;
}

input[type="submit"] {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color:#FFFFFF;
	cursor: pointer;	
	text-align:center;
	background-color: #009ed7;
	padding: 6px 10px 6px 10px;
	text-decoration: none;
}

textarea:hover {
    background: #ccc;
}

span.buttom  {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color:#FFFFFF;
	cursor: pointer;	
	text-align:center;
	background-color: #009ed7;
	padding: 6px 10px 6px 10px;
	text-decoration: none;
	text-transform:uppercase;
}
span.buttom_selected  {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	color: #009ed7 !important; 
	cursor: pointer;	
	text-align:center;
	background-color: #FFFFFF !important;
	padding: 6px 10px 6px 10px;
	text-decoration: none;
	text-transform:uppercase;
}

a:link {
	text-decoration: none;
	border: 0px;
}

span.buttom a:link {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 12px;
	color:#FFFFFF !important;
	cursor: pointer;	
	text-align:center;
	background-color: #009ed7;
	padding: 6px 10px 6px 10px;
	border-radius: 7px;
	text-decoration: none;
	text-transform:uppercase;
	-webkit-border-radius: 7px;
   -moz-border-radius: 7px;
}

.messagebox{
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	width:100px;
	border:1px solid #d2dfe4;
	background:#d2dfe4;
	padding:10px;
	margin-top: 15px;
	color:#666;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 15px;
	border-bottom-right-radius: 15px;
	border-bottom-left-radius: 15px;
	border-top-right-radius: 15px;
	border-top-left-radius: 15px;
}

.messageboxok {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	width:auto;
	border:1px solid #d2dfe4;
	background:#d2dfe4;
	padding:10px;
	font-weight:bold;
	color:#666;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 15px;
	border-bottom-right-radius: 15px;
	border-bottom-left-radius: 15px;
	border-top-right-radius: 15px;
	border-top-left-radius: 15px;
}

.messageboxerror {
	font-family: Open Sans Condensed;
	font-size: 11px;
	width:auto;
	border:1px solid #d2dfe4;
	background:#d2dfe4;
	padding:10px;
	font-weight:bold;
	color:#666;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 15px;
	border-bottom-right-radius: 15px;
	border-bottom-left-radius: 15px;
	border-top-right-radius: 15px;
	border-top-left-radius: 15px;
}

.button_off {
	text-decoration: none; 
	font-family: Verdana;
	font-weight: bold;
	Font-size: 9px;
	color: #FFFFFF;
	background-color:#6aba17;
	border-top:#77d21a solid 1px;
	border-left: #77d21a solid 1px;
	border-bottom:#589b13 solid 2px;
	border-right: #589b13 solid 2px;
	height:10px;
	margin: 2px 0 0px 0px; 
	padding: 4px 6px 4px 6px;
	text-align: center;
	cursor: pointer;
}
		
.button_on {
	text-decoration: none; 
	font-family: Verdana;
	font-weight: bold;
	font-size: 9px;
	color: #FFFFFF;
	background-color:#72c918;
	border-top:#77d21a solid 1px;
	border-left: #77d21a solid 1px;
	border-bottom:#589b13 solid 2px;
	border-right: #589b13 solid 2px;
	height:10px;
	margin: 2px 0 0px 0px; 
	padding: 4px 6px 4px 6px;
	text-align: center;
	cursor: pointer;
}

.alert_big {
	font-family: "Lucida Sans Unicode", "Lucida Grande", sans-serif;
	font-size: 11px;
	display: inline-block;
	line-height: 20px;
	background-color: #d2dfe4;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 15px;
	color:#666;
	border-bottom-right-radius: 15px;
	border-bottom-left-radius: 15px;
	border-top-right-radius: 15px;
	border-top-left-radius: 15px;
	margin-bottom: 15px;
}

img {
	border: 0px;
}

.img_button {
	border: 0px;
	cursor: pointer;
}
	
.oculto{
	display: none;
}
.visible{
	display: inline-block;
}
.trPar{
/*	background-color: #fff;*/
}
.trInpar{
	background-color: #d2dfe4;
}
.tableReport{
	font-size: 11px;
	border-spacing : 0 0;
	border-collapse : collapse;
}
.theadTable th{
	font-weight: bold;
	text-shadow: 0.1em 0.1em #000;
	text-align: left;
	color: #fff;
	background-color: #009ed7;
	padding: 10px;
}
.tbodyTable td{
	color: #666;
	text-shadow: 0.1em 0.1em #f6f6f6;
	border-collapse: collapse;
	padding: 10px;
	vertical-align: top;
}
.espaciadoObjetos{
	margin-bottom: 10px;
	margin-top: 10px;
}
.ctlFull{
	width: 100%;
}
.ctlSelect{
	width: 100%;
}
.ctlInput{
	width: 92%;
}
.ctlCalendar{
    width: 82%;
}
.divInputText{
	width: 90%;
}
.divInputDateText{
	float: left;
}
.divInputDateBtn{
	float: left;
	margin-left: 10px;
}
.formEditUser{
	max-width: 400px;
}
.formLoadCampaign{
	max-width: 600px;
}
.td20{
	width: 20%;
}
.td30{
	width: 30%;
}
.td25{
	width: 25%;
}
.td50{
	width: 50%;
}
.td75{
	width: 75%;
}
.td100{
	width: 100%;
}
.alR{
	text-align: right;
}
.alL{
	text-align: left;
}
.divMessage{
	display: block;
	font-size: 1.5em;
        color: #000001;
}
.btnImg{
	margin: 0 4px;
	
}
fieldset{
	font-size: 1.5em;
}
.ulNotasInformativas{
	font-size: 1.0em;
}
.containerbtnImg33{
	float: left;
	width: 33%;
}
.containerTable{
	max-width: 900px;
	min-width: 900px;
	overflow-x: auto;
}

.reportCustom{
	width: 100%;
    display:block;
}
.theadTable{
	display: inline-block;
    /*width: 100%;*/
    /*height: 20px;*/
}
.tbodyTable{
	max-height: 300px;
    display: inline-block;
    overflow-y: auto;
	overflow-x: hidden;
}
