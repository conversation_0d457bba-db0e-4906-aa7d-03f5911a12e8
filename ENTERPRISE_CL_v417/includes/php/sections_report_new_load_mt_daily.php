
<?php

function gesLoadDailyDetail() {
    title("Reporte MT", "Detalle de Carga Diario", "campa&ntilde;as");
    $idAccount = $_SESSION["id_account"];
    $idCompany = $_SESSION["id"];
    ;
    ?>
    <script type="text/javascript" src="includes/javascript/app/report/loadDailyDetail.js?v=<?=VERSION?>"></script>
    <fieldset>
        <legend>Filtro</legend>
        <input id="hddIdAccount" type="hidden" value="<?= $idAccount ?>">
        <input id="hddIdCompany" type="hidden" value="<?= $idCompany ?>">
        <table class="table-general td100">
            <tbody>
                <tr>
                    <td><b>Estado</b></td>
                    <td><select id="cboStaus" ></select></td>
                    <td><b>Tipo de ingreso</b></td>
                    <td><select id="cboInputMode" ></select></td>
                </tr>
                <tr>
                    <td><b>Company</b></td>
                    <td><select id="cboCompany" type="text"></select></td>
                    <td><b>Usuario</b></td>
                    <td><select id="cboUser" ></select></td>
                </tr>
                <tr>
                    <td><b>Celular</b></td>
                    <td><input id="txtnumberPhone" type="text"></td>
                    <td><b>Codigo cliente</b></td>
                    <td><input id="txtCodeclient" type="text"></td>
                </tr>
                <tr>
                    <td><b>Id campaña</b></td>
                    <td><input id="campaignId" type="text"></td>
                </tr>
            </tbody>
        </table>
    </fieldset>

    <table>
        <tbody>
            <tr>
                <td></td>
                <td>
                    <button id="btnFind" type="text">Buscar</button>
                    <button id="btnGeneratePageCsv" type="text">Generar CSV Pagina Actual</button>
                    <button id="btnGenerateAllCsv" type="text">Generar CSV Completo </button>
                </td>
            </tr>
        </tbody>
    </table>
    <div id="divImgLoading">
        <img id="imgLoading" src="images/waiting_small.gif">
    </div>
    <div id="divMessage" style="width: 100%"></div>

    <div class="containerTable">

        <input id="hddActualPage" type="hidden" value="1"/>
        <table class="ctlFull">
            <tr>
                <td>
                    <div style="margin: 0 auto; width: 270px;">
                        <div style="display: inline-block;" >
                            <span id="btnPageAtras" class="buttom"><</span>
                        </div>
                        <div id="btnLinkPages" style="display: inline-block;">
                            <span class="buttom">1</span>
                        </div>
                        <div style="display: inline-block;">
                            <span id="btnPageSiguiente" class="buttom">></span>
                        </div>
                        <div style="display: inline-block;">
                            <select id="cboRowsPage">
                                <option value="2">2</option>
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="20">20</option>
                            </select>
                        </div>
                    </div>
                </td>
            </tr>
        </table>

        <table style="width: 100%" class="reportCustom tableReport espaciadoObjetos " >
            <thead id="headTable" class="theadTable theadReportCustom">

            </thead>
            <tbody id="bodyTable" class="tbodyTable tbodyReportCustom">

            </tbody>
        </table>
    </div>

    <?php
}
?>

<?php

function gesLoadDailyConsolidated() {
    title("Reporte MT", "Consolidado de Carga Diario", "campa&ntilde;as");
    $idAccount = $_SESSION["id_account"];
    $idCompany = $_SESSION["id"];
    ;
    ?>
    <script type="text/javascript" src="includes/javascript/app/report/loadDailyConsolidated.js?v=<?=VERSION?>"></script>
    <fieldset>
        <legend>Filtro</legend>
        <input id="hddIdAccount" type="hidden" value="<?= $idAccount ?>">
        <input id="hddIdCompany" type="hidden" value="<?= $idCompany ?>">
        <table class="table-general td100">
            <tbody>
                <tr>
                    <td><b>Company</b></td>
                    <td><select id="cboCompany" type="text"></select></td>
                    <td><b>Usuario</b></td>
                    <td><select id="cboUser" ></select></td>
                </tr>
                <tr>
                    <td><b>Tipo de ingreso</b></td>
                    <td><select id="cboInputMode" ></select></td>
                    <td><b>Id campaña</b></td>
                    <td><input id="campaignId" type="text"></td>
                </tr>

            </tbody>
        </table>
    </fieldset>

    <table>
        <tbody>
            <tr>
                <td></td>
                <td>
                    <button id="btnFind" type="text">Buscar</button>
                    <button id="btnGenerateConsolidatedCsv" type="text">Generar CSV Consolidado</button>
                    <button id="btnGenerateDetailCsv" type="text">Generar CSV Detalle </button>
                </td>
            </tr>
        </tbody>
    </table>
    <div id="divImgLoading">
        <img id="imgLoading" src="images/waiting_small.gif">
    </div>
    <div id="divMessage" style="width: 100%"></div>

    <div class="containerTable">

        <table style="width: 100%" class="reportCustom tableReport espaciadoObjetos " >
            <thead id="headTable" class="theadTable theadReportCustom">

            </thead>
            <tbody id="bodyTable" class="tbodyTable tbodyReportCustom">

            </tbody>
            <tfoot id="footerTable" class="theadTable theadReportCustom">

            </tfoot>
        </table>
    </div>

    <?php
}
?>