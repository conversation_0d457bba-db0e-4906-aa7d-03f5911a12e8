<?php
include ('dbConn.php');
?>

<?php

function maintenanceUser() {
    title("Mantenci&oacute;n", "Usuarios", "mantencion");
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    $profile = $_SESSION["profile"];
    $offset = $_SESSION["OFFSET_COMPANY"];
    if (select_type_profile($profile) == 1) {
        $classtxt = "td50";
        $cbocompanyview = 1;
    } else {
        $classtxt = "td75";
        $cbocompanyview = 0;
    }
    ?>
    <script type="text/javascript" src="includes/javascript/app/maintenance/user.js?v=<?=VERSION?>"></script>
    <div id="divEditUser" style="display: none; "class="formEditUser espaciadoObjetos">
        <input id="hddUser" type="hidden" value="<?= $login ?>">
        <input id="hddCompany" type="hidden" value="<?= $company ?>">
        <input id="hddProfile" type="hidden" value="<?= $profile ?>">
        <input id="hddOffset" type="hidden" value="<?= $offset ?>">
        <fieldset>
            <legend id="titleLegendEditUser">Edici&oacute;n de usuarios</legend>
            <input id="txtEditIdUser" type="hidden" value="0">
            <table class="ctlFull">
                <?php if ($cbocompanyview == 1) { ?>
                    <tr>
                        <td><b>Compa&ntildeia:</b></td>
                        <td><select id="cboEditCompany" class="ctlFull"></select></td>
                    </tr>
                <?php } ?>
                <tr>
                    <td><b>Login:</b></td>
                    <td><div class="divInputText" ><input id="txtEditLogin" class="ctlFull" type="text" placeholder="Ingrese el login del usuario"></div></td>
                </tr>
                <tr>
                    <td><b>Nombres:</b></td>
                    <td><div class="divInputText" ><input id="txtEditNombres" class="ctlFull" type="text" placeholder="Ingrese los nombres del usuario"></div></td>
                </tr>
                <tr>
                    <td><b>Apellidos:</b></td>
                    <td><div class="divInputText" ><input id="txtEditApellidos" class="ctlFull" type="text" placeholder="Ingrese los apellidos del usuario"></div></td>
                </tr>
                <tr>
                    <td><b>Mail:</b></td>
                    <td><div class="divInputText" ><input id="txtEditMail" class="ctlFull" type="text" placeholder="Ingrese el mail del usuario"></div></td>
                </tr>
                <tr>
                    <td><b>Telefono:</b></td>
                    <td><div class="divInputText" ><input id="txtEditTelefono" class="ctlFull" type="text" placeholder="Ingrese el telefono del usuario"></div></td>
                </tr>
                <tr>
                    <td><b>Unidad:</b></td>
                    <td><div class="divInputText" ><input id="txtEditUnidad" class="ctlFull" type="text" placeholder="Ingrese la unidad del usuario"></div></td>
                </tr>
                <tr>
                    <td><b>Zona Horaria:</b></td>
                    <td><select id="cboEditTimeZone" class="ctlFull"></select></td>
                </tr>
                <tr>
                    <td><b>Estado:</b></td>
                    <td><select id="cboEditStatus" class="ctlFull"></select></td>
                </tr>
                <tr>
                    <td><b>Perfil:</b></td>
                    <td><select id="cboEditProfile" class="ctlFull"></select></td>
                </tr>
            </table>
            <table align="right">
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td><button id="btnEditSave" type="button" >Guardar</button></td>
                    <td><button id="btnEditCancel" type="button" >Cancel</button></td>
                    <td><button id="btnEditClean" type="button" >Limpiar</button></td>
                </tr>
            </table>
        </fieldset>

    </div>

    <div id="div-filtros">
        <fieldset class="espaciadoObjetos">
            <table style="width: 100%">
                <tr>
                    <td class="<?= $classtxt ?>">Filtro</td>
                    <td class="td25">Perfil</td>
                    <?php if ($cbocompanyview == 1) { ?>
                        <td class="td25">Compa&ntilde;ia</td>
                    <?php } ?>
                </tr>
                <tr>
                    <td class="<?= $classtxt ?>">
                        <div class="divInputText">
                            <input id="txtFilterData" class="ctlFull" placeholder="Ingresar usuario, mail, nombres, unidad o telefono para filtrar">
                        </div>
                    </td>
                    <td class="td25"><select id="cboFilterPerfil" class="ctlFull"></select></td>
                    <?php if ($cbocompanyview == 1) { ?>
                        <td class="td25"><select id="cboFilterCompany" class="ctlFull"></select></td>
                    <?php } ?>
                </tr>
            </table>
        </fieldset>
        <div style="width: 100%;height: 3em;" class="espaciadoObjetos" >
            <table align="right" >
                <tr>
                    <td>
                        <button id="btnBuscarUser" type="button" >Buscar</button>
                        <button id="btnNuevoUser" type="button" >Nuevo</button>
                    </td>
                </tr>
            </table>
        </div>
        <div id="divMessage" style="width: 100%"></div>
        <div id="divImgLoading"><img src="images/waiting_small.gif"></div>
    </div>

    <div id="divError" class="espaciadoObjetos">No se encontraron registros de usuarios.</div>

    <div id="divListUser">


        <input id="txtPage" type="hidden" value="1"/>
        <table class="ctlFull">
            <tr>
                <td>
                    <div style="margin: 0 auto; width: 270px;">
                        <div style="display: inline-block;" >
                            <span id="btnAtras" class="buttom"><</span>
                        </div>
                        <div id="btnPages" style="display: inline-block;">
                            <span class="buttom">1</span>
                        </div>
                        <div style="display: inline-block;">
                            <span id="btnSiguiente" class="buttom">></span>
                        </div>
                        <div style="display: inline-block;">
                            <select id="cboRowsPage">
                                <option value="2">2</option>
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="20">20</option>
                            </select>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <div class="containerTable">
            <table style="width: 100%" class="tableReport espaciadoObjetos" >
                <thead id="headListUser" class="theadTable">
                    <tr>
                        <th style="min-width: 70px; max-width:70px;">LOGIN</th>
                        <th style="min-width: 100px; max-width:100px;">NOMBRES</th>
                        <th style="min-width: 100px; max-width:100px;">APELLIDOS</th>
                        <th style="min-width: 150px; max-width:150px;">MAIL</th>
                        <th style="min-width: 100px; max-width:100px;">TELEFONO</th>
                        <th style="min-width: 100px; max-width:100px;">UNIDAD</th>
                        <th style="min-width: 100px; max-width:100px;">ESTADO</th>
                        <th style="min-width: 100px; max-width:100px;">COMPA&Ntilde;IA</th>
                        <th style="min-width: 150px; max-width:150px;">ZONA HORARIA</th>
                        <th style="min-width: 100px; max-width:100px;">PERFIL</th>
                        <th style="min-width: 100px; max-width:100px;">ACCION</th>
                        <th style="min-width: 100px; max-width:100px;">PASS</th>
                    </tr>
                </thead>
                <tbody id="bodyListUser" class="tbodyTable">
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
    <?php
}
?>

<?php

function maintenanceBlacklist() {
    title("Aprovisionamiento", "Mantenci&oacute;n Blacklist", "mantencion");
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    $profile = $_SESSION["profile"];
    $offset = $_SESSION["OFFSET_COMPANY"];
    if (select_type_profile($profile) == 1) {
        $classtxt = "td75";
        $cbocompanyview = 1;
    } else {
        $classtxt = "td100";
        $cbocompanyview = 0;
    }
    ?>
    <script type="text/javascript" src="includes/javascript/app/aprovisionamiento/mantenanceBlacklist.js?v=<?=VERSION?>"></script>

    <div id="div-create" class="formEditUser espaciadoObjetos">
        <input id="hddUser" type="hidden" value="<?= $login ?>">
        <input id="hddCompany" type="hidden" value="<?= $company ?>">
        <input id="hddProfile" type="hidden" value="<?= $profile ?>">
        <input id="hddOffset" type="hidden" value="<?= $offset ?>">
        <fieldset>
            <legend id="titleLegend-create">Registro de n&uacute;mero en blackList</legend>
            <table class="ctlFull">
                    <tr>
                        <td><b>Compa&ntildeia:</b></td>
                        <td><select id="cboCompany-create" class="ctlFull"></select></td>
                    </tr>
                <tr>
                    <td><b>N&uacute;mero:</b></td>
                    <td>
                        <div class="divInputText" >
                            <input id="txtNumero-create" type="text" class="ctlFull"  placeholder="Ingrese el n&uacute;mero a registrar en el blacklist">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Motivo:</b>
                    </td>
                    <td>
                        <div class="divInputText" >
                            <textarea id="txtMotivo-create" rows="3" class="ctlFull" placeholder="Ingrese el motivo de registro del numero al blacklist"></textarea>
                        </div>
                    </td>
                </tr>
            </table>
            <table align="right">
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td><button id="btnSave-create" type="button" >Guardar</button></td>
                    <td><button id="btnCancel-create" type="button" >Cancel</button></td>
                    <td><button id="btnClean-create" type="button" >Limpiar</button></td>
                </tr>
            </table>
        </fieldset>

    </div>


    <div id="div-filterBlacklist">
        <fieldset class="espaciadoObjetos">
            <table style="width: 100%">
                <tr>
                    <td class="<?= $classtxt ?>">Filtro</td>
                    <?php if ($cbocompanyview == 1) { ?>
                        <td class="td25">Compa&ntilde;ia</td>
                    <?php } ?>
                </tr>
                <tr>
                    <td class="<?= $classtxt ?>">
                        <div class="divInputText">
                            <input id="txtData-filterBlacklist" class="ctlFull">
                        </div>
                    </td>
                    <?php if ($cbocompanyview == 1) { ?>
                        <td class="td25">
                            <select id="cboCompany-filterBlacklist" class="ctlFull"></select>
                        </td>
                    <?php } ?>
                </tr>
            </table>
        </fieldset>
        <div style="width: 100%;height: 3em;" class="espaciadoObjetos" >
            <table align="right" >
                <tr>
                    <td>
                        <button id="btnBuscar-filterBlacklist" type="button" >Buscar</button>
                        <button id="btnNuevo-filterBlacklist" type="button" >Nuevo</button>
                    </td>
                </tr>
            </table>
        </div>
        <div id="divMessage-blacklist" style="width: 100%">
        </div>
        <div id="divImgLoading-blacklist">
            <img src="images/waiting_small.gif">
        </div>
    </div>
    <div id="divError-tableBlacklist" class="espaciadoObjetos">No se encontraron numeros en blacklist.</div>

    <div id="div-tableBlacklist">
        <input id="hddPage-tableBlacklist" type="hidden" value="1"/>
        <table class="ctlFull">
            <tr>
                <td>
                    <div style="margin: 0 auto; width: 270px;">
                        <div style="display: inline-block;" >
                            <span id="btnAtras-tableBlacklist" class="buttom"><</span>
                        </div>
                        <div id="divPages-tableBlacklist" style="display: inline-block;">
                            <span class="buttom">1</span>
                        </div>
                        <div style="display: inline-block;">
                            <span id="btnSiguiente-tableBlacklist" class="buttom">></span>
                        </div>
                        <div style="display: inline-block;">
                            <select id="cboRowsPage-tableBlacklist">
                                <option value="2">2</option>
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="20">20</option>
                            </select>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <div class="containerTable">
            <table style="width: 100%" class="tableReport espaciadoObjetos" >
                <thead id="headTable-tableBlacklist" class="theadTable">
                    <tr>
                        <th style="min-width: 100px; max-width: 100px;">COMPA&Ntilde;IA</th>
                        <th style="min-width: 100px; max-width: 100px;">NUMERO</th>
                        <th style="min-width: 250px; max-width: 250px;">MOTIVO</th>
                        <th style="min-width: 150px; max-width: 150px;">FECHA</th>
                        <th style="min-width: 100px; max-width: 100px;">USUARIO</th>
                        <th style="min-width: 100px; max-width: 100px;">ACCION</th>
                    </tr>
                </thead>
                <tbody id="bodyTable-tableBlacklist" class="tbodyTable">
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
    <?php
}
?>