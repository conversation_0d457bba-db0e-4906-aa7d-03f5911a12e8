<?php
/*
 * ***************************
  ARRAYS
 * ***************************
 */
?>

<?php

function array_input_mode(&$array_id, &$array_name) {
    $array_id = array("WEB-IND", "WEB-GRP", "UPLOAD", "XMLRPC", "WS-SENDSMS");
    $array_name = array("WEB Individual", "WEB Grupal", "Carga desde Archivo", "WEB Service", "WEB Service");
}

function array_type_input(&$array_id, &$array_name) {
    $array_id = array("XMLRPC", "ALL");
    $array_name = array("Activación Tarjeta", "Marketing Comercial");
}

function array_status(&$array_id, &$array_name) {
    $array_id = array("QUEUED", "PUSHED", "CONFIRMED", "FAILED", "NOPUSHED", "ANULADO");
    $array_name = array("Pendiente", "Despachado", "Confirmado", "Fallido", "Rechazado", "Anulado");
}

function array_carrier(&$array_id, &$array_name) {
    $company = $_SESSION["id"];
//	echo $array_name;
    $array_id = mysql_query("SELECT * FROM dispatch WHERE company = $company");
//	$array_id 	= mysql_query("SELECT * FROM dispatch");
}

function array_status_mo(&$array_id, &$array_name) {
    $array_id = array("PUSHED", "ERROR", "QUEUED");
    $array_name = array("Despachado", "Fallido", "Pendiente");
}

function array_cc(&$array_id, &$array_name) {
    $array_id = array("9998", "9999");
    $array_name = array("9998 - campanas", "9999 - CrediChile");
}
?>

<?php
/*
 * ***************************
  INPUT
 * ***************************
 */
?>

<?php

function input_phone($value) {

    $lengh = $_SESSION["SIZE_NUMBER_MOBILE"];
    $misdn = $_SESSION["PLACEHOLDER_NUMBER_MOBILE"];

    if (empty($value)) {
        $value = '';
    }
    $sizeNumberMobile = 0;
    if ($lengh) {
        $sizeNumberMobile = $lengh;
    }
    $placeholder = "";
    if ($misdn) {
        $placeholder = $misdn;
    }
    echo "<input type=\"text\" id=\"phone\" name=\"phone\" maxlength=\"$sizeNumberMobile\" size=\"20\" value=\"$value\" placeholder=\"Ej:$placeholder\" onblur=\"validaFormatoPhone(this);\" onkeypress=\"return event.keyCode != 13\"> "
    ?>


    <?php
}

function input_phone_confirmed($value) {
    $lengh = $_SESSION["SIZE_NUMBER_MOBILE"];
    $misdn = $_SESSION["PLACEHOLDER_NUMBER_MOBILE"];
    if (empty($value)) {
        $value = '';
    }
    $sizeNumberMobile = 0;
    if ($lengh) {
        $sizeNumberMobile = $lengh;
    }
    $placeholder = "";
    if ($misdn) {
        $placeholder = $misdn;
    }
    echo "<input type=\"text\" id=\"phone\" name=\"phone\" maxlength=\"$sizeNumberMobile\" size=\"15\" value=\"$value\" placeholder=\"Ej:$placeholder\" onblur=\"validaFormatoPhone(this);\" onkeyup=\"checkPhone(this.value);\" onkeypress=\"return event.keyCode != 13\"> "
    ?>
    <?php
}
?>

<?php
/*
 * ***************************
  SELECT
 * ***************************
 */
?>

<?php

function select_input_mode($value) {
    $var = "";
    array_input_mode($id, $var);
    ?>

    <select name="mode">
        <option value="" selected>-- Todos --</option>
        <?php
        for ($i = 0; $i < count($id); $i++) {
            if ($value == $id[$i]) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $id[$i] ?>" <?= $selected ?>><?= $tyoe = return_input_mode_name($id[$i]); ?></option>
            <?php
        }
        ?>
    </select>		
    <?php
}

function select_type_input($value) {
    $id;
    $name;
    array_type_input($id, $name);
    ?>

    <select name="type_input">
        <option value="" selected>-- Todos --</option>
        <?php
        for ($i = 0; $i < count($id); $i++) {
            if ($value == $id[$i]) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $id[$i] ?>" <?= $selected ?>><?= $tyoe = return_type_input_name($id[$i]); ?></option>
            <?php
        }
        ?>
    </select>		
    <?php
}

function select_company($id_company) {
    ?>
    <select name="company">
        <option value="" selected>-- Todos --</option>
        <?php
        $query = " SELECT id, nom_corto, description FROM company; ";

        $sql = mysql_query($query);
        while ($row = mysql_fetch_array($sql)) {
            if ($id_company == $row['id']) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $row['id'] ?>" <?= $selected ?>> <?= $row['nom_corto']; ?></option>
            <?php
        }
        ?>
    </select>
    <?php
}

function select_status($value) {
    $var = "";
    array_status($id, $var);
    ?>
    <select name="status">
        <option value="" selected>-- Todos --</option>
        <?php
        for ($i = 0; $i < count($id); $i++) {
            if ($value == $id[$i]) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $id[$i] ?>" <?= $selected ?>><?= $tyoe = return_status_name($id[$i]); ?></option>
            <?php
        }
        ?>
    </select>

    <?php
}

function select_carrier($value, $company_id) {
    $var = "";
    array_carrier($company_id, $var);
    ?>

    <select name="op">
        <option value="" selected>-- Todos --</option>
        <?php
        while ($row = mysql_fetch_array($company_id)) {
            if ($value == $row['carrier']) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $row['carrier'] ?>" <?= $selected ?>><?= $type = return_carrier_name($row['carrier']); ?></option>
            <?php
        }
        ?>
    </select>

    <?php
}

function select_status_mo($value) {
    array_status_mo($id);
    ?>

    <select name="status">
        <option value="" selected>-- Todos --</option>
        <?php
        for ($i = 0; $i < count($id); $i++) {
            if ($value == $id[$i]) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $id[$i] ?>" <?= $selected ?>><?= $tyoe = return_status_mo_name($id[$i]); ?></option>
            <?php
        }
        ?>
    </select>

    <?php
}

function select_users($value) {
    $company = $_SESSION["id"];
    $login = $_SESSION["user"];
    $role = return_role($login);
    $sql_filtro = "";

    if (select_type_profile($role) != 1) {
        if (select_type_profile($role) != 2) {
            $sql_login = " AND t.login = '$login' ";
            $sql_filtro .= $sql_login;
        }
        $sql_filtro .= " AND a.company = $company ";
    }
    $query = " SELECT a.login "
            . " FROM account a "
            . " INNER JOIN company c ON c.id = a.company "
            . " WHERE 1 "
            . "$sql_filtro";

    $sql = mysql_query($query);
    ?>

    <select name="user" id="user">
        <option value="" selected>-- Todos --</option>
        <?php
        while ($row = mysql_fetch_array($sql)) {
            if ($value == $row['login']) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $row['login'] ?>" <?= $selected ?>> <?= $row['login']; ?></option>
            <?php
        }
        ?>
    </select>
    <?php
}

function select_groups($value) {
    $company = $_SESSION["id"];
    $sql = mysql_query("SELECT pr.id, pr.name, count(pr.id) AS cnt FROM UserGroup us, provGroup pr WHERE pr.id = us.idGroup AND pr.company = $company GROUP by us.idGroup ORDER by pr.name");
    ?>

    <select id="idGroup" name="idGroup">
        <option value="" selected>-- Seleccionar un Grupo --</option>
        <?php
        while ($row = mysql_fetch_array($sql)) {
            if ($value == $row['id']) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $row['id'] ?>" <?= $selected ?>> <?= $row['name']; ?> (<?= $row['cnt'] ?>)</option>
            <?php
        }
        ?>
    </select>
    <?php
}

function select_upload_files($value) {
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    $cc = return_cc_asoc_n($login);
    $input_process = return_input_process($cc);
    echo $role = return_role();

    if ($role == "user") {
        $sql = mysql_query("SELECT * FROM process WHERE input_mode = 'UPLOAD' AND company  = $company ORDER by id DESC");
    } else {
        $sql = mysql_query("SELECT * FROM process WHERE input_mode = 'UPLOAD' AND company  = $company ORDER by id DESC");
    }
    ?>
    <select name="id_process" size="8">
        <?php
        while ($row = mysql_fetch_array($sql)) {
            if ($value == $row['id']) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $row['id'] ?>" <?= $selected ?>> <?= $row['target']; ?></option>
            <?php
        }
        ?>
    </select>
    <?php
}

function select_type_profile($role) {
    $type = 3;
    switch ($role) {
        case "superadmin": $type = 1;
            break;
        case "facturacion": $type = 1;
            break;
        case "admin": $type = 2;
            break;
        case "user": $type = 3;
            break;
        case "webservice": $type = 3;
	    break;
	case "mantencion": $type = 2;
	    break;
        default: $type = 3;
            break;
    }
    return $type;
}

function select_cc($value) {
    array_cc($id);
    ?>

    <select name="cc">
        <option value="" selected>-- Todos --</option>
        <?php
        for ($i = 0; $i < count($id); $i++) {
            if ($value == $id[$i]) {
                $selected = "selected";
            } else {
                $selected = "";
            }
            ?>
            <option value="<?= $id[$i] ?>" <?= $selected ?>><?= $tyoe = return_cc_name($id[$i]); ?></option>
            <?php
        }
        ?>
    </select>

    <?php
}

function select_groups_users($value) {
    $company = $_SESSION["id"];
    $sql = mysql_query("SELECT us.phone, pr.name, us.idGroup FROM UserGroup us, provGroup pr WHERE us.phone = $value AND us.idGroup = pr.id AND us.company = $company AND pr.company = $company");

    if (mysql_num_rows($sql) > 0) {
        ?>

        <br />
        <b>Grupos Asociados</b><br /><br />

        <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
            <tr>
                <th>Asociar</th>
                <th>Grupo</th>
            </tr>
            <?php
            $i = 0;
            while ($row = mysql_fetch_array($sql)) {
                $res = $i % 2;
                if ($res == 0) {
                    $class = "";
                } else {
                    $class = "table-list-tr";
                }
                ?>
                <tr class="<?= $class ?>">
                    <td><input type="checkbox" name="groups" value="<?= $row['idGroup'] . "," ?>" checked></td>
                    <td><?= $row['name']; ?></td>
                </tr>
                <?php
                $i++;
            }
            ?>
        </table>
        <?php
    }
}
?>






