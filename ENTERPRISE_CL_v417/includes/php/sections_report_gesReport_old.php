<?php
include ('dbConn.php');
?>

<?php

//************************ Reporte por Detalle activo************** */
function gesReport() {

    title("Gesti&oacute;n", "Reporte por Detalle.", "gestion");

    $company = $_SESSION["id"];
    $login = $_SESSION["user"];

    $role = return_role($login);

    $sendDateFrom = obtener_parameter("sendDateFrom", "GET");
    $sendDateTo = obtener_parameter("sendDateTo", "GET");
    $carrier = obtener_parameter("op", "GET");
    $input_mode = obtener_parameter("mode", "GET");
    $phone = obtener_parameter("phone", "GET");
    $status = obtener_parameter("status", "GET");
    $cc = obtener_parameter("cc", "GET");
    $user = obtener_parameter("user", "GET");
    $listuser = obtener_parameter("listuser", "GET");
    $selcompany = obtener_parameter("company", "GET");

    if (select_type_profile($role) == 3) {
        $listuser = $login;
    }

    $yF = substr($sendDateFrom, 0, 4);
    $mF = substr($sendDateFrom, 5, 2);
    $dF = substr($sendDateFrom, 8, 2);

    $yT = substr($sendDateTo, 0, 4);
    $mT = substr($sendDateTo, 5, 2);
    $dT = substr($sendDateTo, 8, 2);

    if (empty($dF)) {
        $dF = "01";
    } else {
        $dF = $dF;
    }
    if (empty($mF)) {
        $mF = return_date_now_format('m');
    } else {
        $mF = $mF;
    }
    if (empty($yF)) {
        $yF = return_date_now_format('Y');
    } else {
        $yF = $yF;
    }
    if (empty($dT)) {
        $dT = return_date_now_format('d');
    } else {
        $dT = $dT;
    }
    if (empty($mT)) {
        $mT = return_date_now_format('m');
    } else {
        $mT = $mT;
    }
    if (empty($yT)) {
        $yT = return_date_now_format('Y');
    } else {
        $yT = $yT;
    }
    ?>
    <script>
        function agregarUsuario() {

            var hidden = document.getElementById("hdd-id-usuarios");
            var login = document.getElementById("user").value;
            var table = document.getElementById("table-listUser");
            var existe = false;
            var logins = hidden.value;
            var todos = false;

            if (login == "") {//equivalente a todos
                logins = "";
                login = "todos";
                todos = true;
                table.innerHTML = "";
            } else {
                if (logins.indexOf("todos") != -1) {
                    logins = "";
                    table.innerHTML = "";
                }
            }

            if (logins != null && logins.length > 0) {
                var arr = logins.split("-");
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i] == login) {
                        existe = true;
                    }
                }
            }

            if (!existe) {
                if (logins.length == 0) {
                    logins += login;
                } else {
                    logins += "-" + login;
                }
                var btn = document.createElement("span");
                btn.setAttribute("class", "buttom");
                btn.appendChild(document.createTextNode("X"));
                btn.setAttribute("onclick", "quitarUsuario('" + login + "')");

                //				var table = document.createElement("table");
                var tr = document.createElement("tr");
                tr.setAttribute("id", "tr-" + login);
                var tdTexto = document.createElement("td");
                var tdButon = document.createElement("td");


                tdTexto.appendChild(document.createTextNode(login));
                tdButon.appendChild(btn);
                tr.appendChild(tdTexto);
                tr.appendChild(tdButon);
                table.appendChild(tr);

                //				li.appendChild(table);
                //				ul.appendChild(li);
                hidden.value = logins;
            } else {
                alert("el usuario ya ha sido agregado");
            }
        }

        function quitarUsuario(login) {
            var hidden = document.getElementById("hdd-id-usuarios");
            var table = document.getElementById("table-listUser");
            var logins = hidden.value;
            if (logins != null && logins.length > 0) {
                var arr = logins.split("-");
                var loginsAux = "";
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i] != login) {
                        if (loginsAux.length == 0) {
                            loginsAux = arr[i];
                        } else {
                            loginsAux += "-" + arr[i];
                        }
                    }
                }
                hidden.value = loginsAux;
                table.removeChild(document.getElementById("tr-" + login));
            }
        }

    </script>
    <form action="" name="tmc" method="get" onkeypress="return event.keyCode != 13">
        <input id="hdd-id-usuarios" name="listuser" type="hidden" value="<?= $listuser ?>">
        <input type="hidden" name="seccion" value="gesReport">

        <table width="400" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" height="20">
                    <b>Fecha (Desde)</b>
                </td>
                <td align="left" valign="middle" width="115">
                    <input type="text" value="<?= $yF; ?>-<?= $mF ?>-<?= $dF; ?>" readonly name="sendDateFrom" id="sendDateFrom">
                </td>
                <td align="left"  width="160">
                    <img src="includes/javascript/images/calendar.png" id="lanzador1">
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle" height="20">
                    <b>Fecha (Hasta)</b>
                </td>
                <td align="left" valign="middle" width="115">
                    <input type="text" value="<?= $yT; ?>-<?= $mT; ?>-<?= $dT; ?>" readonly name="sendDateTo" id="sendDateTo"> 
                </td>
                <td align="left"  width="160">
                    <img src="includes/javascript/images/calendar.png" id="lanzador2">
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle" width="120"><b>Celular</b></td>
                <td align="left" valign="middle" colspan="2"><?php input_phone($phone); ?></td>
            </tr>
            <tr>
                <td align="left"><b>Estado: </b></td>
                <td align="left" colspan="2"><?php select_status($status); ?></td>
            </tr>
            <tr>
                <td align="left"><b >Operador: </b></td>
                <td align="left" colspan="2"><?php select_carrier($carrier, $company); ?></td>
            </tr>
            <tr>
                <td align="left"><b >Tipo de Ingreso: </b></td>
                <td align="left" colspan="2"><?php select_input_mode($input_mode); ?></td>
            </tr>

            <?php if (select_type_profile($role) == 1 || select_type_profile($role) == 2) { ?>
                <tr>
                    <td align="left"><b >Usuarios: </b></td>
                    <td align="left" colspan="2">
                        <?php select_users($login); ?>
                        <span id="btnAgregar" class="buttom" onclick="agregarUsuario()" >Agregar</span>
                    </td>
                </tr>
                <tr>
                    <td align="left"><b >Usuarios Seleccionados: </b></td>
                    <td align="left" colspan="2">
                        <table>
                            <tbody id="table-listUser">
                                <?php
                                if (!empty($listuser)) {
                                    $arr = explode("-", $listuser);
                                    for ($i = 0; $i < count($arr); $i++) {
                                        $textUsuario = $arr[$i];
                                        echo "<tr id=\"tr-" . $textUsuario . "\"><td>$textUsuario</td><td><span class=\"buttom\" onclick=\"quitarUsuario('$textUsuario')\">X</span></td></tr>";
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </td>
                </tr>
            <?php } ?>
            <?php if (select_type_profile($role) == 1) { ?>
                <tr>
                    <td align="left"><b >Compa&ntilde;ia: </b></td>
                    <td align="left" colspan="2"><?php select_company($selcompany); ?></td>
                </tr>
            <?php } ?>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30" colspan="2">
                    <span class="buttom" onclick="gesReport();">BUSCAR</span> 
                    <span class="buttom" onclick="reset();">BORRAR</span>
                </td>
            </tr>
        </table>
        <span id="txt-message-js" style="float: left; width: 100%; margin-top: 10px; font-size: 1.5em;"></span>
    </form>
    <br /><br />
    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateFrom", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador1"
        }
        )
    </script>

    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateTo", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador2"
        }
        )
    </script>
    <?php
    $sql_filtro = '';
    if (!empty($sendDateFrom)) {
        $format_date = "Y-m-d H:i:s";
        $sendDateFromGmt = return_date_after_format_gmt("$sendDateFrom 00:00:00", $format_date);
        $sendDateToGmt = return_date_after_format_gmt("$sendDateTo 23:59:59", $format_date);
        $sql_date = " AND t.receivedTime between '$sendDateFromGmt' AND  '$sendDateToGmt' ";
        $sql_filtro .= $sql_date;
    }

    if (!empty($carrier)) {
        $sql_carrier = " AND t.recipientDomain LIKE '%$carrier%' ";
        $sql_filtro .= $sql_carrier;
    }

    if (!empty($input_mode)) {
        $sql_input_mode = " AND t.input_mode = '$input_mode' ";
        $sql_filtro .= $sql_input_mode;
    }

    if (!empty($type_input)) {
        if (strcmp($type_input, "XMLRPC") == 0) {
            $sql_type_input = " AND t.input_mode = '$type_input' ";
            $sql_filtro .= $sql_type_input;
        } else {
            $sql_type_input = " AND  t.input_mode IN ('WEB-IND', 'WEB-GRP', 'UPLOAD' , 'USSD') ";
            $sql_filtro .= $sql_type_input;
        }
    }

    if (!empty($phone)) {
        $sql_phone = " AND t.recipientId = '$phone' ";
        $sql_filtro .= $sql_phone;
    }

    if (!empty($status)) {
        if (strcmp($status, "QUEUED") == 0) {
            $sql_status = " AND t.status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED') ";
        } else {
            $sql_status = " AND t.status = '$status' ";
        }
        $sql_filtro .= $sql_status;
    }

    if (!empty($listuser)) {
        if (strcmp($listuser, "todos") != 0) {
            $arrUsers = explode("-", $listuser);
            $textUsers = "";
            for ($i = 0; $i < count($arrUsers); $i++) {
                if (empty($textUsers)) {
                    $textUsers .= "'$arrUsers[$i]'";
                } else {
                    $textUsers .= ", '$arrUsers[$i]'";
                }
            }
            $sql_user = "  AND t.login in ($textUsers) ";
            $sql_filtro .= $sql_user;
        }
    }

    if (!empty($selcompany)) {
        $sql_filtro .= " AND a.company = $selcompany ";
    }

    //si no es perfil admin
    if (select_type_profile($role) != 1) {
        if (select_type_profile($role) != 2) {
            $sql_login = " AND t.login = '$login' ";
            $sql_filtro .= $sql_login;
        }
        $sql_filtro .= " AND a.company = $company ";
    }

    if (empty($phone)) {
        $text_phone = "Todos";
    } else {
        $text_phone = $phone;
    }

    /*
     * revisar como colocaria el status de pendiente  
     * ya que no retornaria nada hay q modificar funcion
     */
    if (empty($status)) {
        $text_status = "Todos";
    } else {
        $text_status = return_status_name($status);
    }

    if (empty($carrier)) {
        $text_carrier = "Todos";
    } else {
        $text_carrier = return_carrier_name($carrier);
    }

    if (empty($input_mode)) {
        $text_mode = "Todos";
    } else {
        $text_mode = return_input_mode_name($input_mode);
    }

    if (empty($sql_date)) {
        
    } else {
        //$pp=return_date_send('receivedTime');

        $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

        $query = " SELECT substring( date_sub(receivedTime, interval $interval minute) ,1,10) as fecha"
                . " , c.id as id_company "
                . " , c.nom_corto as company "
                . " , count(*) as cnt "
                . " FROM trafficMT t "
                . " INNER JOIN account a on t.login = a.login "
                . " INNER JOIN company c on a.company = c.id "
                . " WHERE 1 $sql_filtro "
                . " GROUP by 1, 2, 3";
        $sql = mysql_query($query);
        $cant = mysql_num_rows($sql);
        if ($sql == null || mysql_num_rows($sql) <= 0) {
            ?>

            <div class="alert_big">
                <img src="images/icons/alert.png" /> No se encontraron resultados
            </div>

            <?php
        } else {
            $params = "";
            $params .= "sendDateFrom=" . $sendDateFrom;
            $params .= "&sendDateTo=" . $sendDateTo;
            $params .= "&op=" . $carrier;
            $params .= "&mode=" . $input_mode;
            $params .= "&status=" . $status;
            $params .= "&phone=" . $phone;
            $params .= "&user=" . $user;
            $params .= "&listuser=" . $listuser;
            $params .= "&cc=" . $cc;
            $params .= "&company=" . $selcompany;
            ?>	
            <div class="alert_big">
                <img src="images/icons/excel.png" /> 
                <a href="ajaxfuncs/gesReportExcel.php?<?= $params; ?>">Generar Reporte</a>
            </div>
            <br>
            <table width="100%" cellpadding="0" cellspacing="0" class="table-list">
                <tr>
                    <th>FECHA</th>
                    <th>COMPA&Ntilde;IA</th>
                    <th>M&Oacute;VIL</th>
                    <th>ESTADO</th>
                    <th>OPERADOR</th>
                    <th>TIPO DE INGRESO</th>
                    <th>TOTAL</th>
                </tr>

                <?php
                $i = 0;
                $total = 0;
                while ($row = mysql_fetch_array($sql)) {

                    $res = $i % 2;
                    if ($res == 0) {
                        $class = "";
                    } else {
                        $class = "table-list-tr";
                    }
                    ?>
                    <tr  class="<?= $class ?>">
                        <td>
                            <?php
                            $fechaParams = substr($row['fecha'], 0, 10);
                            $params = "";
                            $params .= "&date=" . $fechaParams;
                            $params .= "&op=" . $carrier;
                            $params .= "&mode=" . $input_mode;
                            $params .= "&status=" . $status;
                            $params .= "&phone=" . $phone;
                            $params .= "&user=" . $user;
                            $params .= "&listuser=" . $listuser;
                            $params .= "&cc=" . $cc;
                            $params .= "&company=" . $row['id_company'];
                            ?>
                            <a href="?seccion=gesReportDetail<?= $params ?>"  
                               title="Ver Detalle">
                                <b><?= $fechaParams ?></b> 
                                <img src="images/icons/detail.png" />
                            </a>
                        </td>
                        <td><?= $row['company'] ?></td>
                        <td><?= $text_phone ?></td>
                        <td><?= $text_status ?></td>
                        <td><?= $text_carrier ?></td>
                        <td><?= $text_mode ?></td>
                        <td><b><?= $row['cnt'] ?></b></td>
                    </tr>
                    <?php
                    $i++;
                    $total = $row['cnt'] + $total;
                }
                ?>	
                <tr class="tr_total">
                    <th>TOTALES</th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <!-- comentado hasta que se tenga el detalle del operador en mexico
                    -->
                    <th></th>
                    <th class="td_total"><b><?= $total ?></b></th>
                </tr>
            </table>		
            <?php
        }
    }
}
?>



<?php

function gesReportDetail() {

    title("Gestión", "Reporte por Detalle.", "gestion");
    $login = $_SESSION["user"];
    $role = return_role($login);

    $page = obtener_parameter("p", "GET");
    $date = obtener_parameter("date", "GET");
    $carrier = obtener_parameter("op", "GET");
    $input_mode = obtener_parameter("mode", "GET");
    $phone = obtener_parameter("phone", "GET");
    $status = obtener_parameter("status", "GET");
    $cc = obtener_parameter("cc", "GET");
    $user = obtener_parameter("user", "GET");
    $listuser = obtener_parameter("listuser", "GET");
    $selcompany = obtener_parameter("company", "GET");
    if (empty($page))
        $page = 1;
    $rows_for_page = 30;
    $rows_from = (($page * $rows_for_page) - $rows_for_page);
    $company = $_SESSION["id"];

    $sql_filtro = "";
    if (!empty($date)) {
        $format_date = "Y-m-d H:i:s";
        $sendDateFromGmt = return_date_after_format_gmt("$date 00:00:00", $format_date);
        $sendDateToGmt = return_date_after_format_gmt("$date 23:59:59", $format_date);
        $sql_date = " AND receivedTime between '$sendDateFromGmt' AND  '$sendDateToGmt' ";
        $sql_filtro .= $sql_date;
    }

    if (!empty($carrier)) {
        $sql_carrier = " AND t.recipientDomain LIKE '%$carrier%' ";
        $sql_filtro .= $sql_carrier;
    }

    if (!empty($input_mode)) {
        $sql_input_mode = " AND t.input_mode = '$input_mode' ";
        $sql_filtro .= $sql_input_mode;
    }

    if (!empty($type_input)) {
        if (strcmp($type_input, "XMLRPC") == 0) {
            $sql_type_input = " AND t.input_mode = '$type_input' ";
        } else {
            $sql_type_input = " AND t.input_mode IN ('WEB-IND', 'WEB-GRP', 'UPLOAD' , 'USSD') ";
        }
        $sql_filtro .= $sql_type_input;
    }

    if (!empty($phone)) {
        $sql_phone = " AND t.recipientId = '$phone' ";
        $sql_filtro .= $sql_phone;
    }

    if (!empty($status)) {
        if (strcmp($status, "QUEUED") == 0) {
            $sql_status = " AND t.status IN ('QUEUED', 'TRANS', 'PENDING', 'SENDING', 'FORDISPATCH', 'STOPPED', 'DISPATCHED' , 'PROCESANDO' ) ";
        } else {
            $sql_status = " AND t.status = '$status' ";
        }
        $sql_filtro .= $sql_status;
    }
    if (!empty($selcompany)) {
        $sql_filtro .= " AND a.company = $selcompany ";
    }

//	if (!empty($user)) {
//		$sql_user = " AND t.login = '$user' ";
//		$sql_filtro .= $sql_user;
//	}

    if (!empty($listuser)) {
        if (strcmp($listuser, "todos") != 0) {
            $arrUsers = explode("-", $listuser);
            $textUsers = "";
            for ($i = 0; $i < count($arrUsers); $i++) {
                if (empty($textUsers)) {
                    $textUsers .= "'$arrUsers[$i]'";
                } else {
                    $textUsers .= ", '$arrUsers[$i]'";
                }
            }
            $sql_user = "  AND t.login in ($textUsers) ";
            $sql_filtro .= $sql_user;
        }
    }

    //rol no admin
    if (select_type_profile($role) != 1) {
        if (select_type_profile($role) != 2) {
            $sql_login = " AND t.login = '$login' ";
            $sql_filtro .= $sql_login;
        }
        $sql_filtro .= " AND a.company = $company ";
    }

    $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

    $query = " SELECT "
            . " t.recipientId "
            . " , t.company "
            . " , t.recipientDomain "
            . " , t.status "
            . " , date_sub( t.receivedTime, interval $interval minute) AS receivedTime"
            . " , date_sub( t.dispatchTime, interval $interval minute) AS dispatchTime"
            . " , date_sub( t.deliveryTime, interval $interval minute) AS deliveryTime"
            . " , t.input_mode "
            . " , t.login "
            . " , t.msgText "
            . " , t.errText "
            . " , c.nom_corto as company "
            . " FROM trafficMT t"
            . " INNER JOIN account a ON t.login = a.login "
            . " INNER JOIN company c on a.company = c.id "
            . " WHERE 1 "
            . " $sql_filtro ";
    $sql = mysql_query($query . " LIMIT $rows_from, $rows_for_page ");
    $total_rows = mysql_num_rows(mysql_query($query));
    $total_pages = ceil($total_rows / $rows_for_page);

    $url = "?seccion=gesReportDetail&date=$date&op=$carrier&mode=$input_mode&status=$status&phone=$phone&user=$user&cc=$cc";
    $params = "";

    $params .= "&date=$date";
    $params .= "&op=$carrier";
    $params .= "&mode=$input_mode";
    $params .= "&status=$status";
    $params .= "&phone=$phone";
    $params .= "&user=$user";
    $params .= "&listuser=$listuser";
    $params .= "&company=$selcompany";
    ?>
    <div class="alert_big">
        <b><?= $total_rows ?></b> mensajes correspondientes a la fecha: <b><?= $date ?></b>.<br/><br/>
        <img src="images/icons/excel.png" />  
        <a href="ajaxfuncs/gesReportDetailExcel.php?<?= $params; ?>">Generar Reporte</a>
    </div><br>
    <span onclick="javascript:history.back();" class="buttom">VOLVER</span><br><br>
    <br />
    <?php pag_pages($page, $total_pages, $url); ?>
    <br />
    <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
        <tr>
            <th>MÓVIL</th>
            <th>OPERADOR</th>
            <th>ESTADO</th>
            <th>FECHA DE CARGA</th>
            <th>FECHA DE ENV&Iacute;O</th>
            <th>FECHA DE DESPACHO</th>
            <th>TIPO DE ENV&Iacute;O</th>
            <th>INGRESO</th>
            <th>USUARIO</th>
            <th>COMPA&Ntilde;IA</th>
            <th>MENSAJE</th>
        </tr>
        <?php
        $i = 0;
        while ($row = mysql_fetch_array($sql)) {

            $res = $i % 2;

            if ($res == 0) {
                $class = "";
            } else {
                $class = "table-list-tr";
            }

            if ($row['status'] == "NOPUSHED" || $row['status'] == "FAILED" || $row['status'] == "REJECTED") {
                $td = "table-list-td-error";
                $cause = "<br>[Causa: " . $row['errtext'] . "]";
            } else {
                $td = "";
                $cause = "";
            }
            ?>
            <tr class="<?= $class ?>">
                <td class="<?= $td; ?>"><?= $row['recipientId'] ?></td>
                <td class="<?= $td; ?>"><?= $carrier = return_carrier_name($row['recipientDomain']) ?></td>
                <td class="<?= $td; ?>"><?= $status = return_status_name($row['status']) ?></td>
                <td class="<?= $td; ?>"><?= $row['receivedTime'] ?></td>
                <td class="<?= $td; ?>"><?= return_dispatchtime($row['dispatchTime'], $row['receivedTime']) ?></td>
                <td class="<?= $td; ?>"><?= $row['deliveryTime'] ?></td>
                <td class="<?= $td; ?>"><?= $type_send = return_type_send_name_new($row['receivedTime'], $row['dispatchTime']); ?></td>
                <td class="<?= $td; ?>"><?= $input = return_input_mode_name($row['input_mode']) ?></td>
                <td class="<?= $td; ?>"><?= $row['login'] ?></td>
                <td class="<?= $td; ?>"><?= $row['company'] ?></td>
                <td class="<?= $td; ?>"><?= $row['msgText'] ?> <?= $cause ?></td>
            </tr>
            <?php
            $i++;
        }
        ?>
    </table>
    <?php
}
?>


