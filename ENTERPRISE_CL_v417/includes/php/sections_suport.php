<?php
include ('dbConn.php');
?>


<?php

function sopPass() {

    title("Soporte", "Cambio de Password.", "soporte");
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    $id_account = $_SESSION["id_account"];
    ?>
    <script type="text/javascript" src="includes/javascript/app/account/changePass.js?v=<?=VERSION?>"></script>
    <form action="" name="tmc" autocomplete="off"> <!-- onkeypress="return event.keyCode != 13"-->
        <table width="100%" border="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" width="200"><b>Usuario</b></td>
                <td align="left">
                    <input type="hidden" id="hddIdAccount" value="<?= $id_account ?>">
                    <input type="text" id="login" name="login" readonly value="<?= $login ?>">
                </td>
            </tr>
            <tr>
                <td align="left"><b>Actual Password</b></td>
                <td align="left"><input type="password" id="oldpassword" name="oldpassword"  autocomplete="off"></td>
            </tr>
            <tr>
                <td align="left"><b>Nueva Password</b></td>
                <td align="left"><input type="password" id="password1" name="password1"  autocomplete="off"></td>
            </tr>
            <tr>
                <td align="left"><b>Re-Ingrese Nueva Password</b></td>
                <td align="left"><input type="password" id="password2" name="password2"  autocomplete="off"></td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30">
    <!--					<span onclick="sopPass();" class="buttom">Modificar</span>-->
                    <span id="btnModificar" class="buttom">Modificar</span>
                    <span onclick="reset();" class="buttom">Borrar</span>
                </td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30">
                    <br />
                    <div id="loading"></div>
                    <br />
                    <div id="resultado"></div>
                </td>
            </tr>
        </table>
    </form>

    <?php
}
?>

<?php

function sopConf() {

    title("Soporte", "Configuración.", "soporte");
    $login = $_SESSION["user"];
    $idCompany = $_SESSION["id"];

    $sql = mysql_query("SELECT * FROM company WHERE id = $idCompany");
    $row = mysql_fetch_array($sql);
    ?>

    <table width="100%" border="0" cellspacing="2" style="height:250px;" class="table-general">
        <tr>
            <td width="300" align="left"><b>Empresa </b></td>
            <td align="left">: <?= $row['description'] ?></td>
        </tr>
        <tr>
            <td align="left"><b>Máximo de mensajes diarios por usuario </b></td>
            <td align="left">: <?= $row['max_msg'] ?></td>
        </tr>
        <tr>
            <td align="left"><b>Máximo de mensajes duplicados diarios por usuario </b></td>
            <td align="left">: <?= $row['max_dup'] ?></td>
        </tr>
        <tr>
            <td align="left"><b>Largo del Mensaje </b></td>
            <td align="left">: <?= $row['max_char'] ?></td>
        </tr>
        <tr>
            <td align="left"><b>Máximo de registros por archivo de carga </b></td>
            <td align="left">: <?= $row['max_upld'] ?></td>
        </tr>
        <tr>
            <td align="left"><b>Expiración de Sesión Web </b></td>
            <td align="left">: 10 minutos</td>
        </tr>
    </table>

    <?php
}
?>

<?php

function sopEmail() {

    title("Soporte", "Contacto", "soporte");
    $login = $_SESSION["user"];
    ?> 

    <form action="" name="tmc">
        <table width="100%" border="0" cellspacing="2" class="table-general">
            <tr>
                <td width="100"><b>Nombre </b></td>
                <td align="left"><input type="text" name="name" size="50" onkeypress="return event.keyCode != 13"></td>
            </tr>
            <tr>
                <td width="100"><b>Email </b></td>
                <td align="left"><input type="text" name="email" size="40" onkeypress="return event.keyCode != 13"></td>
            </tr> 
            <tr>
                <td width="100"><b>Fono Contacto </b></td>
                <td align="left"><input type="text" name="contact" size="10" onkeypress="return event.keyCode != 13"></td>
            </tr>     
            <tr>
                <td width="100"><b>Asunto </b></td>
                <td align="left"><input type="text" name="subject" size="50" onkeypress="return event.keyCode != 13"></td>
            </tr>
            <tr>
                <td valign="top"><b>Mensaje </b></td>
                <td align="left"><textarea name="msgtext" rows="10" cols="50"></textarea></td>
            </tr>
            <tr>
                <td></td>
                <td valign="bottom" height="30" align="left"><span onclick="sopEmail();" class="buttom">ENVIAR</span> <span onclick="reset();" class="buttom">Borrar</span></td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30"><br /><br /><div id="resultado"></div></td>
            </tr>
        </table>
    </form>

    <?php
}
?>

<?php

function sopManual() {

    title("Soporte", "Manual de Uso", "soporte");
    $login = $_SESSION["user"];
    ?>

    - Descargar <a href="manual_TMC.pdf"><b>Manual de Uso</b></a>


    <?php
}
?>