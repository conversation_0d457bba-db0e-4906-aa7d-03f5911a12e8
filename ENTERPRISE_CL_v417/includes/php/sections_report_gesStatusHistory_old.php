<?php
include ('dbConn.php');
?>



<?php

function gesStatusHist() {


    title("Gesti&oacute;n", "Reporte Consolidado Histórico hasta el 15 de Abril, 2013.", "");
    $login = $_SESSION["user"];
    $role = return_role($login);
    $company = $_SESSION["user"];

    $id = $_SESSION["id"];
    $sendDateFrom = $_GET['sendDateFrom'];
    $sendDateTo = $_GET['sendDateTo'];
    $carrier = $_GET['op'];
    $mode = $_GET['mode'];
    $cc = $_GET['cc'];
    $user = $_GET['user'];

    $yF = substr($sendDateFrom, 0, 4);
    $mF = substr($sendDateFrom, 5, 2);
    $dF = substr($sendDateFrom, 8, 2);

    $yT = substr($sendDateTo, 0, 4);
    $mT = substr($sendDateTo, 5, 2);
    $dT = substr($sendDateTo, 8, 2);

    if (empty($dF)) {
        $dF = "01";
    } else {
        $dF = $dF;
    }
    if (empty($mF)) {
        $mF = return_date_now_format('m');
    } else {
        $mF = $mF;
    }
    if (empty($yF)) {
        $yF = return_date_now_format('Y');
    } else {
        $yF = $yF;
    }
    if (empty($dT)) {
        $dT = return_date_now_format('d');
    } else {
        $dT = $dT;
    }
    if (empty($mT)) {
        $mT = return_date_now_format('m');
    } else {
        $mT = $mT;
    }
    if (empty($yT)) {
        $yT = return_date_now_format('Y');
    } else {
        $yT = $yT;
    }
    ?>

    <form action="" name="tmc" method="get" onkeypress="return event.keyCode != 13">
        <input type="hidden" name="seccion" value="gesStatusHist">

        <table width="400" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Desde)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yF; ?>-<?= $mF ?>-<?= $dF; ?>" readonly name="sendDateFrom" id="sendDateFrom"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador1"></td>
            </tr>
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Hasta)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yT; ?>-<?= $mT ?>-<?= $dT; ?>" readonly name="sendDateTo" id="sendDateTo"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador2"></td>
            </tr>
            <tr>
                <td align="left"><b >Operador: </b></td>
                <td align="left" colspan="2"><?php select_carrier($carrier, $company); ?></td>
            </tr>
            <tr>
                <td align="left"><b >Tipo de Ingreso: </b></td>
                <td align="left" colspan="2"><?php select_input_mode($mode); ?></td>
            </tr>
            <?php if ($role == "admin") { ?>
                <tr>
                    <td align="left"><b >Usuarios: </b></td>
                    <td align="left" colspan="2"><?php select_users($user); ?></td>
                </tr>
            <?php } ?>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30" colspan="2"><span onclick="gesStatus();" class="buttom">Buscar</span> <span onclick="reset();" class="buttom">Borrar</span></td>
            </tr>
        </table>
    </form>
    <br /><br />
    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateFrom", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador1"
        }
        )
    </script>

    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateTo", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador2"
        }
        )
    </script>

    <?php
    if (empty($sendDateFrom)) {
        
    } else {

        if (!empty($sendDateFrom)) {
            $sql_date = "AND timestamp >= '$sendDateFrom 00:00:00' AND timestamp <= '$sendDateTo 23:59:59'";
        }

        if (!empty($carrier)) {
            $sql_carrier = "AND carrier LIKE '%$carrier%'";
        }

        if (!empty($input_mode)) {
            $sql_input_mode = "AND input_mode = '$input_mode'";
        }

        if (!empty($phone)) {
            $sql_phone = "AND phone = '$phone'";
        }

        if (!empty($cc)) {
            $sql_cc = "AND cc = '$cc'";
        }

        if (!empty($status)) {
            if ($status == "QUEUED") {
                $sql_status = "AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED')";
            } else {
                $sql_status = "AND status = '$status'";
            }
        }

        if ($role == "admin") {
            $sql_login = "";
        } else {
            $sql_login = "AND login = '$login'";
        }

        if (!empty($user)) {
            $sql_user = "AND login = '$user'";
        }

        $sql = mysql_query("SELECT substring(timestamp,1,10) as fecha, 
		SUM(case status when 'PUSHED' then 1 else 0 end) as pushed, 
		SUM(case status when 'CONFIRMED' then 1 else 0 end) as confirmed, 
		SUM(case status when 'FAILED' then 1 else 0 end) as failed, 
		SUM(case status when 'NOPUSHED' then 1 else 0 end) as nopushed, 
		SUM(case status when 'QUEUED' then 1 else 0 end or case status when 'TRANS' then 1 else 0 end or case status when 'PENDING' then 1 else 0 end or case status when 'SENDING' then 1 else 0 end or case status when 'PROCESANDO' then 1 else 0 end)  as queued, 
		SUM(case status when 'ANULADO' then 1 else 0 end) as anulado, 
		count(*) as total
		FROM traffic 
		WHERE 1 
		$sql_date
		$sql_carrier 
		$sql_mode 
		$sql_login 
		$sql_user $sql_cc
		GROUP by fecha
		ORDER by fecha ASC");

        $total_rows = mysql_num_rows($sql);
        ?>



        <div class="alert_big">

            <?php if ($total_rows < 1) { ?>

                No se encontraron registros asociados a esta consulta.

            <?php } else { ?>

                <img src="images/icons/excel.png" />  <a href="ajaxfuncs/gesStatusExcelHist.php?sendDateFrom=<?= $sendDateFrom ?>&sendDateTo=<?= $sendDateTo ?>&op=<?= $carrier ?>&mode=<?= $input_mode ?>&status=<?= $status ?>&phone=<?= $phone ?>&user=<?= $user ?>&cc=<?= $cc ?>">Generar Reporte</a>

            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table-list">
                <tr>
                    <th>FECHA</th>
                    <th>DESPACHADO</th>
                    <th>CONFIRMADO</th>
                    <th>FALLIDO</th>
                    <th>RECHAZADO</th>
                    <th>PENDIENTE</th>
                    <th>ANULADO</th>
                    <th>TOTAL</th>
                </tr>
                <?php
                $i = 0;
                while ($row = mysql_fetch_array($sql)) {

                    $res = $i % 2;
                    if ($res == 0) {
                        $class = "";
                    } else {
                        $class = "table-list-tr";
                    }
                    ?>
                    <tr class="<?= $class ?>">
                        <td><a href="?seccion=gesStatusDetailHist&date=<?= $row['fecha']; ?>&carrier=<?= $carrier ?>&mode=<?= $input_mode ?>&user=<?= $user ?>"><b><?= $row['fecha']; ?></b></a></td>
                        <td><?= $row['pushed']; ?></td>
                        <td><?= $row['confirmed']; ?></td>
                        <td><?= $row['failed']; ?></td>
                        <td><?= $row['nopushed']; ?></td>
                        <td><?= $row['queued']; ?></td>
                        <td><?= $row['anulado']; ?></td>
                        <td><?= $row['total']; ?></td>
                    </tr>
                    <?php
                    $i++;
                    $pushed = $row['pushed'] + $pushed;
                    $confirmed = $row['confirmed'] + $confirmed;
                    $failed = $row['failed'] + $failed;
                    $nopushed = $row['nopushed'] + $nopushed;
                    $queued = $row['queued'] + $queued;
                    $anulado = $row['anulado'] + $anulado;
                    $total = $row['total'] + $total;
                }
                ?>	
                <tr>
                    <th>TOTALES</th>
                    <th><?= $pushed ?></th>
                    <th><?= $confirmed ?></th>
                    <th><?= $failed ?></th>
                    <th><?= $nopushed ?></th>
                    <th><?= $queued ?></th>
                    <th><?= $anulado ?></th>
                    <th><?= $total ?></th>
                </tr>
            </table>	



            <?php
        }
    }
}
?>


<?php

function gesStatusDetailHist() {

    title("Gesti&oacute;n", "Reporte Consolidado Histórico hasta el 15 de Abril, 2013.", "");
    $login = $_SESSION["user"];
    $role = return_role($login);

    $company = $_SESSION["id"];
    $date = $_GET['date'];
    $sendDateFrom = $_GET['sendDateFrom'];
    $sendDateTo = $_GET['sendDateTo'];
    $carrier = $_GET['carrier'];
    $mode = $_GET['mode'];
    $user = $_GET['user'];
    $cc = $_GET['cc'];
    $p = $_GET['p'];

    if (!empty($sendDateFrom)) {
        $sql_date = "AND timestamp >= '$sendDateFrom 00:00:00' AND timestamp <= '$sendDateTo 23:59:59'";
    }

    if (!empty($carrier)) {
        $sql_carrier = "AND carrier LIKE '%$carrier%'";
    }

    if (!empty($input_mode)) {
        $sql_input_mode = "AND input_mode = '$input_mode'";
    }

    if (!empty($phone)) {
        $sql_phone = "AND phone = '$phone'";
    }

    if (!empty($cc)) {
        $sql_cc = "AND cc = '$cc'";
    }

    if (!empty($status)) {
        if ($status == "QUEUED") {
            $sql_status = "AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED')";
        } else {
            $sql_status = "AND status = '$status'";
        }
    }

    if ($role == "admin") {
        $sql_login = "";
    } else {
        $sql_login = "AND login = '$login'";
    }

    if (!empty($user)) {
        $sql_user = "AND login = '$user'";
    }

    if (!isset($p)) {
        $page = 1;
    } else {
        $page = $p;
    }

    $rows_for_page = 30;

    $rows_from = (($page * $rows_for_page) - $rows_for_page);

    $sql = mysql_query("SELECT * FROM traffic WHERE substring(timestamp, 1,10) = '$date' $sql_carrier $sql_mode $sql_login $sql_user $sql_cc LIMIT $rows_from, $rows_for_page");

    $total_rows = mysql_num_rows(mysql_query("SELECT * FROM traffic WHERE substring(timestamp, 1,10) = '$date' $sql_carrier $sql_mode  $sql_login $sql_user $sql_cc"));

    $total_pages = ceil($total_rows / $rows_for_page);

    if ($total_rows < 1) {
        ?>

        <div class="alert_big">No se encontraron registros asociados a esta consulta.</div>

    <?php } else { ?>
        <?php $url = "?seccion=gesStatusDetailHist&date=$date&carrier=$carrier&mode=$mode&user=$user&cc=$cc"; ?>

        <div class="alert_big"><b><?= $total_rows ?></b> mensajes correspondientes a la fecha: <b><?= $date ?></b>.<br /><br />
            <img src="images/icons/excel.png" /><a href="ajaxfuncs/gesStatusDetailExcelHist.php?date=<?= $date ?>&carrier=<?= $carrier ?>&mode=<?= $mode ?>&user=<?= $user ?>&cc=<?= $cc ?>" target="_blank">Generar Reporte</a></div><br>
        <span onclick="history.back();" class="buttom">VOLVER</span><br><br>

        <?php pag_pages($page, $total_pages, $url); ?>

        <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
            <tr>
                <th>M&Oacute;VIL</th>
                <th>OPERADOR</th>
                <th>ESTADO</th>
                <th>FECHA DE ENV&Iacute;O</th>
                <th>FECHA DE DESPACHO</th>
                <th>FECHA DE CREACI&Oacute;N</th>
                <th>TIPO DE ENV&Iacute;O</th>
                <th>INGRESO</th>
                <th>USUARIO</th>
                <th>MENSAJE</th>  
            </tr>

            <?php
            $i = 0;
            while ($row = mysql_fetch_array($sql)) {

                $res = $i % 2;

                if ($res == 0) {
                    $class = "";
                } else {
                    $class = "table-list-tr";
                }

                if ($row['status'] == "NOPUSHED" || $row['status'] == "FAILED" || $row['status'] == "REJECTED") {
                    $td = "table-list-td-error";
                    $delivery_text = $row['delivery_text'];
                    if ($row['status'] == "FAILED" && empty($delivery_text))
                        $delivery_text = "El Operador no pudo entregar el mensaje.";
                    $cause = "<br>[Causa: " . $delivery_text . "]";
                } else {
                    $td = "";
                    $cause = "";
                }

                $dateSend = return_dispatchtime($row['dispatchtime'], $row['timestamp']);
                ?>

                <tr class="<?= $class ?>">
                    <td class="<?= $td; ?>"><?= $row['phone'] ?></td>
                    <td class="<?= $td; ?>"><?= $carrier = return_carrier_name($row['carrier']) ?></td>
                    <td class="<?= $td; ?>"><?= $status = return_status_name($row['status']) ?></td>
                    <td class="<?= $td; ?>"><?= $dateSend = return_date_send($dateSend) ?></td>
                    <td class="<?= $td; ?>"><?= return_date_send($row['deliverytime']) ?></td>
                    <td class="<?= $td; ?>"><?= return_date_send($row['timestamp']) ?></td>
                    <td class="<?= $td; ?>"><?= $type_send = return_type_send_name($row['dispatchtime']); ?></td>
                    <td class="<?= $td; ?>"><?= $input = return_input_mode_name($row['input_mode']) ?></td>
                    <td class="<?= $td; ?>"><?= $row['login'] ?></td>
                    <td class="<?= $td; ?>"><?= $row['msgtext'] ?> <?= $cause ?></td>
                </tr>
                <?php
                $i++;
            }
            ?>
        </table>
        <?php
    }
}
?>