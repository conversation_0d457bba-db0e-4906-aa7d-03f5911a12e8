<?php

function gesReportQueueReport() {
    title("Cola de Generacion de Reportes", "Listado Reporte", "campa&ntilde;as");
    $idAccount = $_SESSION["id_account"];
    $idCompany = $_SESSION["id"];
    ?>
    <script type="text/javascript" src="includes/javascript/app/report/queueReportDetail.js?v=<?=VERSION?>"></script>
    <input id="hddAccountId" type="hidden" value="<?= $idAccount ?>">
    <input id="hddCompanyId" type="hidden" value="<?= $idCompany ?>">
    <input id="hddReLoadTable" type="hidden" value="1">
    <div id="div-filter">
        <fieldset class="espaciadoObjetos">
            <legend>Filtros de cola de reportes</legend>
            <table style="width: 100%">
                <tr>
                    <td class="td20">Desde</td>
                    <td class="td30">
                        <input id="txtDateFrom-filter" class="ctlCalendar" value="2020-01-01 00:00:00">
                        <img src="includes/javascript/images/calendar.png" id="btnDateFrom-filter">
                    </td>
                    <td class="td20">Hasta</td>
                    <td class="td30">
                        <input id="txtDateTo-filter" class="ctlCalendar" value="2020-12-31 23:59:59">
                        <img src="includes/javascript/images/calendar.png" id="btnDateTo-filter">
                    </td>
                </tr>
                <tr>
                    <td class="td20">Compañia</td>
                    <td class="td30"><select id="cboCompany-filter" class="ctlSelect"></select></td>
                    <td class="td20">Usuario</td>
                    <td class="td30"><select id="cboAccount-filter" class="ctlSelect"></select></td>
                </tr>
                <tr>
                    <td class="td20">Data</td>
                    <td class="td30"><input id="txtData-filter" class="ctlInput"></td>
                    <td class="td20">Status</td>
                    <td class="td30"><select id="cboStatus-filter" class="ctlSelect"></select></td>
                </tr>
            </table>
        </fieldset>
        <div style="width: 100%;height: 3em;" class="espaciadoObjetos" >
            <table align="right" >
                <tr>
                    <td>
                        <button id="btnSearch-filter" type="button" >Buscar</button>
                    </td>
                </tr>
            </table>
        </div>
        <div id="divMessage" style="width: 100%">
        </div>
        <div id="divImgLoading">
            <img id="imgLoading" src="images/waiting_small.gif">
        </div>
    </div>
    
    <div id="divTable">
        <input id="hddPage" type="hidden" value="1"/>
        <table class="ctlFull">
            <tr>
                <td>
                    <div style="margin: 0 auto; width: 270px;">
                        <div style="display: inline-block;" >
                            <span id="btnBackPage" class="buttom"><</span>
                        </div>
                        <div id="divPages" style="display: inline-block;">
                            <span class="buttom">1</span>
                        </div>
                        <div style="display: inline-block;">
                            <span id="btnNextPage" class="buttom">></span>
                        </div>
                        <div style="display: inline-block;">
                            <select id="cboRowsPage">
                                <option value="2">2</option>
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="20">20</option>
                            </select>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <div class="containerTable">
            <table style="width: 100%" class="tableReport espaciadoObjetos" >
                <thead id="headTable" class="theadTable">
                    <!--<tr>    
                        <th style="min-width: 100px;max-width: 100px;">CODE</th>
                        <th style="min-width: 150px;max-width: 150px;">USUARIO</th>
                        <th style="min-width: 200px;max-width: 200px;">FECHA</th>
                        <th style="min-width: 100px;max-width: 100px;">ESTADO</th>
                        <th style="min-width: 150px;max-width: 150px;">PROGRESO</th>
                        <th style="min-width: 100px;max-width: 100px;">ACCION</th>
                    </tr>-->
                </thead>
                <tbody id="bodyTable" class="tbodyTable">
                    
                </tbody>
            </table>
        </div>

    </div>
    <?php
}
?>

