<?php
include ('dbConn.php');
?>

<?php

function gesFileReport() {

    $company = $_SESSION["id"];
    title("Gestión", "Reporte por Archivo Diario", "gestion");
    $login = $_SESSION["user"];
    $role = return_role($login);

    $sendDateFrom = $_GET['sendDateFrom'];
    $sendDateTo = $_GET['sendDateTo'];
    $recipientDomain = $_GET['op'];
    $input_mode = $_GET['mode'];
    $recipientId = $_GET['recipientId'];
    $status = $_GET['status'];
    $user = $_GET['user'];

    $yF = substr($sendDateFrom, 0, 4);
    $mF = substr($sendDateFrom, 5, 2);
    $dF = substr($sendDateFrom, 8, 2);

    $yT = substr($sendDateTo, 0, 4);
    $mT = substr($sendDateTo, 5, 2);
    $dT = substr($sendDateTo, 8, 2);

    if (empty($dF)) {
        $dF = "01";
    } else {
        $dF = $dF;
    }
    if (empty($mF)) {
        $mF = return_date_now_format('m');
    } else {
        $mF = $mF;
    }
    if (empty($yF)) {
        $yF = return_date_now_format('Y');
    } else {
        $yF = $yF;
    }
    if (empty($dT)) {
        $dT = return_date_now_format('d');
    } else {
        $dT = $dT;
    }
    if (empty($mT)) {
        $mT = return_date_now_format('m');
    } else {
        $mT = $mT;
    }
    if (empty($yT)) {
        $yT = return_date_now_format('Y');
    } else {
        $yT = $yT;
    }
    ?>

    <form action="" name="tmc" method="get" onkeypress="return event.keyCode != 13">
        <input type="hidden" name="seccion" value="gesFileReport">

        <table width="400" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Desde)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yF; ?>-<?= $mF ?>-<?= $dF; ?>" readonly name="sendDateFrom" id="sendDateFrom"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador1"></td>
            </tr>
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Hasta)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yT; ?>-<?= $mT; ?>-<?= $dT; ?>" readonly name="sendDateTo" id="sendDateTo"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador2"></td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30" colspan="2"><span onclick="gesFileReport();" class="buttom">CONSULTAR</span> <span onclick="reset();"  class="buttom">Borrar</span></td>
            </tr>
        </table>
    </form>
    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateFrom", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador1"
        }
        )
    </script>

    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateTo", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador2"
        }
        )
    </script>
    <br />

    <?php
    if ($role == "admin") {
        $sql_login = "";
    } else {
        $sql_login = "AND trafficMT.login = '$login'";
    }

    if (!empty($sendDateFrom)) {
        $sql_date = " process.timestamp between '$sendDateFrom 00:00:00' AND  '$sendDateTo 23:59:59' ";
    } else {
        $sql_date = "";
    }



    if (empty($sql_date)) {
        
    } else {
        $sql = mysql_query("
		SELECT process.target as filename, count(*) as cnt, process.timestamp as Fecha, process.id, process.login, 
		SUM(case trafficMT.status when 'QUEUED' then 1 else 0 end or case trafficMT.status when 'PENDING' then 1 else 0 end or case trafficMT.status when 'TRANS' then 1 else 0 end or case trafficMT.status when 'SENDING' then 1 else 0 end) as pending, 
		SUM(case trafficMT.status when 'PUSHED' then 1 else 0 end) as pushed,
		SUM(case trafficMT.status when 'CONFIRMED' then 1 else 0 end) as confirmed, 
		SUM(case trafficMT.status when 'FAILED' then 1 else 0 end or case trafficMT.status when 'EXPIRED' then 1 else 0 end) as failed,
		SUM(case trafficMT.status when 'NOPUSHED' then 1 else 0 end) as nopushed, 
		SUM(case trafficMT.status when 'ANULADO' then 1 else 0 end) as anulado, 
		SUM(case trafficMT.status when 'NOPUSHED' then 1 else 0 end) as nopushed 
		FROM trafficMT inner join process on trafficMT.input_process = process.id WHERE $sql_date $sql_login 
		AND trafficMT.input_mode = 'UPLOAD' 
		GROUP by 1 
		ORDER by Fecha DESC") or die(mysql_error());
        ?>
        <?php if (mysql_num_rows($sql) <= 0) { ?>

            <div class="alert_big"><img src="images/icons/alert.png" /> No se encontraron resultados</div>

        <?php } else { ?>	
            <div class="alert_big"><img src="images/icons/excel.png" /> <a href="ajaxfuncs/gesFileReport.php?sendDateFrom=<?= $sendDateFrom ?>&sendDateTo=<?= $sendDateTo ?>"><b>Generar Reporte</b></a></div><br>

            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
                <tr>
                    <th>FECHA</th>
                    <th>PENDIENTES</th>
                    <th>DESPACHADOS</th>
                    <th>CONFIRMADOS</th>
                    <th>FALLIDOS</th>
                    <th>RECHAZADOS</th>
                    <th>ANULADOS</th>
                    <th>USUARIO</th>
                    <th>ARCHIVO</th>
                    <th>TOTAL BASE</th>
                </tr>

                <?php
                $i = 0;
                while ($row = mysql_fetch_array($sql)) {

                    $res = $i % 2;
                    if ($res == 0) {
                        $class = "";
                    } else {
                        $class = "table-list-tr";
                    }
                    ?>

                    <tr  class="<?= $class ?>">
                        <td><a href="?seccion=gesFileReportDetail&id_process=<?= $row['id'] ?>" title="Ver Detalle"> <?= $row['Fecha'] ?></a></td>
                        <td><?= $row['pending'] ?></td>
                        <td><?= $row['pushed'] ?></td>
                        <td><?= $row['confirmed'] ?></td>
                        <td><?= $row['failed'] ?></td>
                        <td><?= $row['nopushed'] ?></td>
                        <td><?= $row['anulado'] ?></td>
                        <td><?= $row['login'] ?></td>
                        <td><?= $row['filename'] ?></td>
                        <td><b><?= $row['cnt'] ?></b></td>
                    </tr>
                    <?php $i++; ?> 
                <?php } ?>	
            </table>		
            <?php
        }
    }
}
?>

<?php

function gesFileReportDetail() {

    title("Gestión", "Reporte por Archivo Diario", "por Archivo");
    $login = $_SESSION["user"];
    ?>

    <?php
    $company = $_SESSION["id"];
    $id_process = $_GET['id_process'];
    $p = $_GET['p'];
    //$role					= return_type_role();
    //if(!empty($user)) { $sql_admin_login = "AND login = '$user'"; }

    if (!isset($p)) {
        $page = 1;
    } else {
        $page = $p;
    }

    $rows_for_page = 30;

    $rows_from = (($page * $rows_for_page) - $rows_for_page);

    //$sql_cc_asoc    = return_cc_asoc ();
    //$sql_login    = return_role ();
    //$sql = mysql_query("SELECT * FROM trafficMT WHERE company = $company AND substring(receivedTime,1,10) = '$date' $sql_recipientDomain $sql_input_mode $sql_recipientId $sql_status $sql_codigo $sql_cc $sql_row_id $sql_cc_asoc $sql_login $sql_admin_login"); 

    if (!empty($id_process)) {

        $sql = mysql_query("SELECT * FROM trafficMT WHERE input_process =  $id_process LIMIT $rows_from, $rows_for_page");

        $total_rows = mysql_num_rows(mysql_query("SELECT * FROM trafficMT WHERE input_process = $id_process"));

        $total_pages = ceil($total_rows / $rows_for_page);

        if ($total_rows < 1) {
            ?>

            <div class="alert_big">No se encontraron registros asociados a esta consulta.</div>
        <?php } else { ?>
            <?php $url = "?seccion=gesFileReportDetail&id_process=$id_process"; ?>

            <div class="alert_big"><b><?= $total_rows ?></b> mensajes correspondientes al archivo seleccionado.<br /><br />
                <img src="images/icons/excel.png" /><a href="ajaxfuncs/gesFileReportDetail.php?&id_process=<?= $id_process ?>" target="_blank"><b> Generar Reporte</b></a></div><br>
            <span onclick="history.back();" class="buttom">Volver</span><br><br>

            <?php pag_pages($page, $total_pages, $url); ?>
            <br />

            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
                <tr>
                    <?php /* if ($role == "admin") 
                      { */
                    ?>
                    <th>MÓVIL</th>
                    <th>ID</th>
                    <th>OPERADOR</th>
                    <?php
                    /* }
                      else
                      {
                      } */
                    ?>
                    <th>ESTADO</th>
                    <th>FECHA DE ENVÍO</th>
                    <th>FECHA DE DESPACHO</th>
                    <th>FECHA DE CREACI&Oacute;N</th>
                    <th>TIPO DE ENVÍO</th>
                    <th>INGRESO</th>
                    <th>USUARIO</th>
                    <th>MENSAJE</th>
                </tr>

                <?php
                $i = 0;
                while ($row = mysql_fetch_array($sql)) {

                    $res = $i % 2;

                    if ($res == 0) {
                        $class = "";
                    } else {
                        $class = "table-list-tr";
                    }

                    if ($row['status'] == "NOPUSHED" || $row['status'] == "FAILED" || $row['status'] == "REJECTED") {
                        $td = "table-list-td-error";
                        $cause = "<br>[Causa: " . $row['delivery_text'] . "]";
                    } else {
                        $td = "";
                        $cause = "";
                    }



                    $dateSend = return_dispatchTime($row['dispatchtime'], $row['timestamp']);
                    ?>


                    <tr class="<?= $class ?>">
                        <?php /* if ($role == "admin") { */ ?> <td class="<?= $td ?>"><?= $row['phone'] ?></td>
                        <td class="<?= $td ?>"><?= $row['id'] ?></td>
                        <td class="<?= $td ?>"><?= $recipientDomain = return_carrier_name($row['carrier']) ?></td><?php /* } else { } */ ?>
                        <td class="<?= $td ?>"><?= $status = return_status_name($row['status']) ?></td>
                        <td class="<?= $td ?>"><?= $dateSend = return_date_send($dateSend) ?></td>
                        <td class="<?= $td ?>"><?= return_date_send($row['deliverytime']) ?></td>
                        <td class="<?= $td ?>"><?= return_date_send($row['timestamp']) ?></td>
                        <td class="<?= $td ?>"><?= $type_send = return_type_send_name($row['dispatchtime']); ?></td>
                        <td class="<?= $td ?>"><?= $input = return_input_mode_name($row['input_mode']) ?></td>
                        <td class="<?= $td ?>"><?= $row['login'] ?></td>
                        <td class="<?= $td ?>"><?= $row['msgtext'] ?> <?= $cause ?></td>
                    </tr>
                    <?php
                    $i++;
                }
                ?>
            </table>
            <?php
        }
    }
}
?> 