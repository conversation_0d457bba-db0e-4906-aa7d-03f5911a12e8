<?php
include ('dbConn.php');
?>

<?php

function gesReportHist() {

    title("Gesti&oacute;n", "Reporte por Detalle Histórico hasta el 15 de Abril, 2013.", "gestion");

    $company = $_SESSION["id"];
    $login = $_SESSION["user"];
    $role = return_role($login);

    $sendDateFrom = $_GET['sendDateFrom'];
    $sendDateTo = $_GET['sendDateTo'];
    $carrier = $_GET['op'];
    $input_mode = $_GET['mode'];
    $phone = $_GET['phone'];
    $status = $_GET['status'];
    $cc = $_GET['cc'];
    $user = $_GET['user'];

    $yF = substr($sendDateFrom, 0, 4);
    $mF = substr($sendDateFrom, 5, 2);
    $dF = substr($sendDateFrom, 8, 2);

    $yT = substr($sendDateTo, 0, 4);
    $mT = substr($sendDateTo, 5, 2);
    $dT = substr($sendDateTo, 8, 2);

    if (empty($dF)) {
        $dF = "01";
    } else {
        $dF = $dF;
    }
    if (empty($mF)) {
        $mF = return_date_now_format('m');
    } else {
        $mF = $mF;
    }
    if (empty($yF)) {
        $yF = return_date_now_format('Y');
    } else {
        $yF = $yF;
    }
    if (empty($dT)) {
        $dT = return_date_now_format('d');
    } else {
        $dT = $dT;
    }
    if (empty($mT)) {
        $mT = return_date_now_format('m');
    } else {
        $mT = $mT;
    }
    if (empty($yT)) {
        $yT = return_date_now_format('Y');
    } else {
        $yT = $yT;
    }
    ?>

    <form action="" name="tmc" method="get" onkeypress="return event.keyCode != 13">
        <input type="hidden" name="seccion" value="gesReportHist">

        <table width="400" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Desde)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yF; ?>-<?= $mF ?>-<?= $dF; ?>" readonly name="sendDateFrom" id="sendDateFrom"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador1"></td>
            </tr>
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Hasta)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yT; ?>-<?= $mT; ?>-<?= $dT; ?>" readonly name="sendDateTo" id="sendDateTo"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador2"></td>
            </tr>
            <tr>
                <td align="left" valign="middle" width="120"><b>Celular</b></td>
                <td align="left" valign="middle" colspan="2"><?php input_phone($phone); ?></td>
            </tr>
            <tr>
                <td align="left"><b>Estado: </b></td>
                <td align="left" colspan="2"><?php select_status($status); ?></td>
            </tr>
            <tr>
                <td align="left"><b >Operador: </b></td>
                <td align="left" colspan="2"><?php select_carrier($carrier, $company); ?></td>
            </tr>
            <tr>
                <td align="left"><b >Tipo de Ingreso: </b></td>
                <td align="left" colspan="2"><?php select_input_mode($input_mode); ?></td>
            </tr>
            <?php if ($role == "admin") { ?>
                <tr>
                    <td align="left"><b >Usuarios: </b></td>
                    <td align="left" colspan="2"><?php select_users($user); ?></td>
                </tr>
            <?php } ?>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30" colspan="2"><span class="buttom" onclick="gesReport();">BUSCAR</span> <span class="buttom" onclick="reset();">BORRAR</span></td>
            </tr>
        </table>
    </form>
    <br /><br />
    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateFrom", range: [2012, 2013],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador1"
        }
        )
    </script>

    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateTo", range: [2012, 2013],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador2"
        }
        )
    </script>
    <?php
    if (!empty($sendDateFrom)) {
        $sql_date = "AND timestamp >= '$sendDateFrom 00:00:00' AND timestamp <= '$sendDateTo 23:59:59'";
    }

    if (!empty($carrier)) {
        $sql_carrier = "AND carrier LIKE '%$carrier%'";
    }

    if (!empty($input_mode)) {
        $sql_input_mode = "AND input_mode = '$input_mode'";
    }

    if (!empty($phone)) {
        $sql_phone = "AND phone = '$phone'";
    }

    if (!empty($cc)) {
        $sql_cc = "AND cc = '$cc'";
    }

    if (!empty($status)) {
        if ($status == "QUEUED") {
            $sql_status = "AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED')";
        } else {
            $sql_status = "AND status = '$status'";
        }
    }

    if ($role == "admin") {
        $sql_login = "";
    } else {
        $sql_login = "AND login = '$login'";
    }

    if (!empty($user)) {
        $sql_user = "AND login = '$user'";
    }

    if (empty($phone)) {
        $text_phone = "Todos";
    } else {
        $text_phone = $phone;
    }

    if (empty($status)) {
        $text_status = "Todos";
    } else {
        $text_status = return_status_name($status);
    }

    if (empty($carrier)) {
        $text_carrier = "Todos";
    } else {
        $text_carrier = return_carrier_name($carrier);
    }

    if (empty($input_mode)) {
        $text_mode = "Todos";
    } else {
        $text_mode = return_input_mode_name($input_mode);
    }


    if (empty($sql_date)) {
        
    } else {

        $sql = mysql_query("SELECT substring(timestamp,1,10) as fecha, count(*) as cnt FROM traffic WHERE 1 $sql_date $sql_phone $sql_status $sql_carrier $sql_input_mode $sql_login $sql_user $sql_cc GROUP by fecha");

        if (mysql_num_rows($sql) <= 0) {
            ?>

            <div class="alert_big"><img src="images/icons/alert.png" /> No se encontraron resultados</div>

        <?php } else { ?>	
            <div class="alert_big"><img src="images/icons/excel.png" /> <a href="ajaxfuncs/gesReportExcelHist.php?sendDateFrom=<?= $sendDateFrom ?>&sendDateTo=<?= $sendDateTo ?>&op=<?= $carrier ?>&mode=<?= $input_mode ?>&status=<?= $status ?>&phone=<?= $phone ?>&user=<?= $user ?>&cc=<?= $cc ?>">Generar Reporte</a></div><br>
            <table width="100%" cellpadding="0" cellspacing="0" class="table-list">
                <tr>
                    <th>FECHA</th>
                    <th>M&Oacute;VIL</th>
                    <th>ESTADO</th>
                    <th>OPERADOR</th>
                    <th>TIPO DE INGRESO</th>
                    <th>TOTAL</th>
                </tr>

                <?php
                $i = 0;
                while ($row = mysql_fetch_array($sql)) {

                    $res = $i % 2;
                    if ($res == 0) {
                        $class = "";
                    } else {
                        $class = "table-list-tr";
                    }
                    ?>

                    <tr class="<?= $class ?>">
                        <td class="<?= $td; ?>"><?= $row['recipientId'] ?></td>
                        <td class="<?= $td; ?>"><?= $carrier = return_carrier_name($row['recipientDomain']) ?></td>
                        <td class="<?= $td; ?>"><?= $status = return_status_name($row['status']) ?></td>
                        <td class="<?= $td; ?>"><?= return_date_send($row['receivedTime']) ?></td>
                        <td class="<?= $td; ?>"><?= $dateSend = return_date_send($dateSend) ?></td>
                        <td class="<?= $td; ?>"><?= return_date_send($row['deliveryTime']) ?></td>     	
                        <td class="<?= $td; ?>"><?= $type_send = return_type_send_name($row['dispatchTime']); ?></td>
                        <td class="<?= $td; ?>"><?= $input = return_input_mode_name($row['input_mode']) ?></td>
                        <td class="<?= $td; ?>"><?= $row['login'] ?></td>
                        <td class="<?= $td; ?>"><?= $row['msgText'] ?> <?= $cause ?></td>
                    </tr>
                    <?php
                    $i++;
                    $total = $row['cnt'] + $total;
                }
                ?>	
                <tr class="tr_total">
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="td_total"><b><?= $total ?></b></td>
                </tr>
            </table>		
            <?php
        }
    }
}
?>

<?php

function gesReportDetailHist() {

    title("Gestión", "Reporte por Detalle Histórico hasta el 15 de Abril, 2013.", "gestion");
    $login = $_SESSION["user"];

    $role = return_role($login);

    if (!isset($_GET['p'])) {
        $page = 1;
    } else {
        $page = $_GET['p'];
    }

    $rows_for_page = 30;

    $rows_from = (($page * $rows_for_page) - $rows_for_page);


    $company = $_SESSION["id"];
    $date = $_GET['date'];
    $carrier = $_GET['op'];
    $input_mode = $_GET['mode'];
    $phone = $_GET['phone'];
    $status = $_GET['status'];
    $cc = $_GET['cc'];
    $user = $_GET['user'];

    if (!empty($sendDateFrom)) {
        $sql_date = "AND timestamp >= '$sendDateFrom 00:00:00' AND timestamp <= '$sendDateTo 23:59:59'";
    }

    if (!empty($carrier)) {
        $sql_carrier = "AND carrier LIKE '%$carrier%'";
    }

    if (!empty($input_mode)) {
        $sql_input_mode = "AND input_mode = '$input_mode'";
    }

    if (!empty($phone)) {
        $sql_phone = "AND phone = '$phone'";
    }

    if (!empty($cc)) {
        $sql_cc = "AND cc = '$cc'";
    }

    if (!empty($status)) {
        if ($status == "QUEUED") {
            $sql_status = "AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED')";
        } else {
            $sql_status = "AND status = '$status'";
        }
    }

    if ($role == "admin") {
        $sql_login = "";
    } else {
        $sql_login = "AND login = '$login'";
    }

    if (!empty($user)) {
        $sql_user = "AND login = '$user'";
    }

    $sql = mysql_query("SELECT * FROM traffic WHERE substring(timestamp,1,10) = '$date' $sql_carrier $sql_input_mode $sql_phone $sql_status $sql_login $sql_user $sql_cc LIMIT $rows_from, $rows_for_page ");

    $total_rows = mysql_num_rows(mysql_query("SELECT * FROM traffic WHERE substring(timestamp,1,10) = '$date' $sql_carrier $sql_input_mode $sql_phone $sql_status $sql_login $sql_user $sql_cc"));

    $total_pages = ceil($total_rows / $rows_for_page);

    $url = "?seccion=gesReportDetail&date=$date&op=$carrier&mode=$input_mode&status=$status&phone=$phone&user=$user&cc=$cc";
    ?>


    <div class="alert_big"><b><?= $total_rows ?></b> mensajes correspondientes a la fecha: <b><?= $date ?></b>.<br /><br />
        <img src="images/icons/excel.png" />  <a href="ajaxfuncs/gesReportDetailExcelHist.php?date=<?= $date ?>&op=<?= $carrier ?>&mode=<?= $input_mode ?>&status=<?= $status ?>&phone=<?= $phone ?>&user=<?= $user ?>">Generar Reporte</a></div><br>
    <span onclick="javascript:history.back();" class="buttom">VOLVER</span><br><br>
    <br />
    <?php pag_pages($page, $total_pages, $url); ?>
    <br />
    <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
        <tr>
            <th>MÓVIL</th>
            <th>OPERADOR</th>
            <th>ESTADO</th>
            <th>FECHA DE ENV&Iacute;O</th>
            <th>FECHA DE DESPACHO</th>
            <th>FECHA DE CREACI&Oacute;N</th>
            <th>TIPO DE ENV&Iacute;O</th>
            <th>INGRESO</th>
            <th>MENSAJE</th>
        </tr>

        <?php
        $i = 0;
        while ($row = mysql_fetch_array($sql)) {

            $res = $i % 2;

            if ($res == 0) {
                $class = "";
            } else {
                $class = "table-list-tr";
            }

            if ($row['status'] == "NOPUSHED" || $row['status'] == "FAILED" || $row['status'] == "REJECTED") {
                $td = "table-list-td-error";
                $delivery_text = $row['delivery_text'];
                if ($row['status'] == "FAILED" && empty($delivery_text))
                    $delivery_text = "El Operador no pudo entregar el mensaje.";
                $cause = "<br>[Causa: " . $delivery_text . "]";
            } else {
                $td = "";
                $cause = "";
            }

            $dateSend = return_dispatchtime($row['dispatchtime'], $row['timestamp']);
            ?>

            <tr class="<?= $class ?>">
                <td class="<?= $td; ?>"><?= $row['phone'] ?></td>
                <td class="<?= $td; ?>"><?= $carrier = return_carrier_name($row['carrier']) ?></td>
                <td class="<?= $td; ?>"><?= $status = return_status_name($row['status']) ?></td>
                <td class="<?= $td; ?>"><?= $dateSend = return_date_send($dateSend) ?></td>
                <td class="<?= $td; ?>"><?= return_date_send($row['deliverytime']) ?></td>
                <td class="<?= $td; ?>"><?= return_date_send($row['timestamp']) ?></td>
                <td class="<?= $td; ?>"><?= $type_send = return_type_send_name($row['dispatchtime']); ?></td>
                <td class="<?= $td; ?>"><?= $input = return_input_mode_name($row['input_mode']) ?></td>
                <td class="<?= $td; ?>"><?= $row['msgtext'] ?> <?= $cause ?></td>
            </tr>
            <?php
            $i++;
        }
        ?>
    </table>
    <?php
}
?>