<?php

function return_input_process($cc) {
    $sql = mysql_query("SELECT input_process FROM traffic WHERE cc IN ($cc) GROUP by input_process");

    while ($row = mysql_fetch_array($sql)) {
        $input_process .= $row['input_process'] . ",";
    }

    $input_process = substr($input_process, 0, strlen($input_process) - 1);

    if (!empty($input_process)) {
        $input_process = $input_process;
    } else {
        $input_process = "";
    }

    return $input_process;
}

function return_cc_asoc_n($login) {
    $sql = mysql_query("SELECT group_concat(cc separator ',') as costo FROM UserCCosto WHERE user = '$login'");
    $row = mysql_fetch_array($sql);
    $cc = $row['costo'];

    if (!empty($cc)) {
        $cc = $cc;
    } else {
        $cc = "";
    }
    return $cc;
}

function return_cc_by_login($login) {
    $sql = mysql_query("SELECT cc as costo FROM UserCCosto WHERE user = '$login'");
    $row = mysql_fetch_array($sql);
    $cc = $row['costo'];

    if (!empty($cc)) {
        $cc = $cc;
    } else {
        $cc = "";
    }

    return $cc;
}

function return_cc_exist($cc) {
    $login = $_SESSION["user"];
    $sql = mysql_query("SELECT cc FROM cc_costo WHERE cc = '$cc'");
    $row = mysql_fetch_array($sql);
    $cc = $row['cc'];

    if (isset($cc)) {
        $return = "Y";
    } else {
        $return = "N";
    }

    return $return;
}

function return_cc_name($value) {
    array_cc($id, $name);

    for ($i = 0; $i < count($id); $i++) {
        if ($value == $id[$i]) {
            $return = $name[$i];
        }
    }

    return $return;
}

function return_carrier_name($value) {
    $var = "";
    $id = $_SESSION["id"];
    array_carrier($id, $var);
    $return = "Desconocido";
    while ($row = mysql_fetch_array($id)) {
        $carrier = return_carrier_clean($row['carrier']);
        $value = return_carrier_clean($value);

        if ($value == $carrier) {
            $return = $row['carrier_descrip_for_report'];
        }
    }
    return $return;
}

function return_carrier_clean($value) {
    if (is_numeric(substr($value, 0, 1))) {
        $patron = "/^[0-9]*/";
        $value = preg_replace($patron, "", $value);
        $value = substr_replace($value, '', 0, 1);
    } else {
        $value = $value;
    }

    return $value;
}

function return_status_name($value) {
    $return = "";
    switch ($value) {
        case "PUSHED":$return = "Despachado";
            break;

        case "CONFIRMED":$return = "Confirmado";
            break;

        case "FAILED":$return = "Fallido";
            break;

        case "QUEUED":$return = "Pendiente";
            break;
        case "TRANS":$return = "Pendiente";
            break;
        case "PENDING":$return = "Pendiente";
            break;
        case "SENDING":$return = "Pendiente";
            break;
        case "FORDISPATCH":$return = "Pendiente";
            break;
        case "STOPPED":$return = "Pendiente";
            break;
        case "DISPATCHED":$return = "Pendiente";
            break;
        case "PROCESANDO":$return = "Pendiente";
            break;


        case "NOPUSHED":$return = "Rechazado";
            break;
        case "BLACKLISTED":$return = "Rechazado";
            break;
        case "NOPUSHEDBL":$return = "Rechazado";
            break;
        case "CANCELED":$return = "Rechazado";
            break;
        case "EXPIRED":$return = "Rechazado";
            break;
        case "REJECTED":$return = "Rechazado";
            break;

        case "ANULADO":$return = "Anulado";
            break;
        default: $return = "Desconocido";
            break;
    }
    return $return;
}

function return_type_input_name($value) {
    array_type_input($id, $name);

    for ($i = 0; $i < count($id); $i++) {
        if ($value == $id[$i]) {
            $return = $name[$i];
        }
    }

    return $return;
}

function return_status_mo_name($value) {
    array_status_mo($id, $name);

    for ($i = 0; $i < count($id); $i++) {
        if ($value == $id[$i]) {
            $return = $name[$i];
        }
    }

    return $return;
}

function return_input_mode_name($value) {
    array_input_mode($id, $name);
    $return = "";
    for ($i = 0; $i < count($id); $i++) {
        if ($value == $id[$i]) {
            $return = $name[$i];
        }
    }
    return $return;
}

function return_carrier($value) {
    /*
      $sql2 = mysql_query("SELECT carrier FROM route WHERE lower <= $value AND upper >= $value LIMIT 0,1");
      $carrier1 = '';
      $carrier2 = '';

      if($sql2 != null ){
      $row = mysql_fetch_array($sql2);
      if (empty($row['carrier'])) {
      $carrier1 = "UNKNOWN";
      } else {
      $carrier2 = $row['carrier'];
      }
      if ($carrier1 == "UNKNOWN" || $carrier1 == "Unknown") {
      $carrier = $carrier1;
      } else {
      $carrier = $carrier2;
      }
      }
     */
    $carrier = 'UNKNOWN';

    return $carrier;
}

function return_input_phone($phone, $company) {
    $sql = mysql_query("SELECT phone FROM provUser WHERE phone = $phone AND company = $company");
    $row = mysql_fetch_array($sql);

    if (empty($row['phone'])) {
        $phone = "N";
    } else {
        $phone = "Y";
    }

    return $phone;
}

function return_phone_exists($value) {
    $sql = mysql_query("SELECT phone FROM provUser WHERE phone = $value");
    $row = mysql_fetch_array($sql);
    $phone = $row['phone'];

    if (!empty($phone)) {
        $return = "Y";
    } else {
        $return = "N";
    }

    return $return;
}

function return_check_phone_group($phone, $idGroup) {
    $company = $_SESSION["id"];
    $sql = mysql_query("SELECT phone FROM UserGroup WHERE phone = $phone AND idGroup = $idGroup");
    $row = mysql_fetch_array($sql);

    if (!empty($row['phone'])) {
        return true;
    } else {
        return false;
    }
}

function return_count_user_group($idGroup) {
    $company = $_SESSION["id"];
    $sql = mysql_query("SELECT count(phone) AS cnt FROM UserGroup WHERE company = $company AND idGroup = $idGroup");
    $row = mysql_fetch_array($sql);

    return $row['cnt'];
}

function return_max_char() {
//	$company = $_SESSION["id"];
//	$sql = mysql_query("SELECT max_char FROM company WHERE id = $company");
//	$row = mysql_fetch_array($sql);
//	$max_char = $row['max_char'];
    $max_char = 160;
    if (defined("MAX_LENGTH_TEXT_MSG")) {
        $max_char = MAX_LENGTH_TEXT_MSG;
    }
    return $max_char;
}

/*
  function return_message_clean($value) {
  $value = $value;
  $chars1 = array("á", "é", "í", "ó", "ú", "Á", "É", "Í", "Ó", "Ú", "Â", "Ã©", "Ã³", "Ãº", "|", "'", "&#160;");
  $chars2 = array("a", "e", "i", "o", "u", "A", "E", "I", "O", "U", " ", "á", "ó", "ú", " ", "", "");
  $chars_cnt = count($chars1);

  for ($i = 0; $i < $chars_cnt; $i++) {
  $value = str_replace($chars1[$i], $chars2[$i], $value);
  }

  return $value;
  }
 */

function return_role($user) {
//	$company = $_SESSION["id"];
//	$sql = mysql_query("SELECT role FROM  account WHERE login = '$user'");
//	$row = mysql_fetch_array($sql);
//	$role = $row['role'];
    $role = $_SESSION["profile"];
    return $role;
}

function return_type_send_name_new($recived, $dispatch) {
    $value = "";
    if (empty($dispatch)) {
        $value = "Inmediato";
    } else if ($dispatch == $recived) {
        $value = "Inmediato";
    } else {
        $value = "Agendado";
    }
    return $value;
}

function return_dispatchtime($value1, $value2) {
    if (!empty($value1)) {
        $value = $value1;
    } else {
        $value = $value2;
    }
    return $value;
}

function return_name_group($company, $idGroup) {
    $sql = mysql_query("SELECT name FROM provGroup WHERE company = $company AND id = $idGroup");
    $row = mysql_fetch_array($sql);

    return $row['name'];
}

function return_date_now() {
    $OFFSET_COMPANY = $_SESSION["OFFSET_COMPANY"];
    $inversor = 1; //puede ser (1 = no invierte valores) o (-1 = si invierte valores)
    $offset = convertirOffset($OFFSET_COMPANY, $inversor);
    $format = 'Y-m-d H:i:s';
    $nowUtc = new \DateTime('now', new \DateTimeZone('UTC'));
    $nowUtc->modify("$offset minute");
    $str_now = $nowUtc->format($format);
    return $str_now;
}

function return_date_now_format($format) {
    $inversor = 1; //puede ser (1 = no invierte valores) o (-1 = si invierte valores)
    $OFFSET_COMPANY = $_SESSION["OFFSET_COMPANY"];
    $offset = convertirOffset($OFFSET_COMPANY, $inversor);
    $nowUtc = new \DateTime('now', new \DateTimeZone('UTC'));
    $nowUtc->modify("$offset minute");
    $str_now = $nowUtc->format($format);
    return $str_now;
}

function return_date_after($date) {
    $format = 'Y-m-d H:i:s';
    $nowUtc = new \DateTime($date);
    $str_now = $nowUtc->format($format);
    return $str_now;
}

function return_date_after_format($date, $format) {
    $nowUtc = new \DateTime($date);
    $str_now = $nowUtc->format($format);
    return $str_now;
}

function return_date_after_gmt($date) {
    $inversor = -1; //puede ser (1 = no invierte valores) o (-1 = si invierte valores)
    $OFFSET_COMPANY = $_SESSION["OFFSET_COMPANY"];
    $offset = convertirOffset($OFFSET_COMPANY, $inversor);
    $format = 'Y-m-d H:i:s';
    $nowUtc = new \DateTime($date);
    $nowUtc->modify("$offset minute");
    $str_now = $nowUtc->format($format);
    return $str_now;
}

function return_date_after_format_gmt($date, $format) {
    $inversor = -1; //puede ser (1 = no invierte valores) o (-1 = si invierte valores)
    $OFFSET_COMPANY = $_SESSION["OFFSET_COMPANY"];
    $offset = convertirOffset($OFFSET_COMPANY, $inversor);
    $nowUtc = new \DateTime($date);
    $nowUtc->modify("$offset minute");
    $str_now = $nowUtc->format($format);
    return $str_now;
}

function return_date_send_report($date) {
    $value = date("Y-m-d H:i:s", strtotime("$date +0 hour"));
    return $value;
}

function return_process($company, $input) {
    $sql = mysql_query("SELECT id FROM process WHERE company = $company AND input_mode = '$input' ORDER BY id DESC");
    $row = mysql_fetch_array($sql);
    $id = $row['id'];

    return $id;
}

?>