<?php
include ('dbConn.php');
?>

<?php

//*************************Reporte Consolidado activo*****************************************/
function gesStatus() {
    title("Gesti&oacute;n", "Reporte Consolidado.", "");
    $login = $_SESSION["user"];
    $role = return_role($login);
    $company = $_SESSION["id"];
    $status = "";
    $phone = "";

    $id = $_SESSION["id"];
    $sendDateFrom = obtener_parameter("sendDateFrom", "GET");
    $sendDateTo = obtener_parameter("sendDateTo", "GET");
    $carrier = obtener_parameter("op", "GET");
    $input_mode = obtener_parameter("mode", "GET");
    $type_input = obtener_parameter("type_input", "GET");
    $cc = obtener_parameter("cc", "GET");
    $user = obtener_parameter("user", "GET");
    $listuser = obtener_parameter("listuser", "GET");
    $selcompany = obtener_parameter("company", "GET");


    $yF = substr($sendDateFrom, 0, 4);
    $mF = substr($sendDateFrom, 5, 2);
    $dF = substr($sendDateFrom, 8, 2);

    $yT = substr($sendDateTo, 0, 4);
    $mT = substr($sendDateTo, 5, 2);
    $dT = substr($sendDateTo, 8, 2);

    if (empty($dF)) {
        $dF = "01";
    } else {
        $dF = $dF;
    }
    if (empty($mF)) {
        $mF = return_date_now_format('m');
    } else {
        $mF = $mF;
    }
    if (empty($yF)) {
        $yF = return_date_now_format('Y');
    } else {
        $yF = $yF;
    }
    if (empty($dT)) {
        $dT = return_date_now_format('d');
    } else {
        $dT = $dT;
    }
    if (empty($mT)) {
        $mT = return_date_now_format('m');
    } else {
        $mT = $mT;
    }
    if (empty($yT)) {
        $yT = return_date_now_format('Y');
    } else {
        $yT = $yT;
    }
    ?>

    <script>
        function agregarUsuario() {

            var hidden = document.getElementById("hdd-id-usuarios");
            var login = document.getElementById("user").value;
            var table = document.getElementById("table-listUser");
            var existe = false;
            var logins = hidden.value;
            var todos = false;

            if (login == "") {//equivalente a todos
                logins = "";
                login = "todos";
                todos = true;
                table.innerHTML = "";
            } else {
                if (logins.indexOf("todos") != -1) {
                    logins = "";
                    table.innerHTML = "";
                }
            }

            if (logins != null && logins.length > 0) {
                var arr = logins.split("-");
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i] == login) {
                        existe = true;
                    }
                }
            }

            if (!existe) {
                if (logins.length == 0) {
                    logins += login;
                } else {
                    logins += "-" + login;
                }
                var btn = document.createElement("span");
                btn.setAttribute("class", "buttom");
                btn.appendChild(document.createTextNode("X"));
                btn.setAttribute("onclick", "quitarUsuario('" + login + "')");

                //				var table = document.createElement("table");
                var tr = document.createElement("tr");
                tr.setAttribute("id", "tr-" + login);
                var tdTexto = document.createElement("td");
                var tdButon = document.createElement("td");


                tdTexto.appendChild(document.createTextNode(login));
                tdButon.appendChild(btn);
                tr.appendChild(tdTexto);
                tr.appendChild(tdButon);
                table.appendChild(tr);

                //				li.appendChild(table);
                //				ul.appendChild(li);
                hidden.value = logins;
            } else {
                alert("el usuario ya ha sido agregado");
            }
        }

        function quitarUsuario(login) {
            var hidden = document.getElementById("hdd-id-usuarios");
            var table = document.getElementById("table-listUser");
            var logins = hidden.value;
            if (logins != null && logins.length > 0) {
                var arr = logins.split("-");
                var loginsAux = "";
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i] != login) {
                        if (loginsAux.length == 0) {
                            loginsAux = arr[i];
                        } else {
                            loginsAux += "-" + arr[i];
                        }
                    }
                }
                hidden.value = loginsAux;
                table.removeChild(document.getElementById("tr-" + login));
            }
        }

    </script>

    <form action="" name="tmc" method="get" onkeypress="return event.keyCode != 13">
        <input type="hidden" name="seccion" value="gesStatus">
        <input id="hdd-id-usuarios" name="listuser" type="hidden" value="<?= $listuser ?>">

        <table width="400" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Desde)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yF; ?>-<?= $mF ?>-<?= $dF; ?>" readonly name="sendDateFrom" id="sendDateFrom"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador1"></td>
            </tr>
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha (Hasta)</b></td>
                <td align="left" valign="middle" width="115"><input type="text" value="<?= $yT; ?>-<?= $mT ?>-<?= $dT; ?>" readonly name="sendDateTo" id="sendDateTo"> </td>
                <td align="left"  width="160"><img src="includes/javascript/images/calendar.png" id="lanzador2"></td>
            </tr>
            <!--
            <tr>
                    <td align="left"><b >Operador: </b></td>
                    <td align="left" colspan="2"><?php //select_carrier($carrier, $company);   ?></td>
            </tr>
            -->
            <tr>
                <td align="left"><b >Tipo de Ingreso: </b></td>
                <td align="left" colspan="2"><?php select_input_mode($input_mode); ?></td>
            </tr>
            <?php
            if ($login == "facturacion") {
                ?>
                <tr>
                    <td align="left"><b >Ingreso: </b></td>
                    <td align="left" colspan="2"><?php select_type_input($type_input); ?></td>
                </tr>
                <?php
            }
            ?>
            <?php if (select_type_profile($role) == 1 || select_type_profile($role) == 2) { ?>
                <tr>
                    <td align="left"><b >Usuarios: </b></td>
                    <td align="left" colspan="2">
                        <?php select_users($user); ?>
                        <span id="btnAgregar" class="buttom" onclick="agregarUsuario()" >Agregar</span>
                    </td>
                </tr>
                <tr>
                    <td align="left"><b >Usuarios Seleccionados: </b></td>
                    <td align="left" colspan="2">
                        <table>
                            <tbody id="table-listUser">
                                <?php
                                if (!empty($listuser)) {
                                    $arr = explode("-", $listuser);
                                    for ($i = 0; $i < count($arr); $i++) {
                                        $textUsuario = $arr[$i];
                                        echo "<tr id=\"tr-" . $textUsuario . "\"><td>$textUsuario</td><td><span class=\"buttom\" onclick=\"quitarUsuario('$textUsuario')\">X</span></td></tr>";
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </td>
                </tr>
            <?php } ?>
            <?php if (select_type_profile($role) == 1) { ?>	
                <tr>
                    <td align="left"><b >Compa&ntilde;ia: </b></td>
                    <td align="left" colspan="2"><?php select_company($selcompany); ?></td>
                </tr>
            <?php } ?>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30" colspan="2"><span onclick="gesStatus();" class="buttom">Buscar</span> <span onclick="reset();" class="buttom">Borrar</span></td>
            </tr>
        </table>
    </form>
    <br /><br />
    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateFrom", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador1"
        }
        )
    </script>

    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateTo", range: [2003, 2999],
            ifFormat: "%Y-%m-%d",
            showsTime: false,
            timeFormat: "24",
            button: "lanzador2"
        }
        )
    </script>

    <?php
    if (empty($sendDateFrom)) {
        
    } else {
        $sql_filtro = "";
        if (!empty($sendDateFrom)) {
            $format_date = "Y-m-d H:i:s";
            $sendDateFromGmt = return_date_after_format_gmt("$sendDateFrom 00:00:00", $format_date);
            $sendDateToGmt = return_date_after_format_gmt("$sendDateTo 23:59:59", $format_date);
            $sql_date = " AND receivedTime between '$sendDateFromGmt' AND  '$sendDateToGmt' ";
            $sql_filtro .= $sql_date;
        }

        if (!empty($carrier)) {
            $sql_carrier = " AND t.recipientDomain LIKE '%$carrier%' ";
            $sql_filtro .= $sql_carrier;
        }

        if (!empty($input_mode)) {
            $sql_input_mode = " AND t.input_mode = '$input_mode' ";
            $sql_filtro .= $sql_input_mode;
        }

        if (!empty($type_input)) {
            if (strcmp($type_input, "XMLRPC") == 0) {
                $sql_type_input = " AND t.input_mode = '$type_input' ";
            } else {
                $sql_type_input = " AND t.input_mode IN ('WEB-IND', 'WEB-GRP', 'UPLOAD', 'USSD') ";
            }
            $sql_filtro .= $sql_type_input;
        }

        if (!empty($phone)) {
            $sql_phone = " AND t.recipientId = '$phone' ";
            $sql_filtro .= $sql_phone;
        }

        if (!empty($status)) {
            if (strcmp($status, "QUEUED") == 0) {
                $sql_status = " AND t.status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED') ";
            } else {
                $sql_status = " AND t.status = '$status' ";
            }
            $sql_filtro .= $sql_status;
        }

        if (!empty($selcompany)) {
            $sql_filtro .= " AND a.company = $selcompany ";
        }

        if (!empty($listuser)) {
            if (strcmp($listuser, "todos") != 0) {
                $arrUsers = explode("-", $listuser);
                $textUsers = "";
                for ($i = 0; $i < count($arrUsers); $i++) {
                    if (empty($textUsers)) {
                        $textUsers .= "'$arrUsers[$i]'";
                    } else {
                        $textUsers .= ", '$arrUsers[$i]'";
                    }
                }
                $sql_user = "  AND t.login in ($textUsers) ";
                $sql_filtro .= $sql_user;
            }
        }

        if (select_type_profile($role) != 1) {
            if (select_type_profile($role) != 2) {
                $sql_login = " AND t.login = '$login' ";
                $sql_filtro .= $sql_login;
            }
            $sql_filtro .= " AND a.company = $company ";
        }


        $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);


        $query = ""
                . " SELECT substring( date_sub( t.receivedTime , interval $interval minute) ,1,10 ) as fecha,"
                . " c.id as id_company, "
                . " c.nom_corto as company, "
                . " SUM("
                . "		   case t.status when 'PUSHED'		then 1 else 0 end "
                . " ) as pushed, "
                . " SUM("
                . "		   case t.status when 'CONFIRMED'	then 1 else 0 end "
                . " ) as confirmed, "
                . " SUM("
                . "		   case t.status when 'FAILED'		then 1 else 0 end "
                . " ) as failed, "
                . " SUM("
                . "		   case t.status when 'NOPUSHED'	then 1 else 0 end "
                . "		or case t.status when 'BLACKLISTED' then 1 else 0 end "
                . "		or case t.status when 'NOPUSHEDBL'	then 1 else 0 end "
                . "		or case t.status when 'CANCELED'	then 1 else 0 end "
                . "		or case t.status when 'EXPIRED'		then 1 else 0 end "
                . "		or case t.status when 'REJECTED'	then 1 else 0 end "
                . " ) as nopushed, "
                . " SUM("
                . "		   case t.status when 'QUEUED'		then 1 else 0 end "
                . "		or case t.status when 'TRANS'		then 1 else 0 end "
                . "		or case t.status when 'PENDING'		then 1 else 0 end "
                . "		or case t.status when 'SENDING'		then 1 else 0 end "
                . "		or case t.status when 'FORDISPATCH' then 1 else 0 end "
                . "		or case t.status when 'STOPPED'		then 1 else 0 end "
                . "		or case t.status when 'DISPATCHED'	then 1 else 0 end "
                . "		or case t.status when 'PROCESANDO'	then 1 else 0 end "
                . " ) as queued, "
                . " SUM("
                . "		   case t.status when 'ANULADO' then 1 else 0 end "
                . " ) as anulado, "
                . " count(*) as total "
                . " FROM trafficMT t "
                . " INNER JOIN account a ON t.login = a.login "
                . " INNER JOIN company c on a.company = c.id "
                . " WHERE 1 "
                . " $sql_filtro "
                . " GROUP by 1, 2, 3 "
                . " ORDER by 1 ASC ";
        $sql = mysql_query($query);
        $total_rows = mysql_num_rows($sql);
        ?>
        <div class="alert_big">
            <?php
            if ($total_rows < 1) {
                echo "No se encontraron registros asociados a esta consulta.";
            } else {
                $params = "";
                $params .= "sendDateFrom=$sendDateFrom";
                $params .= "&sendDateTo=$sendDateTo";
                $params .= "&op=$carrier";
                $params .= "&mode=$input_mode";
                $params .= "&status=$status";
                $params .= "&phone=$phone";
                $params .= "&user=$user";
                $params .= "&listuser=" . $listuser;
                $params .= "&cc=$cc";
                $params .= "&company=$selcompany";
                $params .= "&type_input=$type_input";
                ?>
                <img src="images/icons/excel.png" />  
                <a href="ajaxfuncs/gesStatusExcel.php?<?= $params; ?>">
                    Generar Reporte</a>
            </div>

            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="table-list">
                <tr>
                    <th>FECHA</th>
                    <th>COMPA&Ntilde;IA</th>
                    <th>DESPACHADO</th>
                    <th>CONFIRMADO</th>
                    <th>FALLIDO</th>
                    <th>RECHAZADO</th>
                    <th>PENDIENTE</th>
                    <th>ANULADO</th>
                    <th>TOTAL</th>
                </tr>
                <?php
                $i = 0;
                $pushed = 0;
                $confirmed = 0;
                $failed = 0;
                $nopushed = 0;
                $queued = 0;
                $anulado = 0;
                $total = 0;

                while ($row = mysql_fetch_array($sql)) {

                    $res = $i % 2;
                    if ($res == 0) {
                        $class = "";
                    } else {
                        $class = "table-list-tr";
                    }
                    $paramfecha = substr($row['fecha'], 0, 10);
                    $params = "";
                    $params .= "&date=$paramfecha";
                    $params .= "&carrier=$carrier";
                    $params .= "&mode=$input_mode";
                    $params .= "&user=$user";
                    $params .= "&listuser=" . $listuser;
                    $params .= "&type_input=$type_input";
                    $params .= "&company=" . $row['id_company'];
                    ?>
                    <tr class="<?= $class ?>">
                        <td><a href="?seccion=gesStatusDetail<?= $params ?>"><b><?= $paramfecha; ?></b></a></td>
                        <td><?= $row['company']; ?></td>
                        <td><?= $row['pushed']; ?></td>
                        <td><?= $row['confirmed']; ?></td>
                        <td><?= $row['failed']; ?></td>
                        <td><?= $row['nopushed']; ?></td>
                        <td><?= $row['queued']; ?></td>
                        <td><?= $row['anulado']; ?></td>
                        <td><?= $row['total']; ?></td>
                    </tr>
                    <?php
                    $i++;
                    $pushed = $row['pushed'] + $pushed;
                    $confirmed = $row['confirmed'] + $confirmed;
                    $failed = $row['failed'] + $failed;
                    $nopushed = $row['nopushed'] + $nopushed;
                    $queued = $row['queued'] + $queued;
                    $anulado = $row['anulado'] + $anulado;
                    $total = $row['total'] + $total;
                }
                ?>	
                <tr>
                    <th>TOTALES</th>
                    <th></th>
                    <th><?= $pushed ?></th>
                    <th><?= $confirmed ?></th>
                    <th><?= $failed ?></th>
                    <th><?= $nopushed ?></th>
                    <th><?= $queued ?></th>
                    <th><?= $anulado ?></th>
                    <th><?= $total ?></th>
                </tr>
            </table>	



            <?php
        }
    }
}
?>




<?php

function gesStatusDetail() {

    title("Gesti&oacute;n", "Reporte Consolidado.", "");
    $login = $_SESSION["user"];
    $role = return_role($login);
    $cc = "";
    $company = $_SESSION["id"];

    $date = obtener_parameter("date", "GET");
    $sendDateFrom = obtener_parameter("sendDateFrom", "GET");
    $sendDateTo = obtener_parameter("sendDateTo", "GET");
    $carrier = obtener_parameter("op", "GET");
    $input_mode = obtener_parameter("mode", "GET");
    $type_input = obtener_parameter("type_input", "GET");
    $user = obtener_parameter("user", "GET");
    $listuser = obtener_parameter("listuser", "GET");
    $p = obtener_parameter("p", "GET");
    $selcompany = obtener_parameter("company", "GET");

    $sql_filtro = "";
    if (!empty($date)) {
        $format_date = "Y-m-d H:i:s";
        $sendDateFromGmt = return_date_after_format_gmt("$date 00:00:00", $format_date);
        $sendDateToGmt = return_date_after_format_gmt("$date 23:59:59", $format_date);
        $sql_date = " AND receivedTime between '$sendDateFromGmt' AND  '$sendDateToGmt' ";
        $sql_filtro .= $sql_date;
    }

    if (!empty($carrier)) {
        $sql_carrier = " AND t.recipientDomain LIKE '%$carrier%' ";
        $sql_filtro .= $sql_carrier;
    }

    if (!empty($input_mode)) {
        $sql_input_mode = " AND t.input_mode = '$input_mode' ";
        $sql_filtro .= $sql_input_mode;
    }

    if (!empty($type_input)) {
        if (strcmp($type_input, "XMLRPC") == 0) {
            $sql_type_input = " AND t.input_mode = '$type_input' ";
        } else {
            $sql_type_input = " AND t.input_mode IN ('WEB-IND', 'WEB-GRP', 'UPLOAD', 'USSD') ";
        }
        $sql_filtro .= $sql_type_input;
    }

    if (!empty($phone)) {
        $sql_phone = " AND t.recipientId = '$phone' ";
        $sql_filtro .= $sql_phone;
    }

    if (!empty($status)) {
        if (strcmp($status, "QUEUED") == 0) {
            $sql_status = " AND t.status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED') ";
        } else {
            $sql_status = " AND t.status = '$status' ";
        }
        $sql_filtro .= $sql_status;
    }

    if (!empty($selcompany)) {
        $sql_filtro .= " AND a.company = $selcompany ";
    }

    if (!empty($listuser)) {
        if (strcmp($listuser, "todos") != 0) {
            $arrUsers = explode("-", $listuser);
            $textUsers = "";
            for ($i = 0; $i < count($arrUsers); $i++) {
                if (empty($textUsers)) {
                    $textUsers .= "'$arrUsers[$i]'";
                } else {
                    $textUsers .= ", '$arrUsers[$i]'";
                }
            }
            $sql_user = "  AND t.login in ($textUsers) ";
            $sql_filtro .= $sql_user;
        }
    }

    if (select_type_profile($role) != 1) {
        if (select_type_profile($role) != 2) {
            $sql_login = " AND t.login = '$login' ";
            $sql_filtro .= $sql_login;
        }
        $sql_filtro .= " AND a.company = $company ";
    }

    if (!isset($p)) {
        $page = 1;
    } else {
        if ($p <= 0) {
            $page = 1;
        } else {
            $page = $p;
        }
    }

    $rows_for_page = 30;

    $rows_from = (($page * $rows_for_page) - $rows_for_page);


    $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);


    $query = " SELECT "
            . " t.recipientId "
            . " , t.company "
            . " , t.recipientDomain "
            . " , t.status "
            . " , date_sub( t.receivedTime, interval $interval minute) AS receivedTime"
            . " , date_sub( t.dispatchTime, interval $interval minute) AS dispatchTime"
            . " , date_sub( t.deliveryTime, interval $interval minute) AS deliveryTime"
            . " , t.input_mode "
            . " , t.login "
            . " , t.msgText "
            . " , t.errText "
            . " , c.nom_corto as company "
            . " FROM trafficMT t "
            . " INNER JOIN account a on t.login = a.login "
            . " INNER JOIN company c on a.company = c.id "
            . " WHERE 1 "
            . " $sql_filtro ";
    $sql = mysql_query($query . " LIMIT $rows_from, $rows_for_page");

    $total_rows = mysql_num_rows(mysql_query($query));

    $total_pages = ceil($total_rows / $rows_for_page);

    if ($total_rows < 1) {
        ?>




        <div class="alert_big">No se encontraron registros asociados a esta consulta.</div>

        <?php
    } else {
        $params = "";
        $params .= "date=$date";
        $params .= "&carrier=$carrier";
        $params .= "&mode=$input_mode";
        $params .= "&user=$user";
        $params .= "&listuser=$listuser";
        $params .= "&cc=$cc";
        $params .= "&type_input=$type_input";
        $params .= "&company=$selcompany";
        ?>
        <div class="alert_big"><b><?= $total_rows ?></b> mensajes correspondientes a la fecha: <b><?= $date ?></b>.<br /><br />
            <img src="images/icons/excel.png" />
            <a href="ajaxfuncs/gesStatusDetailExcel.php?<?= $params; ?>" target="_blank">Generar Reporte</a>
        </div><br>
        <span onclick="history.back();" class="buttom">VOLVER</span><br><br>

        <?php pag_pages($page, $total_pages, "?seccion=gesStatusDetail&$params"); ?>

        <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
            <tr>
                <th>M&Oacute;VIL</th>
                <th>OPERADOR</th>
                <th>ESTADO</th>
                <th>FECHA DE CARGA</th>
                <th>FECHA DE ENV&Iacute;O</th>
                <th>FECHA DE DESPACHO</th>
                <th>TIPO DE ENV&Iacute;O</th>
                <th>INGRESO</th>
                <th>USUARIO</th>
                <th>COMPA&Ntilde;IA</th>
                <th>MENSAJE</th>  
            </tr>


            <?php
            $i = 0;
            while ($row = mysql_fetch_array($sql)) {

                $res = $i % 2;

                if ($res == 0) {
                    $class = "";
                } else {
                    $class = "table-list-tr";
                }

                if ($row['status'] == "NOPUSHED" || $row['status'] == "FAILED" || $row['status'] == "REJECTED") {
                    $td = "table-list-td-error";
                    $cause = "<br>[Causa: " . $row['errText'] . "]";
                } else {
                    $td = "";
                    $cause = "";
                }
                ?>

                <tr class="<?= $class ?>">
                    <td class="<?= $td; ?>"><?= $row['recipientId'] ?></td>
                    <td class="<?= $td; ?>"><?= return_carrier_name($row['recipientDomain']) ?></td>
                    <td class="<?= $td; ?>"><?= return_status_name($row['status']) ?></td>
                    <td class="<?= $td; ?>"><?= $row['receivedTime'] ?></td>
                    <td class="<?= $td; ?>"><?= return_dispatchtime($row['dispatchTime'], $row['receivedTime']) ?></td>
                    <td class="<?= $td; ?>"><?= $row['deliveryTime'] ?></td>     	
                    <td class="<?= $td; ?>"><?= return_type_send_name_new($row['receivedTime'], $row['dispatchTime']); ?></td>
                    <td class="<?= $td; ?>"><?= return_input_mode_name($row['input_mode']) ?></td>
                    <td class="<?= $td; ?>"><?= $row['login'] ?></td>
                    <td class="<?= $td; ?>"><?= $row['company'] ?></td>
                    <td class="<?= $td; ?>"><?= $row['msgText'] ?> <?= $cause ?></td>
                </tr>
                <?php
                $i++;
            }
            ?>
        </table>
        <?php
    }
}
?>




