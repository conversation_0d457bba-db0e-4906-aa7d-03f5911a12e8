<?php

function session() {
    ?>
    <div class="loggin">
        <table style="width: 100%;">
            <?php
            if (!empty($_SESSION["user"])) {
                $user = $_SESSION["user"];
                ?>
                <tr>
                    <td style="text-align: left;">Bienvenido:</td>
                    <td style="text-align: left;"><b><?= $user ?></b></td>
                </tr>	
                <?php
            }
            if (!empty($_SESSION["profile"])) {
                $profile = $_SESSION["profile"];
                ?>
                <tr>
                    <td style="text-align: left;">Perfil:</td>
                    <td style="text-align: left;"><b><?= $profile ?></b></td>
                </tr>
                <?php
            }
            ?>
        </table>
        <div style="text-align: right;">
            <a href="expire_session.php"> Cerrar Sesi&oacute;n </a>
        </div>



    </div>
    <?php
}

function menu() {
    ?>
    <div class="containerMenu">	
        <ul id="topnav" class="nav">
            <li><a href="?seccion=home" >HOME</a></li>
            <?php if (validate_permisson("1000")) { ?>
                <li>
                    <a href=""> ENVIO SMS </a> 
                    <ul>
                        <?php if (validate_permisson("1001")) { ?>
                            <li><a href="?seccion=sendNow">INDIVIDUAL INMEDIATO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1002")) { ?>
                            <li><a href="?seccion=sendAfter">INDIVIDUAL AGENDADO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1003")) { ?>
                            <li><a href="?seccion=sendNowGroup">GRUPAL INMEDIATO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1004")) { ?>
                            <li><a href="?seccion=sendAfterGroup">GRUPAL AGENDADO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1005")) { ?>
                            <li><a href="?seccion=sendFile">DESDE ARCHIVO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1006")) { ?>
                            <li><a href="?seccion=sendFileConvinated">DESDE ARCHIVO COMBINADO</a></li>
                        <?php } ?>
                    </ul>
                </li>
            <?php } ?>
            <?php if (validate_permisson("1050")) { ?>
                <li>
                    <a href="#"> CAMPA&Ntilde;A </a> 
                    <ul>
                        <?php if (validate_permisson("1051")) { ?>
                            <li><a href="?seccion=loadCampaignByFile">CARGA CON ARCHIVO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1052")) { ?>
                            <li><a href="?seccion=listPendingCampaign">LISTA PENDIENTES</a></li>
                        <?php } ?>

                    </ul>
                </li>
            <?php } ?>	

            <?php if (validate_permisson("1010")) { ?>
                <li>
                    <a href=""> APROVISIONAMIENTO</a>
                    <ul>
                        <?php if (validate_permisson("1011")) { ?>
                            <li><a href="?seccion=provUsers">ABONADOS</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1012")) { ?>
                            <li><a href="?seccion=provGroups">GRUPOS</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1013")) { ?>
                            <li><a href="?seccion=mantenanceBlackList">MANTENCI&Oacute;N BLACKLIST</a></li>
                        <?php } ?>
                    </ul>
                </li>
            <?php } ?>
            <?php if (validate_permisson("1020")) { ?>
                <li>
                    <a href=""> REPORTES</a>
                    <ul>
                        <?php if (validate_permisson("1021")) { ?>
                            <li><a href="?seccion=gesLoadDailyDetail"> MT - DETALLE DE CARGA DIARIO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1022")) { ?>
                            <li><a href="?seccion=gesLoadDailyConsolidated"> MT - CONSOLIDADO DE CARGA DIARIO</a></li>
                        <?php } ?>						
                        <?php if (validate_permisson("1026")) { ?>
                            <li><a href="?seccion=gesLoadHistorialDetail"> MT - DETALLE DE CARGA HISTORICO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1027")) { ?>
                            <li><a href="?seccion=gesLoadHistorialConsolidated"> MT - CONSOLIDADO DE CARGA HISTORICO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1023")) { ?>
                            <li><a href="?seccion=gesCustom">MT - PERSONALIZADO</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1024")) { ?>	
                            <li><a href="?seccion=gesBillingDetail">MT - DETALLE DE FACTURACION</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1025")) { ?>
                            <li><a href="?seccion=gesBillingConsolidated">MT - CONSOLIDADO DE FACTURACION</a></li>
                        <?php } ?>

                        <?php if (validate_permisson("1028")) { ?>
                            <li><a href="?seccion=gesLoadDetailMo">MO - DETALLE RECEPCION SMS</a></li>
                        <?php } ?>
                           
                        <?php if (validate_permisson("1029")) { ?>
                            <li><a href="?seccion=gesReportQueueReport">GENERAL - COLA REPORTES</a></li>
                        <?php } ?>
                            

                    </ul>
                </li>
            <?php } ?>

            <?php if (validate_permisson("1040")) { ?>
                <li>
                    <a href=""> MANTENCIÓN</a>
                    <ul>
                        <?php if (validate_permisson("1041")) { ?>
                            <li><a href="?seccion=userMaintenance">USUARIOS</a></li>
                        <?php } ?>
                    </ul>
                </li>
            <?php } ?>

            <?php if (validate_permisson("1030")) { ?>
                <li>
                    <a href=""> SOPORTE</a>
                    <ul>
                        <?php if (validate_permisson("1031")) { ?>
                            <li><a href="?seccion=sopPass">CAMBIO DE PASSWORD</a></li>
                        <?php } ?>
                        <?php if (validate_permisson("1032")) { ?>
                            <li><a href="manual-mcs.pdf" target="_blank">MANUAL MCS</a></li>
                        <?php } ?>
                    </ul>
                </li>
            <?php } ?>
        </ul>
    </div>
    <?php
}

function title($title, $section, $image) {
    ?>
    <table width="100%" border="0" cellspacing="0">
        <tr>
            <td height="20" valign="bottom">
                <span class="container-text-title"> <?= $title ?> | <?= $section ?></span>
            </td>
        </tr>
    </table>
    <br />
    <?php
}

function copy_rights() {
    $company = "";
    if (defined("COMPANY_NAME")) {
        $company = COMPANY_NAME;
    }
    ?>
    <table width="900" border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td width="450" align="left" valign="top">
                <span class="text-company"><?= $company ?></span>
                <br />
                <span class="text-copyright"><br />
                    MOBID © Copyright 2012</span>
            </td>

            <td width="450" align="right" valign="top">
                <table width="200" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td width="40" height="40" align="left"><img src="images/email.png" /></td>
                        <td width="160" align="left"><span style="font-family: 'Open Sans Condensed', sans-serif;	font-size: 11px; color:#eee;"><a href="mailto: <EMAIL>" style="color:#eee;"><EMAIL></a></span></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <?php
}

function log_tmc($company, $title, $category, $action, $login) {
    $msg_error = "";
    $date = return_date_now_format('Y-m-d H:i:s');
    $msg_error .= "[ " . $date . " ] \n";
    $msg_error .= "[ " . $date . " ] -------------------------------\n";
    $msg_error .= "[ " . $date . " ] COMPANY\t: [" . $company . "]\n";
    $msg_error .= "[ " . $date . " ] SECTION\t: [" . $title . "]\n";
    $msg_error .= "[ " . $date . " ] ACTION\t: [" . $category . "]\n";
    $msg_error .= "[ " . $date . " ] INFO\t: [" . $action . "]\n";
    $msg_error .= "[ " . $date . " ] LOGIN\t: [" . $login . "]\n";
    $msg_error .= "[ " . $date . " ] -------------------------------\n";
    $msg_error .= "[ " . $date . " ] \n";
    $path = path_logs;
    $log = fopen('' . $path . '/DEMO_TMP/DEMO_TMP.log', 'a');
    fwrite($log, $msg_error);
    fclose($log);
}

function pag_pages($page, $total_pages, $url) {
    ?>

                        <!--<table width="300" border="0" cellpading="0" cellspacing="0" align="center" class="tablegeneral">-->
    <table width="300" border="0" cellspacing="0" align="center" class="tablegeneral">
        <tr>
            <td align="right" width="70" valign="top">

                <?php
                if ($page > 1) {
                    echo "<a title='Primera Página' href='$url&p=1'><img src='images/icons/pag_first.png'></a> ";
                } else {
                    echo "<img src='images/icons/pag_first_off.png'> ";
                }

                if ($page > 1) {
                    echo "<a title='Anterior' href='$url&p=" . ($page - 1) . "'><img src='images/icons/pag_back.png'></a> ";
                } else {
                    echo "<img src='images/icons/pag_back_off.png'> ";
                }
                ?>

            </td>
            <td align="center" valign="top" width="160">

                <?php
                for ($i = $page - 3; $i < $page; $i++) {
                    if ($i < 1) {
                        
                    } else {

                        if ($i <= 9) {
                            $i = "0$i";
                        }

                        echo " <a title='Página $i' href='$url&p=$i' class='pag'>$i</a> ";
                    }
                }

                echo "<b>[$page]</b>";

                for ($n = $page + 1; $n < $page + 4; $n++) {
                    if ($n > $total_pages) {
                        
                    } else {
                        if ($n <= 9) {
                            $n = "0$n";
                        }

                        echo " <a title='Página $n' href='$url&p=$n' class='pag'>$n</a> ";
                    }
                }
                ?>

            </td>
            <td align="left" width="70" valign="top">
                <?php
                if ($page < $total_pages) {
                    echo "<a title='Siguiente' href='$url&p=" . ($page + 1) . "'><img src='images/icons/pag_next.png'></a> ";
                } else {
                    echo "<img src='images/icons/pag_next_off.png'> ";
                }

                if ($page < $total_pages) {
                    echo "<a title='Última Página' href='$url&p=$total_pages'><img src='images/icons/pag_last.png'></a> ";
                } else {
                    echo "<img src='images/icons/pag_last_off.png'> ";
                }
                ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </table>
    <?php
}

function pag_pages_ajax($page, $total_pages, $url, $function) {
    ?>

    <table width="300" border="0" class="tablegeneral">
        <tr>
            <td align="right" width="70" valign="top">

                <?php
                if ($page > 1) {
                    ?>
                    <img src='images/icons/pag_first.png' onclick="<?= $function ?>('<?= 1 ?>', '<?= $url ?>');" title="Primera Página">
                    <?php
                } else {
                    ?>
                    <img src='images/icons/pag_first_off.png'>
                    <?php
                }

                if ($page > 1) {
                    ?>
                    <img src='images/icons/pag_back.png'  onclick="<?= $function ?>('<?= $page - 1; ?>', '<?= $url ?>');" title="Anterior">
                    <?php
                } else {
                    ?>
                    <img src='images/icons/pag_back_off.png'>
                    <?php
                }
                ?>

            </td>
            <td align="center" valign="middle" width="160">

                <?php
                for ($i = $page - 3; $i < $page; $i++) {
                    if ($i < 1) {
                        
                    } else {
                        if ($i <= 9) {
                            $i = "0$i";
                        }
                        ?>
                        <a title='Página <?= $i ?>' onclick="<?= $function ?>('<?= $i ?>', '<?= $url ?>');"><?= $i ?></a>
                        <?php
                    }
                }

                echo "<b>[$page]</b>";

                for ($n = $page + 1; $n < $page + 4; $n++) {
                    if ($n > $total_pages) {
                        
                    } else {
                        if ($n <= 9) {
                            $n = "0$n";
                        }
                        ?>
                        <a title='Página <?= $n ?>' onclick="<?= $function ?>('<?= $n ?>', '<?= $url ?>');"><?= $n ?></a>
                        <?php
                    }
                }
                ?>

            </td>
            <td align="left" width="70" valign="top">

                <?php
                if ($page < $total_pages) {
                    ?>
                    <img src='images/icons/pag_next.png' onclick="<?= $function ?>('<?= $page + 1; ?>', '<?= $url ?>');" title="Siguiente">
                    <?php
                } else {
                    ?>
                    <img src='images/icons/pag_next_off.png'>
                    <?php
                }

                if ($page < $total_pages) {
                    ?>
                    <img src='images/icons/pag_last.png'  onclick="<?= $function ?>('<?= $total_pages; ?>', '<?= $url ?>');"  title="Última Página">
                    <?php
                } else {
                    ?>
                    <img src='images/icons/pag_last_off.png'>
                    <?php
                }
                ?>

            </td>
        </tr>
        <tr>
            <td></td>
            <td align="center"></td>
            <td></td>
        </tr>
    </table>
    <?php
}
?>