<?php
include ('dbConn.php');
?>

<?php

function loadCampaignByFile() {
    title("Campa&ntilde;as", "Carga con archivo", "campa&ntilde;as");
    $profile = $_SESSION["profile"];
    $company = $_SESSION["id"];
    if (select_type_profile($profile) == 1) {
        $classtxt = "td75";
        $cbocompanyview = 1;
    } else {
        $classtxt = "td100";
        $cbocompanyview = 0;
    }
    $max_char_msg = 160;
    if (defined("MAX_LENGTH_TEXT_MSG")) {
        $max_char_msg = MAX_LENGTH_TEXT_MSG;
    }
    $msg_example = "Escriba un mensaje agregando paramteros:\n\nEjemplo:\nEstimado Sr: ==P1==\nLe informamos que tiene ==P2== de credito para realizar compras hasta el ==P3==";
    ?>


    <script type="text/javascript" src="includes/javascript/datetimepicker/jquery.simple-dtpicker.js"></script>
    <link type="text/css" rel="stylesheet" href="includes/javascript/datetimepicker/jquery.simple-dtpicker.css"/>

    <script type="text/javascript" src="includes/javascript/app/campaign/loadCampaignByFile.js?v=<?=VERSION?>"></script>

    <div id="div-create" class="formLoadCampaign espaciadoObjetos">
    <!--		<input id="hddUser" type="hidden" value="< ?=$login? >">
            <input id="hddCompany" type="hidden" value="< ?=$company? >">
            <input id="hddProfile" type="hidden" value="< ?=$profile? >">
            <input id="hddOffset" type="hidden" value="< ?=$offset? >">-->
        <input id="hddIdCompany-create" type="hidden" value="<?= $company ?>">
        <fieldset class="espaciadoObjetos">
            <legend id="titleLegend-create"><b>Creacion de Campañas</b></legend>
            <input id="viewCompany" type="hidden" value="<?= $cbocompanyview ?>"/>
            <table class="ctlFull espaciadoObjetos">
                <tr>
                    <td>
                        <b>Nombre:</b>
                    </td>
                    <td>
                        <div class="divInputText" >
                            <input id="txtNombre-create" type="text" class="ctlFull" placeholder="Ingrese un nombre para la campa&ntilde;a">
                        </div>
                    </td>
                </tr>
                <!--< ?php if ($cbocompanyview == 1) { ?>-->
                <tr>
                    <td><b>Compa&ntildeia:</b></td>
                    <td><select id="cboCompany-create" class="ctlFull"></select></td>
                </tr>
                <!--< ?php } ?>-->
                <tr>
                    <td><b>Fecha Inicio:</b></td>
                    <td>
                        <div class="divInputDateText" >
                            <input id="txtFechaInicio-create" type="text" class=""  readonly="">
                        </div>
                        <div class="divInputDateBtn">
                            <img id="imgStartDate-create" src="includes/javascript/images/calendar.png" >
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><b>Fecha Termino:</b></td>
                    <td>
                        <div class="divInputDateText" >
                            <input id="txtFechaTermino-create" type="text" class="" readonly="">
                        </div>
                        <div class="divInputDateBtn">
                            <img id="imgEndDate-create" src="includes/javascript/images/calendar.png" >
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Message:</b>
                    </td>
                    <td>
                        <div class="divInputText" >
                            <textarea id="txtMessage-create" rows="5" class="ctlFull" placeholder="<?= $msg_example ?>"></textarea>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Archivo:</b>
                    </td>
                    <td>
                        <div class="divInputText" >
                            <input id="txtFile-create" type="file"  class="ctlFull" placeholder="Seleccione un archivo"/>
                        </div>
                    </td>
                </tr>
            </table>
        </fieldset>
        <fieldset class="espaciadoObjetos">
            <legend><b>Informacion:</b></legend>
            <table>
                <tr>
                    <td>
                        <ul class="ulNotasInformativas">
                            <li>
                                * La cantidad m&aacute;xima de parametros es <b>(9)</b>.
                            </li>
                            <li>
                                * El mensaje debe tener como m&aacute;ximo <b>(<?= $max_char_msg ?>)</b> caracteres, despu&eacute;s de conbinarse con los par&aacute;metros del archivo.
                            </li>
                            <li>* Los archivos a subir deben estar en formato <b>CVS</b> o <b>TXT</b></li>
                            <li>* El archivo debe contener las columnas <b>MOVIL; PARAMETRO1; PARAMETRO2 ...</b>.</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </fieldset>
        <table align="right">
            <tr>
                <td><button id="btnSave-create" type="button" >Guardar</button></td>
                <td></td>
                <td><button id="btnClean-create" type="button" >Limpiar</button></td>
            </tr>
        </table>

        <div id="divMessage-create" style="width: 100%">
        </div>
        <div id="divImgLoading-create">
            <img src="images/waiting_small.gif">
        </div>
    </div>
    <?php
}
?>

<?php

function listPendingCampaign() {
    title("Campa&ntilde;as", "Lista Pendientes", "campa&ntilde;as");

    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    $profile = $_SESSION["profile"];
    $offset = $_SESSION["OFFSET_COMPANY"];
    if (select_type_profile($profile) == 1) {
        $classtxt = "td75";
        $cbocompanyview = 1;
    } else {
        $classtxt = "td100";
        $cbocompanyview = 0;
    }
    $admin = 0;
    if (select_type_profile($profile) == 1 || select_type_profile($profile) == 2) {
        $admin = 1;
    }
    ?>
    <script type="text/javascript" src="includes/javascript/app/campaign/listPendingCampaign.js?v=<?=VERSION?>"></script>

    <div id="div-filterListCampaing">
        <fieldset class="espaciadoObjetos">
            <legend>Filtros del listado de pendientes</legend>
            <input id="txtAdmin" type="hidden" value="<?= $admin ?>"/>
            <table style="width: 100%">
                <tr>
                    <td class="<?= $classtxt ?>">Texto</td>
                    <?php if ($cbocompanyview == 1) { ?>
                        <td class="td25">Compa&ntilde;ia</td>
                    <?php } ?>
                </tr>
                <tr>
                    <td class="<?= $classtxt ?>">
                        <div class="divInputText">
                            <input id="txtData-filterListCampaing" class="ctlFull">
                        </div>
                    </td>
                    <?php if ($cbocompanyview == 1) { ?>
                        <td class="td25">
                            <select id="cboCompany-filterListCampaing" class="ctlFull"></select>
                        </td>
                    <?php } ?>
                </tr>
            </table>
        </fieldset>
        <div style="width: 100%;height: 3em;" class="espaciadoObjetos" >
            <table align="right" >
                <tr>
                    <td>
                        <button id="btnBuscar-filterListCampaing" type="button" >Buscar</button>

                    </td>
                </tr>
            </table>
        </div>
        <div id="divMessage-listCampaing" style="width: 100%">
        </div>
        <div id="divImgLoading-listCampaing">
            <img src="images/waiting_small.gif">
        </div>
    </div>
    <div id="divError-listCampaing" style="display:none;" class="divMessage espaciadoObjetos">No se encontraron campañas pendientes.</div>

    <div id="div-tableListCampaing">
        <input id="hddPage-tableListCampaing" type="hidden" value="1"/>
        <table class="ctlFull">
            <tr>
                <td>
                    <div style="margin: 0 auto; width: 270px;">
                        <div style="display: inline-block;" >
                            <span id="btnAtras-tableListCampaing" class="buttom"><</span>
                        </div>
                        <div id="divPages-tableListCampaing" style="display: inline-block;">
                            <span class="buttom">1</span>
                        </div>
                        <div style="display: inline-block;">
                            <span id="btnSiguiente-tableListCampaing" class="buttom">></span>
                        </div>
                        <div style="display: inline-block;">
                            <select id="cboRowsPage-tableListCampaing">
                                <option value="2">2</option>
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="20">20</option>
                            </select>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <div class="containerTable">
            <table style="width: 100%" class="tableReport espaciadoObjetos" >
                <thead id="headTable-tableListCampaing" class="theadTable">
                    <tr>
                        <th style="min-width: 100px;max-width: 100px;">CODIGO</th>
                        <th style="min-width: 100px;max-width: 100px;">COMPA&Ntilde;IA</th>
                        <th style="min-width: 200px;max-width: 200px;">NOMBRE</th>
                        <th style="min-width: 150px;max-width: 150px;">INICIO</th>
                        <th style="min-width: 150px;max-width: 150px;">TERMINO</th>
                        <th style="min-width: 50px;max-width: 50px;">AVANCE</th>
                        <th style="min-width: 100px;max-width: 100px;">ESTADO</th>
                        <th style="min-width: 100px;max-width: 100px;">USUARIO</th>
                        <th style="min-width: 100px;max-width: 100px;">ACCION</th>
                    </tr>
                </thead>
                <tbody id="bodyTable-tableListCampaing" class="tbodyTable">
                    <tr>
                        <td>MASTER1</td>
                        <td>CAMPAÑA 1</td>
                        <td>2018-05-05 09:00:00</td>
                        <td>2018-05-06 18:00:00</td>
                        <td>50/100</td>
                        <td>PROCESING</td>
                        <td>ADMIN</td>
                        <td>
                            <span class="buttom">||</span>
                            <span class="buttom">-</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
    <?php
}
?>
