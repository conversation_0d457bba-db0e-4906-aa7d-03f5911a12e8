<?php
include('dbConn.php');
$response = array();
$user_name = filter_input(INPUT_POST, 'user_name');
$codigo = filter_input(INPUT_POST, 'codigo');
$json = "{\"username\":\"$user_name\"}";

//Inicio de la validacion
$regex = '/^[a-zA-Z0-9\-\_\.]+$/';
$coincidencia = preg_match($regex, $user_name);
if ($coincidencia == 1) {
$url = "";
if (defined("URL_SERVICE_ACCOUNT")) {
    $url = URL_SERVICE_ACCOUNT;
}
$urlLogin = $url . "/api/auth/token";
$request = curl_init($urlLogin);
curl_setopt($request, CURLOPT_CUSTOMREQUEST, "POST");
curl_setopt($request, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
curl_setopt($request, CURLOPT_POSTFIELDS, $json);
curl_setopt($request, CURLOPT_RETURNTRANSFER, true);
// Aplicar optimizaciones de timeout
include_once('includes/php/general_funcs.php');
optimizeCurlTimeouts($request);
$responseText = curl_exec($request);
$http_code = curl_getinfo($request, CURLINFO_HTTP_CODE);
curl_close($request);


if ($http_code == 200) {
    //busca el texto HTML o html para saber si la respuesta es correcta
    $validateNoHtml = strpos($responseText, "html");
    //error_log("validateNoHtml: $validateNoHtml");
    if ($validateNoHtml === false) {
        $serviceResponse = json_decode($responseText);
        if (!is_null($serviceResponse)) {
            //validamos que el error no sea un 401 del servicio
            //error_log("status: ".$serviceResponse->status);
            if( strpos($responseText, "token") >= 0){//si no encuentra devuelve false
                $token = $serviceResponse->token;
                $response["token"] = $token;
                //now validating the username and password
                $query = " SELECT "
                        . " a.company as id_company"
                        . " , a.id as id_account"
                        . " , a.login"
                        . " , a.password"
                        . " , p2.id_profile as id_profile"
                        . " , p2.desc_profile as profile"
                        . " , t.offset "
                        . " , a.change_pass "
                        . " , c.max_total_msgs "
                        . " , pc.code "
                        . " , pc.msisdn_length"
                        . " , pc.msisdn_mask"
                        . " FROM account a INNER JOIN account_profile ac ON a.id = ac.id_account "
                        . " INNER JOIN profile p2 ON ac.id_profile = p2.id_profile "
                        . " INNER JOIN time_zone t ON a.id_time_zone = t.id_time_zone "
                        . " INNER JOIN company c ON a.company = c.id "
                        . " INNER JOIN time_zone tc ON c.id_time_zone = tc.id_time_zone "
                        . " INNER JOIN pais pc ON tc.id_pais = pc.id_pais "
                        . " WHERE a.login = ?";

                        $stmt = $dbh->prepare($query);
                        $stmt->execute(array($user_name));

                        $result = $stmt->fetch(PDO::FETCH_OBJ);
               
                        $_SESSION["id"] = $result->id_company;
                        $_SESSION["id_account"] = $result->id_account;
                        $_SESSION["user"] = $login = $result->login;
                        $_SESSION["profile"] = $result->profile;
                        $_SESSION["id_profile"] = $result->id_profile;
                        $_SESSION["OFFSET_COMPANY"] = $result->offset;
                        $_SESSION["max_msg"] = $result->max_total_msgs;
                        $_SESSION["TOKEN"] = $token;
                        $_SESSION["auth"] = "1";
                    
                        $_SESSION["PLACEHOLDER_NUMBER_MOBILE"] = $result->msisdn_mask;
                        $_SESSION["SIZE_NUMBER_MOBILE"] = $result->msisdn_length;
                        $_SESSION["REX_TYPE_NUMBER_MOBILE"] = $result->code;

                if ($result->change_pass == "1") {
                    $response["result"] = "2";
                    $response["message"] = "Redireccionando a cambio de password.";
                } else {
                    $response["result"] = "1";
                    $response["message"] = "Redireccionado al home.";
                }
            }else{
                $response["result"] = "0";
                $response["message"] = "Error al validar usuario y contraseña en el servicio";
            }
        } else {
            $response["result"] = "0";
            $response["message"] = "Error al obtener la validacion del servidor";
        }
    } else {
        $response["result"] = "0";
        $response["message"] = "Error al conectar con el servicio account";
    }
} else {
    $response["result"] = "0";
    $response["message"] = "Error al intentar enviar la peticion";
    //solo mandamos 200 caracteres para no generar problemas en el javascript
    $response["hideMessage"] = substr($responseText, 0, 200);
}
$responseJson = json_encode($response, JSON_FORCE_OBJECT);
echo $responseJson;
	

}else{
error_log("(USERNAME contiene caracter especial ");
echo "no";
}
$dbh=null;
?>
