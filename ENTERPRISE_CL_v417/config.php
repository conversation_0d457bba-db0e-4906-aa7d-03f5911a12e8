<?php
	if(!defined("const")){
		define ("const", "1");
		define ("styleButtonOne", 'onmouseover="className=\'button_on\'" onmouseout="className=\'button_off\'"');
//		define ("path_logs", '../../logs');
		define ("path_logs", '/var/log/httpd/');
		define ("path_loadfiles", '../loadFiles');
		define ("COMPANY_NAME",'MCS Enterprise Chile');
		define ("PLACEHOLDER_NUMBER_MOBILE", "");
		define ("SIZE_NUMBER_MOBILE", '');
		define ("REX_TYPE_NUMBER_MOBILE", "");
		define ("MAX_LENGTH_TEXT_MSG", 160);
		define ("VERSION", "4.17.0");

		define ("SERVERDB", "35.223.25.116:3306");
		define ("USERDB", "entersql");
		define ("PASSDB", "pass.mobid");
		define ("DB", "mcs_enterprise_cl");
        // Configuración de servicios - Mantener compatibilidad con versión anterior
        define ("URL_SERVER", "https://qa-ares.mobid.cl/");

        // Nuevas URLs de servicios microservicios (entorno dev)
        // Temporalmente usar servidor anterior hasta que el endpoint esté disponible
        define ("URL_SERVICE_ACCOUNT", "https://enterprise-account-api.dev.smscorpchile.bemobi.team/account-cl");
        define ("URL_SERVICE_GENERAL", "https://enterprise-general-api.dev.smscorpchile.bemobi.team/general-cl");
        define ("URL_SERVICE_BLACKLIST", "https://enterprise-blacklist-api.dev.smscorpchile.bemobi.team/blacklist-cl");

        // Mantener URL_SERVICE_REPORT con el servidor anterior hasta migración
        define ("URL_SERVICE_REPORT", "https://enterprise-report-api.dev.smscorpchile.bemobi.team/report-cl");
       
		/* TAMAÑO MAXIMO DE LOS ARCHIVOS A CARGAR */
		define ("MAX_ROW_MSG_UPLOAD", 50000);
		/* NUMERO DE INSERT ACUMULADOS DURANTE LA REVISION
		 * PARA INSERT MASIVO DE LAS FILAS A LA BASE DE DATIOS */
		define ("MAX_STACK_MSG_UPLOAD", 100);
		/* NUMERO MAXIMO DE ERRORES PARA LOS ARCHIVOS AL LLEGAR A ESTE NUMERO 
		 * SE DETIENE LA REVISION DEL ARCHIVO Y SE INFORMAN LOS ERRORES */
		define ("MAX_SIZE_ERROR_FILE", 100);
		
	}
?>
