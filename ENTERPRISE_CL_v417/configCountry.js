$(document).ready(function(){
	setSession();
});

function setSession(){
	$.ajax({
		url: "ajaxfuncs/general/SetSession.php",
		type: 'GET',
		processData: false,
		contentType: "application/json;charset=UTF-8",
		success: function (data, textStatus, jqXHR) {
			sessionStorage.setItem ('config', data);
			//console.log(JSON.parse(data));
			//console.log(sessionStorage.getItem('config'));
		},
		error: function (jqXHR, textStatus, errorThrown) {
			console.log("error");
		}
	});	
}
