<?php
setcookie("cookie", "mcs", time() + 1200);
include ('config.php');
include ('dbConn.php');

include ('includes/php/general_funcs.php');
include ('includes/php/general_function_control.php');
include ('includes/php/general_function_page.php');
include ('includes/php/general_function_return.php');

include ('includes/php/sections_funcs.php');
include ('includes/php/sections_campaign.php');
include ('includes/php/sections_maintenance.php');
include ('includes/php/sections_provisioning.php');
include ('includes/php/sections_report_gesReportFile_old.php');
include ('includes/php/sections_report_gesReportHistory_old.php');
include ('includes/php/sections_report_gesReportMO_old.php');
include ('includes/php/sections_report_gesReport_old.php');
include ('includes/php/sections_report_gesStatusHistory_old.php');
include ('includes/php/sections_report_gesStatus_old.php');
include ('includes/php/sections_report_new_billing.php');
include ('includes/php/sections_report_new_custom.php');
include ('includes/php/sections_report_new_load_mo.php');
include ('includes/php/sections_report_new_load_mt_daily.php');
include ('includes/php/sections_report_new_load_mt_historical.php');
include ('includes/php/sections_report_new_queue_report_detail.php');
include ('includes/php/sections_send.php');
include ('includes/php/sections_suport.php');

if (empty($_COOKIE['cookie'])) {
	header('Location: expire_session.php');
}

if (empty($_SESSION["id"])) {
	header('Location: expire_session.php');
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>MCS | <?= COMPANY_NAME ?></title>
		
		<link rel="stylesheet" href="includes/style/style.css?v=<?=VERSION?>" type="text/css" media="screen" />
		<link rel="stylesheet" type="text/css" media="all" href="includes/javascript/calendar/calendar-green.css" title="win2k-cold-1" />
		<link rel="stylesheet" href="includes/style/menu.css" type="text/css" media="screen" />
		<link rel="shortcut icon" type="image/x-icon" href="images/favicon.ico" />
		<script type="text/javascript" src="includes/javascript/calendar/calendar.js"></script>
		<script type="text/javascript" src="includes/javascript/calendar/calendar-es.js"></script>
		<script type="text/javascript" src="includes/javascript/calendar/calendar-setup.js"></script>
		<script type="text/javascript" src="includes/javascript/extra_funcs.js"></script>
		<script type="text/javascript" src="includes/javascript/functions.js"></script>
                <script type="text/javascript" src="includes/javascript/app/common.js?v=<?=VERSION?>"></script>
		<script type="text/javascript" src="jquery-3.7.1.min.js"></script>
	</head>
	<body>
		<?php
                
                        $misdn =   $_SESSION["PLACEHOLDER_NUMBER_MOBILE"];
                        $lengh =   $_SESSION["SIZE_NUMBER_MOBILE"] ;
                        $codePais =  $_SESSION["REX_TYPE_NUMBER_MOBILE"];
                        
			$maxLengMessage="";
			if(defined("MAX_LENGTH_TEXT_MSG")){
				$maxLengMessage = MAX_LENGTH_TEXT_MSG;
			}
			$placeholderFormatMovil="";
			if($misdn){
				$placeholderFormatMovil = $misdn;
			}
			$maxSizeMobilNumber="";
			if($lengh){
				$maxSizeMobilNumber = $lengh;
			}
			$rexPais="";
			if($codePais){
				$rexPais = $codePais;
			}
			$offset=$_SESSION["OFFSET_COMPANY"];
			$id_profile = $_SESSION["id_profile"];
                        
			?>
		<input id="offset" type="hidden" value="<?=$offset?>"></input>
		<input id="txtIdProfile" type="hidden" value="<?=$id_profile?>"></input>
		<input id="maxlengMessage" name="maxlengMessage" type="hidden" value="<?=$maxLengMessage?>"></input>
		<input id="placeHolderFormatMobilNumber" name="placeHolderFormatMobilNumber" type="hidden" value="<?=$placeholderFormatMovil?>"></input>
		
		<input id="maxSizeMobilNumber" name="maxSizeMobilNumber" type="hidden" value="<?=$maxSizeMobilNumber?>"></input>
		<input id="rexPais" name="maxSizeMobilNumber" type="hidden" value="<?=$rexPais?>"></input>
		
		<table width="100%" style="height:100%;" cellpadding="0" cellspacing="0">
			<tr>
				<td height="150" bgcolor="#58585a">
					<table width="950" style="height:150px;" align="center" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td height="100" width="100" valign="middle" align="left"><img src="images/logo.png" /></td>
							<td height="100" width="600" valign="middle" align="left"></td>
							<td height="100" width="200" valign="middle" align="right"><?php 
                                                        session(); 
                                                        ?></td>
						</tr>
						<tr class="trmenu">
							<td height="40" valign="bottom" width="100%" colspan="3"><?php 
                                                        menu(); 
                                                        ?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td align="center" valign="top" height="400" bgcolor="#f0f0f0">
					<table width="900" border="0">
						<tr>
							<td align="left" valign="top" style="padding-top: 30px; padding-bottom: 30px;">
								<br />
								<?php 
                                                                include('modulos.php'); 
                                                                ?>
							</td>
						</tr>
					</table>

				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td height="100" bgcolor="#58585a" align="center"><?php 
                                copy_rights(); 
                                ?></td>
			</tr>
		</table>
	</body>
</html>
