<?php
setcookie("cookie", "mcs", time() + 1200,"","",true,true);
include ('includes/php/general_funcs.php');
include ('includes/php/sections_funcs.php');
include ('config.php');
$login = $_SESSION["username"];
$code = $_SESSION["code"];
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>

<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>MCS | <?= COMPANY_NAME ?></title>
		<LINK REL="stylesheet" TYPE="text/css" HREF="../includes/style/style.css"/>
		<link rel="shortcut icon" type="image/x-icon" href="../images/favicon.ico" />
	
			<script language="javascript" type="text/javascript" src="../includes/javascript/functions.js"></script>
			<script src="../jquery-3.7.1.min.js" type="text/javascript" language="javascript"></script>
			<script type="text/javascript" src="../includes/javascript/app/login.js?v=<?=VERSION?>" ></script>
			<script language="javascript">
				//  Developed by Roshan Bhattarai 
				//  Visit http://roshanbh.com.np for this script and more.
				//  This notice MUST stay intact for legal use

				$(document).ready(function ()
				{
					$("#login_form").submit(function ()
					{
						//remove all the class add the messagebox classes and start fading
						//$("#msgbox").removeClass().addClass('messagebox').text('Validando ...').fadeIn(100);
						//check the username exists or not from ajax
					//	var codigo = $('#codigo').val();
					//	var password = $('#password').val();
					//	console.log(password);
						
					$.post("ajax_code.php", {password:$('#password').val() , rand: Math.random()}, function (data)
						{
							if(IsJsonString(data)){
                                                        data = JSON.parse(data);
                                                       if (data.result == '1') {
						$.post("ajax_token.php", {user_name:$('#user_name').val() , rand: Math.random()}, function (data)
						{
        					    if(IsJsonString(data)){
                                                        data = JSON.parse(data);
                                                       if (data.result == '1') {
                                                            sessionStorage.setItem("token", data.token); 
                                                            showMessage(data.message,'mcs.php?seccion=home');
                                                        }else if (data.result == '2'){
                                                            sessionStorage.setItem("token", data.token); 
                                                            showMessage(data.message, 'changePass.php');
                                                        }
														else if (data.result == '0'){
                                                            showMessage(data.message, null);
                                                            console.log(data.hideMessage)
                                                        } else {
                                                            showMessage(data.message, null);
                                                        }
                                                    }else{
                                                        showMessage("Error al obtener informacion de login", null);
                                                    }
						});
						return false; //not to post the  form physically
					}
				else if (data.result == '2'){
					showMessage(data.message, null);
                    console.log(data.hideMessage)
                                                        }
													}

					});
						return false; //not to post the  form physically

					});
					//now call the ajax also focus move from 

				});
				function showMessage(message, url){
					$("#msgbox").fadeTo(100, 1, function () {
						$("#msgbox").html(message);
						$("#msgbox").addClass('messageboxok');
						if(url!=null){
							redirect(url);
						}
					});
				}
				
				function redirect(url){
					$("#msgbox").fadeTo(100, 1, function () {
						document.location = url;
					});
				}
			</script>
	</head>
	<body>
		<table width="100%" style="height:100%;" cellpadding="0" cellspacing="0">
			<tr>
				<td height="150" bgcolor="#58585a">
					<table width="900" height="150" align="center" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td height="100" width="100" valign="middle" align="left"><img src="images/logo.png" /></td>
							<td height="100" width="800" valign="middle" align="right"></td>
						</tr>
						<tr>
							<td height="50" valign="bottom" colspan="2">

							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td align="center" valign="top" height="400" bgcolor="#f0f0f0">
					<table width="900" border="0">
						<tr>
							<td align="left" valign="top" style="padding-top: 30px; padding-bottom: 30px;">
								<table width="900" border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td width="450" align="left" valign="top">
											<img src="images/intro.jpg" />
											<div style="width: 255px; height: 50px; background-color:#d10660; color:#fff; padding: 10px; text-align: center;">
												<span style="font-family: 'Merriweather Sans', sans-serif;	font-size: 20px; ">Mobile Corporate Suite<br /> <?= COMPANY_NAME ?></span></div></td>
										<td width="450">
											<span class="container-text-title">Ingrese Codigo</span>
											<br /><br />
											<form method="post" action="" id="login_form" autocomplete="off">
												<table width="300" border="0" cellpadding="0" cellspacing="0">
													<tr>
													<td height="30" align="left"><input name="user_name" type="hidden" id="user_name" value="<?=$login?>" maxlength="30" />
													</td>
													</tr>
													<tr>
														<td height="30" align="left"><input name="password" type="password" id="password" value="" maxlength="30"  autocomplete="off" /></td>
													</tr> 
													<tr>
														<td height="30" align="left" valign="top"><br/>
															<input name="Submit" type="submit" id="submit" value="INGRESAR" class="buttom" />
														</td>
													</tr>
													
												</table>
												<table>
													<tr>
														<td height="50" align="left" valign="top">
															<br/>
															<span id="msgbox" style="display: none"></span>
														</td>
													</tr>
												</table>
											</form>
										</td>
									</tr>
								</table>

							</td>
						</tr>
					</table>

				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td height="100" bgcolor="#58585a" align="center"><?php copy_rights(); ?></td>
			</tr>
		</table>
	</body>
</html>
