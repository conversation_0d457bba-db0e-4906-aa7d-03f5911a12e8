<?php

//session_start();
//  Developed by <PERSON><PERSON><PERSON> 
//  Visit http://roshanbh.com.np for this script and more.
//  This notice MUST stay intact for legal use
//Connect to database from here
include('dbConn.php');

//get the posted values
$user = $_SESSION["user"];
$passAct = $_POST['passAct'];
$passNew = $_POST['passNew'];
$passReNew = $_POST['passReNew'];

//now validating the username and password
$query = " SELECT "
		. " a.company"
		. " , a.id"
		. " , a.login"
		. " , a.password"
		. " , p2.desc_profile as profile"
		. " , t.offset "
		. " , a.change_pass"
		. " FROM account a INNER JOIN account_profile ac ON a.id = ac.id_account "
		. " INNER JOIN profile p2 ON ac.id_profile = p2.id_profile "
		. " INNER JOIN time_zone t ON a.id_time_zone = t.id_time_zone "
		. " WHERE a.login = ?";

$stmt = $dbh->prepare($query);
$stmt->execute(array($login));


$result = $stmt->fetch(PDO::FETCH_OBJ);


	$company = $result->company;
    $login = $result->login;
    $profile = $result->profile;
    $offset = $result->offset;
    $id = $result->id;
		
//if username exists

if (!empty($login)) {
	if ( $result->password == $passAct) {
		if($passNew == $passReNew){
			$query = "";
			$query .= " UPDATE  account ";
			$query .= " SET ";
			$query .= " password = ? ";
			$query .= " , change_pass = 0 ";
			$query .= " WHERE id = ? ";

			$stmt->execute(array($passNew,$id));
			

			$_SESSION["id"] = $company;
			$_SESSION["user"] = $login;
			$_SESSION["profile"] = $profile;
			$_SESSION["OFFSET_COMPANY"] = $offset;
			$_SESSION["auth"] = "1";
			echo "0";
		}else{
			echo "3";
		}
	} else {
		echo "2";
	}
} else {
	echo "1";
}
$dbh=null;
?>
