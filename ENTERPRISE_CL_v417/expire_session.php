<?php
include ('includes/php/general_funcs.php');
include ('includes/php/general_function_page.php');

session_start();
session_unset();
session_destroy();	
if(isset($_SESSION['auth'])){
	session_destroy();
}
setcookie("cookie", "mcs", time() - 60, "/", "");
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>MCS | Mobile Corporate Suite</title>
		<LINK REL="stylesheet" TYPE="text/css" HREF="includes/style/style.css"/>
		<link rel="shortcut icon" type="image/x-icon" href="images/favicon.ico" />
			<script language="javascript" type="text/javascript" src="includes/javascript/functions.js"></script>
			<script src="jquery-3.7.1.min.js" type="text/javascript" language="javascript"></script>
			<script language="javascript">
				//  Developed by Roshan Bhattarai 
				//  Visit http://roshanbh.com.np for this script and more.
				//  This notice MUST stay intact for legal use

				$(document).ready(function ()
				{
					$("#login_form").submit(function ()
					{
						//remove all the class add the messagebox classes and start fading
						$("#msgbox").removeClass().addClass('messagebox').text('Validando ...').fadeIn(1000);
						//check the username exists or not from ajax
						$.post("ajax_login.php", {user_name: $('#user_name').val(), password: $('#password').val(), rand: Math.random()}, function (data)
						{
							if (data == 'yes') //if correct login detail
							{
								$("#msgbox").fadeTo(200, 0.1, function ()  //start fading the messagebox
								{
									//add message and change the class of the box and start fading
									$(this).html('Ingresando ...').addClass('messageboxok').fadeTo(900, 1,
											function ()
											{
												//redirect to secure page
												document.location = 'mcs.php?seccion=sendNow';
											});

								});
							} else
							{
								$("#msgbox").fadeTo(200, 0.1, function () //start fading the messagebox
								{
									//add message and change the class of the box and start fading
									$(this).html('Error de Usuario o Password').addClass('messageboxerror').fadeTo(900, 1);
								});
							}

						});
						return false; //not to post the  form physically
					});
					//now call the ajax also focus move from 
					$("#password").blur(function ()
					{
						$("#login_form").trigger('submit');
					});
				});
			</script>
	</head>
	<body>
		<table width="100%" style="height:100%;" cellpadding="0" cellspacing="0">
			<tr>
				<td height="150" bgcolor="#58585a">
					<table width="900" height="150" align="center" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td height="100" width="100" valign="middle" align="left"><img src="images/logo.png" /></td>
							<td height="100" width="800" valign="middle" align="right"></td>
						</tr>
						<tr>
							<td height="50" valign="bottom" colspan="2">

							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td align="center" valign="top" height="400" bgcolor="#f0f0f0">
					<table width="900" border="0">
						<tr>
							<td align="left" valign="top" style="padding-top: 30px; padding-bottom: 30px;">
								<br />
								<br />
								<br />
								<br />
								<br />
								<br />
								<p align="center"><span class="container-text-title">Tu Sesi&oacute;n ha expirado. Ir al inicio <a href="index.php">[aqu&iacute;]</a></span></p>

							</td>
						</tr>
					</table>

				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td height="100" bgcolor="#58585a" align="center"><?php copy_rights(); ?></td>
			</tr>
		</table>
		</td>
		</tr>
		</table>  
	</body>
</html>
