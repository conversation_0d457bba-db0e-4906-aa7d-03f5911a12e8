<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');
	
	$subject 		= $_POST['subject']; 
	$name		 	= $_POST['name'];
	$contact	 	= $_POST['contact'];
	$email		 	= $_POST['email'];
	$msgText 	= $_POST['msgText'];
	$company 	= $_SESSION["id"];
	
	try {
		$query = "SELECT description FROM company WHERE id = :company";
		$stmt = $dbh->prepare($query);
		$stmt->bindParam(':company', $company, PDO::PARAM_INT);
		$stmt->execute();
		
		$row = $stmt->fetch(PDO::FETCH_ASSOC);
		$company_name = $row['description'];
		
		// Ahora, $company_name contiene la descripción de la empresa
	} catch (PDOException $e) {
		echo "Error en la consulta en company: " . $e->getMessage();
	}
	
	$sdestinatario		= "<EMAIL>";
	$ssubject 			= $subject;
	$shtml				= nl2br($msgText);
	$sfrom				= "MCS - $company_name <$email>";
	
	$smsgtext = "<html>
					<span style='font-family: Trebuchet MS; font-size: 13px; color: #000000;'>
					Estimados,
					<br><br>
					El Cliente <b>$company_name</b> reporta el siguiente problema desde la aplicaci&oacute;n TMC:
					<br><br>
					El mensaje es el siguiente:
					<br><br>
					$shtml
					<br><br>
					Su nombre es: <b>$name</b><br>
					El n&uacute;mero de contacto es: <b>$contact</b>
					</span>
					</html>";
	$sheader="From: ".$sfrom."\n"; 
	$sheader=$sheader."X-Mailer:PHP/".phpversion()."\n"; 
	$sheader=$sheader."Mime-Version: 1.0\n"; 
	$sheader=$sheader."Content-Type: text/html"; 
	mail($sdestinatario,$ssubject,$smsgtext,$sheader);
	
	?>
	
	<div class="alert_big"> <img src="images/icons/confirm.png"> Su e-mail ha sido enviado exitosamente.</div>

	<?php 
	$dbh=null;
	$action = $name." ==> ".$contact." ==> ".$email." ==> ".$ssubject." ==> ".$msgText; 
//	log_tmc($company, "Soporte", "Contacto", $action, $login); 
	?>