<?php
	
include('../dbConn.php'); 
include('../includes/php/general_funcs.php'); 
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');
	$company		= $_SESSION["id"];
	$login			= $_SESSION["user"];
	$filename		= $_POST['filetxt'];
	$idGroup		= obtener_parameter('idGroup', 'POST');
	$path			= "../loadFiles/";
	$lines			= file($path.$filename);
	add_process ($company, "UPLOAD", $filename,$login);
	$process = return_process ($company, "UPLOAD");

	foreach ($lines as $line_num => $line) 
	{
		$row		= explode(";", $line);
       	$phone		= trim($row[0]);
  		$name		= trim($row[1]);
  		$lastname	= trim($row[2]);
		$carrier	= return_carrier($phone);
		
		$checkPhone		= return_phone_exists($phone);
		$checkUserGroup	= return_check_phone_group($phone, $idGroup);
		
		if($checkPhone == "N")
		{
			//("INSERT into provUser (company, phone, carrier, name1, name2, created, login) VALUES ('$company', '$phone', '$carrier','$name', '$lastname', UTC_TIMESTAMP(), '$login')") or die ("Error al realizar el env&iacute;o (Codigo Error: ".mysql_errno()." - ".mysql_error()." ".del_process($company, $process).")");
			//se debe revisar
			try {
				$query = "INSERT INTO provUser (company, phone, carrier, name1, name2, created, login) VALUES (:company, :phone, :carrier, :name, :lastname, UTC_TIMESTAMP(), :login)";
				$stmt = $dbh->prepare($query);
				
				$stmt->bindParam(':company', $company, PDO::PARAM_INT);
				$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
				$stmt->bindParam(':carrier', $carrier, PDO::PARAM_STR);
				$stmt->bindParam(':name', $name, PDO::PARAM_STR);
				$stmt->bindParam(':lastname', $lastname, PDO::PARAM_STR);
				$stmt->bindParam(':login', $login, PDO::PARAM_STR);
				
				$stmt->execute();
				
				echo "Inserción exitosa en provUser";
			} catch (PDOException $e) {
				echo "Error en la inserción en provUser: " . $e->getMessage();
				del_process($company, $process);
			}
			
		}
		
		if($checkUserGroup == false)
		{
			//("INSERT into UserGroup (company, phone, idGroup, login) VALUES ('$company', '$phone', '$idGroup', '$login')") or die ("Error al realizar el env&iacute;o (Codigo Error: ".mysql_errno()." - ".mysql_error()." ".del_process($company, $process).")");
			try {
				$query = "INSERT INTO UserGroup (company, phone, idGroup, login) VALUES (:company, :phone, :idGroup, :login)";
				$stmt = $dbh->prepare($query);
				
				$stmt->bindParam(':company', $company, PDO::PARAM_INT);
				$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
				$stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
				$stmt->bindParam(':login', $login, PDO::PARAM_STR);
				
				$stmt->execute();
				
				echo "Inserción exitosa en UserGroup";
			} catch (PDOException $e) {
				echo "Error en la inserción en UserGroup: " . $e->getMessage();
				del_process($company, $process);
			}
		}
	}
		
		?>
	<br>
	<div class="alert_big">
		El archivo <b><?=$filename?></b> ha sido cargado exitosamente.
	</div>
    <br />
    <br />
    <a href="?seccion=provGroups" class="styleButtonOne"><span class="buttom">Volver</span></a>
     
	<?php 
	$dbh=null;
	//$action = $company." ==> ".utf8_decode($filename);  
//	log_tmc($company, "Envio SMS", "Desde Archivo", $action, $login); 
	?>
