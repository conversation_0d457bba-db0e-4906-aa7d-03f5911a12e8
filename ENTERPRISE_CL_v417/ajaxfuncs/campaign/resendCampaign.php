<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {

            $id = obtener_parameter("id", "GET");
            $query = "";
            $query .= " SELECT COUNT(*) CANT ";
            $query .= " FROM campaign c ";
            $query .= " WHERE c.id_campaign = $id ";

            $sql = mysql_query($query);

            if ($sql != null) {
                $row = mysql_fetch_array($sql);
                $cant = $row["CANT"];
                if (!empty($cant)) {

                    $query = "";
                    $query .= " SELECT c.status ";
                    $query .= " FROM campaign c ";
                    $query .= " WHERE c.id_campaign = $id ";

                    $sql = mysql_query($query);
                    if (!empty($sql)) {
                        $row = mysql_fetch_array($sql);
                        $status = $row["status"];
                        if (!empty($status)) {
                            if ($status === "PAUSED") {
                                $query = "";
                                $query .= " UPDATE campaign ";
                                $query .= " SET status='SENDING' ";
                                $query .= " WHERE id_campaign = $id ";
                                mysql_query($query);

                                $response["result"] = "OK";
                                $response["data"] = 1;
                                $response["message"] = "Se ha REINICIADO los envios de la campaña.";
                            } else {
                                $response["result"] = "ERROR";
                                $response["message"] = "para REINICIAR los envios de una campaña debe estar en estado PAUSADO.";
                            }
                        } else {
                            $response["result"] = "ERROR";
                            $response["message"] = "Error al obtener el estado de la campaña.";
                        }
                    } else {
                        $response["result"] = "ERROR";
                        $response["message"] = "Error al intentar obtener el estado de la campaña.";
                    }
                } else {
                    $response["result"] = "ERROR";
                    $response["message"] = "Registro no encontrado.";
                }
            } else {
                $response["result"] = "ERROR";
                $response["message"] = "Registro no encontrado.";
            }
        } else {
            $response["result"] = "ERROR";
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);

