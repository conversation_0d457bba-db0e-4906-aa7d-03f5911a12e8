<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $login = $_SESSION["user"];
            $company = $_SESSION["id"];
            $profile = $_SESSION["profile"];
            $offset = $_SESSION["OFFSET_COMPANY"];
            $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

            $selcompany = obtener_parameter("company", "GET");
            $filtertext = obtener_parameter("txtfilter", "GET");
            $page = obtener_parameter("page", "GET");
            $rows = obtener_parameter("rows", "GET");
            $cant_rows_page = 10;
            if ($rows == null) {
                $cant_rows_page = 10;
            } else {
                $cant_rows_page = $rows;
            }

            $limit = 0;
            $sql_filtro = "";
            if (!empty($page)) {
                $limit = $page;
                if ($limit > 0) {
                    $limit -= 1;
                }
            } else {
                $limit = 0;
            }

            if (select_type_profile($profile) != 1) {
                if (select_type_profile($profile) != 2) {
                    $sql_filtro .= " AND a.login = '$login' ";
                }
                $sql_filtro .= " AND COALESCE(com.id, 0) = $company ";
            }

            if (!empty($selcompany)) {
                $sql_filtro .= " AND COALESCE(com.id, 0) = $selcompany ";
            }

            if (!empty($filtertext)) {
                $sql_filtro .= "AND ( ";
                $sql_filtro .= "	com.nom_corto LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR c.description_campaign LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR a.login LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR c.date_start_campaign LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR c.date_start_campaign LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR c.status LIKE '%$filtertext%' ";
                $sql_filtro .= " ) ";
            }
            $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

            $query = "";

            $query .= " SELECT ";
            $query .= " com.nom_corto ";
            $query .= " , c.id_campaign ";
            $query .= " , c.description_campaign ";
            $query .= " , c.date_start_campaign - interval $interval minute AS date_start_campaign ";
            $query .= " , c.date_end_campaign - interval $interval minute AS date_end_campaign";
//			$query .= " , c.date_end_campaign ";
            $query .= " , c.status ";
            $query .= " , c.reagendar ";
            $query .= " , a.login ";
            $query .= " , sum(IF(status_detcamp='PROCESSED',1,0)) as proceced ";
            $query .= " , sum(IF(status_detcamp is not null,1,0)) as total ";
            $query .= " FROM ";
            $query .= " campaign c ";
            $query .= " INNER JOIN detail_campaign d ON c.id_campaign = d.id_campaign ";
            $query .= " INNER JOIN company com ON c.id_company = com.id ";
            $query .= " INNER JOIN account a ON c.id_account = a.id  ";
            $query .= " WHERE 1 ";
            $query .= " $sql_filtro";
            $query .= " GROUP BY c.id_campaign ";
            $query .= " ORDER BY c.date_start_campaign DESC ";

            //Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
            header('Content-Type: application/json');

            $rawdata = array();
            $i = 0;

            $offset_rows = $limit * $cant_rows_page;
            $cant_rows = get_count_row_query($query);
            $textsql = "$query LIMIT $cant_rows_page OFFSET $offset_rows";

            $sql = mysql_query($textsql);
            if ($sql != null) {
                while ($row = mysql_fetch_array($sql)) {
                    $fila = array();
                    $fila["id"] = $row["id_campaign"];
                    $fila["company"] = $row["nom_corto"];
                    $fila["nombre"] = $row["description_campaign"];
                    $fila["datestart"] = $row["date_start_campaign"];
                    $fila["dateend"] = $row["date_end_campaign"];
                    $fila["estado"] = $row["status"];
                    $fila["reagendar"] = $row["reagendar"];
                    $fila["user"] = $row["login"];
                    $fila["proceced"] = $row["proceced"];
                    $fila["total"] = $row["total"];
                    $rawdata[$i] = $fila;
                    $i++;
                }
                $response["result"] = "OK";
                $response["data"] = $rawdata;
                $response["totalRows"] = $cant_rows;
                $response["message"] = "Datos obtenidos";
            } else {
                $response["result"] = "ERROR";
                $response["data"] = null;
                $response["message"] = "Error al obtener los datos.";
            }
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
?>
