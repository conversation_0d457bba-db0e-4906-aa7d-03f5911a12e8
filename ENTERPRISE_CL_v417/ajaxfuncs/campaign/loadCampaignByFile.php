<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $site = $_SESSION["SITE_ROOT"];
            $path_loadFiles = $_SERVER["DOCUMENT_ROOT"] . "/" . $site;
            $ruta_loadFiles = $path_loadFiles . "/loadFiles";
            $path_loadFiles = "../../loadFiles";
            $format_date_file = "dmY_His";
            $format_date = "Y-m-d H:i:s";
            $carrier = "UNKNOWN";
            $id_process = 0;

            $login = $_SESSION["user"];
            $company = $_SESSION["id"];
            $profile = $_SESSION["profile"];
            $offset = $_SESSION["OFFSET_COMPANY"];
            $misdn = $_SESSION["PLACEHOLDER_NUMBER_MOBILE"];
            $lengh = $_SESSION["SIZE_NUMBER_MOBILE"];
            $codePais = $_SESSION["REX_TYPE_NUMBER_MOBILE"];


            $selcompany = obtener_parameter("idcompany", "POST");
            //revisar aca por que no entra a este if
            if ($selcompany == "undefined" || $selcompany == null || empty($selcompany)) {
                $selcompany = $company;
            }
            $selNombre = obtener_parameter("nombre", "POST");
            $selFechaInicio = obtener_parameter("fechainicio", "POST");
            $selFechaTermino = obtener_parameter("fechatermino", "POST");
            $selMessage = obtener_parameter("message", "POST");

            $maximumRecordsPerInsert = 100;
            $max_characters_msg = 160;
            $max_row_file_upload = 50000;
            $size_number_movil = 10;
            $max_size_error_file = 100;
            $placeholder_number_mobil = "";
            $rex_number_mobil = "";

            if (defined("MAX_STACK_MSG_UPLOAD")) {
                $max_stack_msg_upload = MAX_STACK_MSG_UPLOAD;
            }
            if (defined("MAX_LENGTH_TEXT_MSG")) {
                $max_characters_msg = MAX_LENGTH_TEXT_MSG;
            }
            if (defined("MAX_ROW_MSG_UPLOAD")) {
                $max_row_file_upload = MAX_ROW_MSG_UPLOAD;
            }
            if ($lengh) {
                $size_number_movil = $lengh;
            }
            if ($misdn) {
                $placeholder_number_mobil = $misdn;
            }
            if ($codePais) {
                $rex_number_mobil = $codePais;
            }
            if (defined("MAX_SIZE_ERROR_FILE")) {
                $max_size_error_file = MAX_SIZE_ERROR_FILE;
            }

            $selfile = str_replace(" ", "_", $_FILES['file']["name"]);
            $fileType = pathinfo($selfile);
            $file_name = $fileType['filename'];
            $file_extension = $fileType['extension'];
            $date_local_file = return_date_now_format($format_date_file);
            $reagendar = 1;
            $filas = 0;
            $col_fijas = 1;
            $date_now = return_date_now_format($format_date);
            $date_gmt = return_date_after_format_gmt($date_now, $format_date);
            $id_process = add_process_uploadfile("UPLOAD_CAMPAIGN", $selfile, 0, $date_now);

            $filename_full = $file_name . "_id_" . $id_process . "_" . $date_local_file . "." . $file_extension;
            $source_file = $_FILES['file']['tmp_name'];
            $target_file = $ruta_loadFiles . "/" . $filename_full;

            if ($file_extension === "txt" || $file_extension === "csv" || $file_extension === "TXT" || $file_extension === "CSV") {
                if (move_uploaded_file($source_file, $target_file)) {
                    $encoding = obtenerEncoding($target_file);
                    $filename_utf8 = modificarEncoding($ruta_loadFiles, $filename_full, $encoding);
                    $arr_lines = file($ruta_loadFiles . '/' . $filename_utf8);
                    $lengh_lines = count($arr_lines);
                    if ($lengh_lines <= $max_row_file_upload) {
                        if (!empty($selMessage)) {
                            $arrParametros = obtenerParametros($selMessage);
                            $countParameters = count($arrParametros);
                        }
                        $contColumsEncontradas = 1 + $countParameters;
                        $line = $arr_lines[0];
                        $datos = explode(";", $line);
                        $cantColumData = count($datos);
                        $col_fijas = 1;
                        if ($cantColumData == $contColumsEncontradas) {
                            $status = "PROCESSING";
                            $result = updateProcessFile($filename_utf8, $id_process, $lengh_lines, "", $status);
                            $errorData = [];
                            $data_insert = "";
                            for ($i = 0; $i < $lengh_lines; $i++) {
                                /* esto es para salir del for por exceso de errores */
                                if (count($errorData) > $max_size_error_file) {
                                    $i = $lengh_lines + 1;
                                }
                                $fila = $i + 1;
                                $line = quitarEnter($arr_lines[$i]);
                                if (strlen($line) > 0) {
                                    $datos = explode(";", $line);
                                    $cantColumData = count($datos);
                                    if ($cantColumData == $contColumsEncontradas) {
                                        $messageAux = reeplaceParametersMsg($datos, $arrParametros, $selMessage, $col_fijas);
                                        $mobil = $datos[0];

                                        //validacion de numero mobil
                                        if (validate_format_phone($mobil, $rex_number_mobil)) {
                                            $carrier = return_carrier($mobil);
                                        } else {
                                            $error_msg = "El numero no cumple con el formato requerido de $size_number_movil digitos ($placeholder_number_mobil)";
                                            array_push($errorData, "Error Linea $fila: " . $error_msg);
                                        }

                                        //validacion de mensaje
                                        if (empty($messageAux)) {
                                            $error_msg = "Mensaje Vacio";
                                            array_push($errorData, "Error Linea $fila: " . $error_msg);
                                        }
                                        if (strlen($messageAux) > $max_characters_msg) {
                                            $error_msg = "El largo del mensaje no debe ser superior a los $max_characters_msg caracteres";
                                            array_push($errorData, "Error Linea $fila: " . $error_msg);
                                        }

                                        if (empty($data_insert)) {
                                            $data_insert = "($id_process, $mobil, '$date_now', '$date_gmt', '$messageAux', '$carrier', 'PENDING' )";
                                        } else {
                                            $data_insert .= ", ($id_process, $mobil, '$date_now', '$date_gmt', '$messageAux', '$carrier', 'PENDING' )";
                                        }
                                    } else {
                                        $error_msg = "La fila contiene $cantColumData de $contColumsEncontradas columnas requeridas.";
                                        array_push($errorData, "Error Linea $fila: " . $error_msg);
                                    }
                                    if ($i % $max_stack_msg_upload == 0) {
                                        $id = insertDetailTableTemporal($data_insert);
                                        $data_insert = "";
                                    }
                                }
                            }

                            $id = insertDetailTableTemporal($data_insert);

                            if (count($errorData) == 0) {
                                $date_start_gmt = return_date_after_format_gmt($selFechaInicio, $format_date);
                                $date_end_gmt = return_date_after_format_gmt($selFechaTermino, $format_date);
                                $id_campaign = insertCampaign($selcompany, $selNombre, $date_start_gmt, $date_end_gmt, $filename_utf8, $reagendar, $lengh_lines);
                                $id_detail = insertDetailCampaign($id_process, $id_campaign);
                                updateStatusProcess($id_process, "", "PROCESSED");
                                $response["result"] = "OK";
                                $response["code"] = 0;
                                $response["data"] = $id_campaign;
                                $response["message"] = "La campaña ha sido cargada, con el codigo de seguimiento $id_campaign.";
                            } else {
                                $response["result"] = "ERROR";
                                $response["data"] = $errorData;
                                $response["code"] = 1006;
                                $response["message"] = "El archivo posee errores favor de revisar y cargar nuevamente.";
                                updateStatusProcess($id_process, $response["message"] . ": " . $errorData[0], "FAILED");
                            }
                        } else {
                            $columnas = textoNombreColumnasFileCampaing($arrParametros);
                            $response["result"] = "ERROR";
                            $response["code"] = 1005;
                            $response["message"] = "El archivo contiene $cantColumData de $contColumsEncontradas columnas requeridas, el archivo debe contener las columnas ($columnas).";
                            updateStatusProcess($id_process, $response["message"], "FAILED");
                        }
                    } else {
                        $response["result"] = "ERROR";
                        $response["code"] = 1004;
                        $response["message"] = "Error, la cantidad de filas del archivo excede el maximo de carga de ($max_row_file_upload) filas.";
                        updateStatusProcess($id_process, $response["message"], "FAILED");
                    }
                } else {
                    $response["result"] = "ERROR";
                    $response["code"] = 1003;
                    $response["message"] = "Error al mover el archivo.";
                    updateStatusProcess($id_process, $response["message"], "FAILED");
                }
            } else {
                $response["result"] = "ERROR";
                $response["code"] = 1002;
                $response["message"] = "El archivo debe ser TXT o CSV, favor de modificar e intentarnuevamente.";
                updateStatusProcess($id_process, $response["message"], "FAILED");
            }
        } else {
            $response["result"] = "ERROR";
            $response["code"] = 1001;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["code"] = 1001;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["code"] = 1000;
    $response["result"] = "ERROR";
    $response["message"] = "Error al generar los datos.";
}
//Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
header('Content-Type: application/json');
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
?>

