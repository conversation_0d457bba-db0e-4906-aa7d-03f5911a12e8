<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$idGroup = obtener_parameter('idGroup', 'GET');

//("DELETE FROM UserGroup WHERE idGroup = $idGroup AND company = $company") or die(mysql_error());
//("DELETE FROM provGroup WHERE id = $idGroup AND company = $company") or die(mysql_error());
try {
    $sql = "DELETE FROM UserGroup WHERE idGroup = :idGroup AND company = :company";
    $stmt = $dbh->prepare($sql);
    
    $stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmt->bindParam(':company', $company, PDO::PARAM_STR);
    
    $stmt->execute();
    
    
} catch (PDOException $e) {
    echo "Error en UserGroup: " . $e->getMessage();
}

try {
    $sql = "DELETE FROM provGroup WHERE id = :idGroup AND company = :company";
    $stmt = $dbh->prepare($sql);
    
    $stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmt->bindParam(':company', $company, PDO::PARAM_STR);
    
    $stmt->execute();
    
   
} catch (PDOException $e) {
    echo "Error en provGroup: " . $e->getMessage();
}

?>
<img src="images/icons/confirm.png" /> Eliminado

<?php
$dbh=null;
$action = "idGroup => '" . $idGroup . "'";
//log_tmc($company, "Aprovisionamiento", "Eliminar Grupo", $action, $login); 
?>
     

