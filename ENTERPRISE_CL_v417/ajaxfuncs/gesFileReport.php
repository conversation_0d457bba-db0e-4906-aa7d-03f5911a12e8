<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$fecha = return_date_now_format('Ymd');
$sendDateFrom = $_GET['sendDateFrom'];
$sendDateTo = $_GET['sendDateTo'];
$role = return_role($login);

if (!empty($sendDateFrom)) {
    $sql_date = " and process.timestamp between '$sendDateFrom 00:00:00' AND  '$sendDateTo 23:59:59' ";
}

if ($role == "admin") {
    $sql_login = "";
} else {
    $sql_login = "AND trafficMT.login = '$login'";
}


header('Pragma: public');
header('Content-Type: text/plain; name=reporte' . $fecha . '.csv');
header('Content-Transfer-Encoding: BASE64;');
header('Content-Disposition: attachment; filename="' . $fecha . '.csv"');

$sql = "SELECT process.target as filename, 
               count(*) as cnt, 
               process.timestamp as Fecha, 
               process.id, 
               trafficMT.login, 
               SUM(CASE trafficMT.status 
                       WHEN 'QUEUED' THEN 1 
                       WHEN 'PENDING' THEN 1 
                       WHEN 'TRANS' THEN 1 
                       WHEN 'SENDING' THEN 1 
                       ELSE 0 
                   END) as pending, 
               SUM(CASE trafficMT.status WHEN 'PUSHED' THEN 1 ELSE 0 END) as pushed, 
               SUM(CASE trafficMT.status WHEN 'CONFIRMED' THEN 1 ELSE 0 END) as confirmed, 
               SUM(CASE trafficMT.status 
                       WHEN 'FAILED' THEN 1 
                       WHEN 'EXPIRED' THEN 1 
                       ELSE 0 
                   END) as failed, 
               SUM(CASE trafficMT.status WHEN 'NOPUSHED' THEN 1 ELSE 0 END) as nopushed, 
               SUM(CASE trafficMT.status WHEN 'ANULADO' THEN 1 ELSE 0 END) as anulado, 
               SUM(CASE trafficMT.status WHEN 'NOPUSHED' THEN 1 ELSE 0 END) as nopushed 
        FROM trafficMT 
        INNER JOIN process ON trafficMT.input_process = process.id 
        WHERE trafficMT.input_mode = 'UPLOAD' ? ? 
        GROUP BY 1";

echo "Archivo;";
echo "Fecha;";
echo "Base Cargada;";
echo "Pendientes;";
echo "Despachados;";
echo "Confirmados;";
echo "Fallidos;";
echo "Rechazados;";
echo "Anulados;";
echo "Usuario;";
echo "\n";

$stmt = $dbh->query($sql);
$stmt->execute(array($sql_date,$sql_login));
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {

    echo "" . $row['filename'] . ";";
    echo "" . $row['Fecha'] . ";";
    echo "" . $row['cnt'] . ";";
    echo "" . $row['pending'] . ";";
    echo "" . $row['pushed'] . ";";
    echo "" . $row['confirmed'] . ";";
    echo "" . $row['failed'] . ";";
    echo "" . $row['nopushed'] . ";";
    echo "" . $row['anulado'] . ";";
    echo "" . $row['login'] . ";";
    echo "\n";
}

$dbh=null;
?>
