<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];

$login = $_SESSION["user"];

$name = $_GET['name'];

$action = $_GET['action'];

$query = "SELECT name FROM provGroup WHERE company = :company AND name = :name";
    $stmt = $dbh->prepare($query);
    $stmt->bindParam(':name', $phone, PDO::PARAM_STR);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->execute();
    $row = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>      
<?php if (!empty($row['name'])) { ?>

    <div class="alert_big"> El Grupo <b><?= $name ?></b> ya existe, favor probar nuevamenteasdas.</div>
    <br /><br />
    <a href="?seccion=provGroupAdd" class="styleButtonOne"><span class="buttom"> NUEVO GRUPO</span></a>

<?php
} else {

    $query = "INSERT INTO provGroup (company, name, created, login) VALUES (:company, :name, UTC_TIMESTAMP(), :login)";
    $stmt = $dbh->prepare($query);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->bindParam(':name', $name, PDO::PARAM_STR);
    $stmt->bindParam(':login', $login, PDO::PARAM_STR);
    $stmt->execute();

    //no se esta reemplazanod si da error or die(mysql_error()
    


    //$query = ("SELECT id FROM provGroup WHERE company = $company AND name = '$name'") or die(mysql_error());
    $query = "SELECT id FROM provGroup WHERE company = :company AND name = :name";
    $stmt = $dbh->prepare($query);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->bindParam(':name', $name, PDO::PARAM_STR);
    $stmt->execute();
    
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $idGroup = $row['id'];
    ?>
    <div class="alert_big"> <img src="images/icons/confirm.png"> El grupo <b><?= $name ?></b> ha sido creado exitosamente.</div>



    <?php if ($action == "Y") { ?>

        <br>
        <b><u>Seleccione Tipo de Abonado:</u></b>
        <br><br>
        <input type="radio" name="type" onclick="provGroupUser('new', '<?= $idGroup ?>');"> Abonado Nuevo  
        <input type="radio" name="type" onclick="provGroupUser('old', '<?= $idGroup ?>');"> Abonado Existente 

    <?php } else { ?>
        <br /><br />
        <a href="?seccion=provGroups" class="styleButtonOne"><span class="buttom">VOLVER</span></a>
    <?php }
}

$dbh=null;
?>