<?php
include ('../dbConn.php');
include ('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
if (isset($_POST['phone'])) {
	$phone = $_POST['phone'];
} else {
	$phone = '';
}
if (isset($_POST['msgtext'])) {
	$msgtext = $_POST['msgtext'];
} else {
	$msgtext = '';
}


$format = "d-m-Y H:i:s";
//$dif_hour = "-0 hour";
$carrier = return_carrier($phone);
$date_now = return_date_now();
$fecha = return_date_now_format($format);
//$fecha = "01-01-2017 23:59:59";
?>
<div class="alert_big">	
	<?php
	if (!empty($_COOKIE['cookie']) && !empty($_SESSION["id"])) {
			if (validate_format_datetime($fecha)) {
				if (validate_time_available_send_message($fecha)) {
					if (validacion_quota_sms()) {
						$content="";
						add_process($company, "WEB-IND", $content, $login);
						$process = return_process($company, "WEB-IND");
						// $query = " INSERT into trafficMT ("
						// 		. " company, recipientId, recipientDomain, status, login,receivedTime, dispatchTime,msgText, input_process"
						// 		. " ) values("
						// 		. " '$company', '$phone', '$carrier', 'QUEUED', '$login', UTC_TIMESTAMP(), UTC_TIMESTAMP(), '$msgtext', '$process')";
						// $errorSql = "Error al realizar el env&iacute;o (Codigo Error: " . mysql_errno() . " - " . mysql_error() . ") " . del_process($company, $process) . " ";
						// mysql_($query)or die($errorSql);
						try {
							$query = "INSERT INTO trafficMT (company, recipientId, recipientDomain, status, login, receivedTime, dispatchTime, msgText, input_process) VALUES (:company, :phone, :carrier, 'QUEUED', :login, UTC_TIMESTAMP(), UTC_TIMESTAMP(), :msgtext, :process)";
							$stmt = $dbh->prepare($query);
							
							$stmt->bindParam(':company', $company, PDO::PARAM_INT);
							$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
							$stmt->bindParam(':carrier', $carrier, PDO::PARAM_STR);
							$stmt->bindParam(':login', $login, PDO::PARAM_STR);
							$stmt->bindParam(':msgtext', $msgtext, PDO::PARAM_STR);
							$stmt->bindParam(':process', $process, PDO::PARAM_STR);
							
							$stmt->execute();
							
							echo "Inserción exitosa en trafficMT";
						} catch (PDOException $e) {
							echo "Error en la inserción en trafficMT: " . $e->getMessage();
							// Aquí puedes ejecutar del_process si es necesario
							del_process($company, $process);
						}
						

						?>
						<img src="images/icons/confirm.png"> Su <b>mensaje</b> ha sido enviado exitosamente.
						<?php
					} else {
						?>
						<img src="images/icons/error.png"/> 
						Se ha excedido la quota de mensajes disponibles, contacte al administrador.
						<?php
					}
				} else {
					?>
					<img src="images/icons/error.png"/>
					No es posible agregar un nuevo mensaje, ya que esta fuera de los rangos de disponibilidad del servicio.
				<?php
			}
		} else {
			?>
				<img src="images/icons/error.png"/> 
				No se pudo obtener de forma correcta la fecha de ejecuci&oacute;n.
				<?php
		}
		
} else {
	?>
		<img src="images/icons/alert.png"> Su sesi&oacute;n ha expirado. Vuelva a ingresar <a href="index.php">aqu&iacute;</a>
		<?php
	}
	$dbh=null;
//	$action = "phone => '" . $phone . "',  msgtext => '" . $msgtext . "'";
//    log_tmc($company, "Envio SMS", "Individual Inmediato", $action, $login);
	?>
</div>