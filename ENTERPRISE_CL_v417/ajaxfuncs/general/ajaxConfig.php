<?php
include('../../dbConn.php');
$response = array();

if (defined("MAX_STACK_MSG_UPLOAD")) {
	$response["maxStackMsgUpload"] = MAX_STACK_MSG_UPLOAD;
}
if (defined("MAX_LENGTH_TEXT_MSG")) {
	$response["maxLengthTextMsg"] = MAX_LENGTH_TEXT_MSG;
}
if (defined("MAX_ROW_MSG_UPLOAD")) {
	$response["maxRowMsgUpload"] = MAX_ROW_MSG_UPLOAD;
}
if (defined("SIZE_NUMBER_MOBILE")) {
	$response["sizeNumberMobile"] = SIZE_NUMBER_MOBILE;
}
if (defined("PLACEHOLDER_NUMBER_MOBILE")) {
	$response["placeholderNumberMobile"] = PLACEHOLDER_NUMBER_MOBILE;
}
if (defined("REX_TYPE_NUMBER_MOBILE")) {
	$response["rexTypeNumberMobile"] = REX_TYPE_NUMBER_MOBILE;
}
if (defined("MAX_SIZE_ERROR_FILE")) {
	$response["maxSizeErrorFile"] = MAX_SIZE_ERROR_FILE;
}
if (defined("URL_SERVICE_REPORT")) {
	$response["urlServiceReport"] = URL_SERVICE_REPORT;
}
if (defined("USER_SERVICE_REPORT")){
	$response["userServiceReport"] = USER_SERVICE_REPORT;
}
if (defined("PASS_SERVICE_REPORT")){
	$response["passServiceReport"] = PASS_SERVICE_REPORT;
}
if (defined("URL_SERVICE_ACCOUNT")) {
	$response["urlServiceAccount"] = URL_SERVICE_ACCOUNT;
}
if (defined("URL_SERVICE_GENERAL")) {
	$response["urlServiceGeneral"] = URL_SERVICE_GENERAL;
}
if (defined("VERSION")) {
	$response["version"] = VERSION;
}
if (defined("URL_SERVICE_BLACKLIST")) {
	$response["urlServiceBlacklist"] = URL_SERVICE_BLACKLIST;
}

echo json_encode($response, JSON_FORCE_OBJECT);
?>


