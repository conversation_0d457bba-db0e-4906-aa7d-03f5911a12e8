<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {

            $role = $_SESSION["profile"];
            $company = $_SESSION["id"];

            $sql_filtro = "";
            if (select_type_profile($role) != 1) {
                $sql_filtro .= " AND id = $company ";
            }

            $query = "";
            $query .= " SELECT \n";
            $query .= " id \n";
            $query .= " ,description \n";
            $query .= " FROM company \n";
            $query .= " WHERE 1 \n";
            $query .= " $sql_filtro \n";

            //Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
            header('Content-Type: application/json');

            $rawdata = array();
            $i = 0;
            
            $stmt = $dbh->prepare($query);
            $stmt->execute(array());
            $result = $stmt->fetchAll(PDO::FETCH_OBJ);
            foreach($result as $row){
                $fila = array();
                $fila["id"] = $row->id;
                $fila["nombre"] = $row->description;
                $rawdata[$i] = $fila;
                $i++;
            }
            $response["result"] = "OK";
            $response["data"] = $rawdata;
            $response["message"] = "Datos obtenidos";
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
?>

