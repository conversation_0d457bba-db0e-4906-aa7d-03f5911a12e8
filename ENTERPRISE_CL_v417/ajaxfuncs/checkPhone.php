<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');
$phone = obtener_parameter("phone", "GET");
$company = $_SESSION["id"];
//$carrier		= return_carrier($phone);

$query="SELECT phone FROM provUser WHERE phone = ? AND company = ?";

$stmt = $dbh->prepare($query);
    $stmt->execute(array($phone,$company));
    $result = $stmt->fetchAll(PDO::FETCH_OBJ);

if ($result!== false) {
    $row = $result->phone;
    if (empty($phone)) {
        echo "<img src='images/icons/alert.png'> Debe ingresar un M&oacute;vil";
    } else {
//			if($carrier == "UNKNOWN") {
//				echo "<img src='images/icons/confirm.png'> N&uacute;mero no v&aacute;lido";
//				echo "<input type='hidden' name='check_phone' id='check_phone' value='U'>";
//			}
//			else
        if (empty($row)) {
            echo "<img src='images/icons/confirm.png'> Disponible";
            echo "<input type='hidden' name='check_phone' id='check_phone' value='Y'>";
        } elseif (!empty($row)) {
            echo "<img src='images/icons/delete.png'> No Disponible";
            echo "<input type='hidden' name='check_phone' id='check_phone' value='N'>";
        }
    }
}

$dbh=null;
?>
