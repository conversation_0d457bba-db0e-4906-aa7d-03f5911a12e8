<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$fecha = return_date_now_format('Ymd');

$sendDateFrom = obtener_parameter("sendDateFrom", "GET");
$sendDateTo = obtener_parameter("sendDateTo", "GET");
$phone = obtener_parameter("phone", "GET");
$carrier = obtener_parameter("op", "GET");
$status = obtener_parameter("status", "GET");
$input_mode = obtener_parameter("mode", "GET");
$type_input = obtener_parameter("type_input", "GET");
$cc = obtener_parameter("cc", "GET");
$selcompany = obtener_parameter("company", "GET");
$listuser = obtener_parameter("listuser", "GET");

$role = return_role($login);

$sql_filtro = "";
if (!empty($sendDateFrom)) {
    $format_date = "Y-m-d H:i:s";
    $sendDateFromGmt = return_date_after_format_gmt("$sendDateFrom 00:00:00", $format_date);
    $sendDateToGmt = return_date_after_format_gmt("$sendDateTo 23:59:59", $format_date);
    $sql_date = " AND receivedTime between '$sendDateFromGmt' AND  '$sendDateToGmt' ";
    $sql_filtro .= $sql_date;
}

if (!empty($carrier)) {
    $sql_carrier = " AND recipientDomain LIKE '%$carrier%' ";
    $sql_filtro = $sql_carrier;
}

if (!empty($input_mode)) {
    $sql_input_mode = " AND input_mode = '$input_mode' ";
    $sql_filtro = $sql_date;
}

if (!empty($type_input)) {
    if ($type_input == "XMLRPC") {
        $sql_type_input = " AND input_mode = '$type_input' ";
    } else {
        $sql_type_input = " AND  input_mode IN ('WEB-IND', 'WEB-GRP', 'UPLOAD') ";
    }
    $sql_filtro = $sql_input_mode;
}

if (!empty($phone)) {
    $sql_phone = " AND recipientId = '$phone' ";
    $sql_filtro = $sql_phone;
}

if (!empty($status)) {
    if ($status == "QUEUED") {
        $sql_status = " AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED') ";
    } else {
        $sql_status = " AND status = '$status' ";
    }
    $sql_filtro = $sql_status;
}

if (!empty($listuser)) {
    if (strcmp($listuser, "todos") != 0) {
        $arrUsers = explode("-", $listuser);
        $textUsers = "";
        for ($i = 0; $i < count($arrUsers); $i++) {
            if (empty($textUsers)) {
                $textUsers .= "'$arrUsers[$i]'";
            } else {
                $textUsers .= ", '$arrUsers[$i]'";
            }
        }
        $sql_user = "  AND t.login in ($textUsers) ";
        $sql_filtro .= $sql_user;
    }
}

if (!empty($selcompany)) {
    $sql_filtro .= " AND a.company = $selcompany ";
}

if (select_type_profile($role) != 1) {
    if (select_type_profile($role) != 2) {
        $sql_login = " AND t.login = '$login' ";
        $sql_filtro .= $sql_login;
    }
    $sql_filtro .= " AND a.company = $company ";
}

header('Pragma: public');
header('Content-Type: text/plain; name=reporte-consolidado-' . $fecha . '.csv');
header('Content-Transfer-Encoding: BASE64;');
header('Content-Disposition: attachment; filename="reporte-consolidado-' . $fecha . '.csv"');

$interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

$query = " SELECT "
        . " t.recipientId "
        . " , t.company "
        . " , t.recipientDomain "
        . " , t.status "
        . " , date_sub( t.receivedTime, interval $interval minute) AS receivedTime"
        . " , date_sub( t.dispatchTime, interval $interval minute) AS dispatchTime"
        . " , date_sub( t.deliveryTime, interval $interval minute) AS deliveryTime"
        . " , t.input_mode "
        . " , t.login "
        . " , t.msgText "
        . " , t.errText "
        . " , c.nom_corto as company "
        . " FROM trafficMT t "
        . " INNER JOIN account a ON t.login = a.login "
        . " INNER JOIN company c on a.company = c.id "
        . " WHERE 1 "
        . " ? ";

        $stmt = $dbh->query($sql);
        $stmt->execute(array($sql_filtro));

echo "Movil;";
echo "Operador;";
echo "Estado;";
echo "Fecha de Carga;";
echo "Fecha de Envio;";
echo "Fecha de Despacho;";
echo "Tipo de Envío;";
echo "Tipo de Ingreso;";
echo "Usuario;";
echo "Company;";
echo "Mensaje;";
echo "\n";

while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    if ($row['status'] == "NOPUSHED" || $row['status'] == "FAILED" || $row['status'] == "REJECTED" || $row['status'] == "ANULADO") {
        $cause = "[Causa: " . $row['errText'] . "]";
    } else {
        $cause = "";
    }

    echo $row['recipientId'] . ";";
    echo return_carrier_name($row['recipientDomain']) . ";";
    echo return_status_name($row['status']) . ";";
    echo $row['receivedTime'] . ";";
    echo return_dispatchtime($row['dispatchTime'], $row['receivedTime']) . ";";
    echo $row['deliveryTime'] . ";";
    echo return_type_send_name_new($row['receivedTime'], $row['dispatchTime']) . ";";
    echo return_input_mode_name($row['input_mode']) . ";";
    echo $row['login'] . ";";
    echo $row['company'] . ";";
    echo $row['msgText'] . $cause . ";";
    echo "\n";
}
$dbh=null;
?>
