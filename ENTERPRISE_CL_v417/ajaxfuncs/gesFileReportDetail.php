<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$fecha = return_date_now_format('Ymd');
$id_process = obtener_parameter("id_process", "GET");
$phone = obtener_parameter("phone", "GET");
$carrier = obtener_parameter("op", "GET");
$status = obtener_parameter("status", "GET");
$mode = obtener_parameter("mode", "GET");
$user = obtener_parameter("user", "GET");
$role = return_role($login);

$sql_filtro = "";
if (!empty($sendDateFrom)) {
    $sql_date = " AND t.timestamp >= '$sendDateFrom 00:00:00' AND timestamp <= '$sendDateTo 23:59:59' ";
    $sql_filtro = $sql_date;
}

if (!empty($carrier)) {
    $sql_carrier = " AND t.carrier LIKE '%$carrier%' ";
    $sql_filtro = $sql_carrier;
}

if (!empty($input_mode)) {
    $sql_input_mode = " AND t.input_mode = '$input_mode' ";
    $sql_filtro = $sql_input_mode;
}

if (!empty($phone)) {
    $sql_phone = " AND t.phone = '$phone' ";
    $sql_filtro = $sql_phone;
}

if (!empty($cc)) {
    $sql_cc = " AND t.cc = '$cc' ";
    $sql_filtro = $sql_cc;
}

if (!empty($row_id)) {
    $sql_row_id = " AND t.row_id = '$row_id' ";
    $sql_filtro = $sql_row_id;
}

if (!empty($codigo)) {
    $sql_codigo = " AND t.codigo = '$codigo' ";
    $sql_filtro = $sql_codigo;
}

if (!empty($status)) {
    if (strcmp($status, "QUEUED") == 0) {
        $sql_status = " AND t.status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED') ";
    } else {
        $sql_status = " AND t.status = '$status' ";
    }
    $sql_filtro = $sql_status;
}

if (select_type_profile($role) != 1) {
    if (select_type_profile($role) != 2) {
        $sql_login = " AND t.login = '$login' ";
        $sql_filtro .= $sql_login;
    }
    $sql_filtro .= " AND a.company = $company ";
}

header('Pragma: public');
header('Content-Type: text/plain; name=reporte-por-detalle-' . $fecha . '.csv');
header('Content-Transfer-Encoding: BASE64;');
header('Content-Disposition: attachment; filename="reporte-por-detalle-' . $fecha . '.csv"');

$query = " SELECT * "
        . " FROM trafficMT t "
        . " inner join account a on t.login = a.login "
        . " WHERE input_process = ? "
        . " ?";

    $stmt = $dbh->prepare($query);
    $stmt->execute(array($id_process,$sql_filtro));

echo "Móvil;";
echo "Id;";
echo "Operador;";
echo "Estado;";
echo "Fecha de Envio;";
echo "Fecha de Despacho;";
echo "Fecha de Creacion;";
echo "Tipo de Envío;";
echo "Ingreso;";
echo "Usuario;";
echo "Mensaje;";
echo "\n";

while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $dateSend = return_dispatchtime($row['dispatchTime'], $row['timestamp']);
    echo $row['phone'] . ";";
    echo $row['id'] . ";";
    echo $carrier = return_carrier_name($row['carrier']) . ";";
    echo $status = return_status_name($row['status']) . ";";
    echo $dateSend = return_date_send($dateSend) . ";";
    echo return_date_send($row['deliverytime']) . ";";
    echo return_date_send($row['timestamp']) . ";";
    echo $type_send = return_type_send_name_new($row['receivedTime'], $row['dispatchTime']) . ";";
    echo $input_mode = return_input_mode_name($row['input_mode']) . ";";
    echo $row['login'] . ";";
    echo $msgtext = preg_replace("[\n|\r|\n\r]", ' ', $row['msgtext']) . ";";
    echo "\n";
}

$dbh=null;
?>
