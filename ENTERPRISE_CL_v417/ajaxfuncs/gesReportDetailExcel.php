<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$fecha = return_date_now_format('Ymd');

$date = obtener_parameter("date", "GET");
$phone = obtener_parameter("phone", "GET");
$carrier = obtener_parameter("op", "GET");
$status = obtener_parameter("status", "GET");
$mode = obtener_parameter("mode", "GET");
$user = obtener_parameter("user", "GET");
$listuser = obtener_parameter("listuser", "GET");
$company = obtener_parameter("company", "GET");

$role = return_role($login);

$sql_filtro = "";
if (!empty($date)) {
    $format_date = "Y-m-d H:i:s";
    $sendDateFromGmt = return_date_after_format_gmt("$date 00:00:00", $format_date);
    $sendDateToGmt = return_date_after_format_gmt("$date 23:59:59", $format_date);
    $sql_date = " AND receivedTime between '$sendDateFromGmt' AND  '$sendDateToGmt' ";
    $sql_filtro .= $sql_date;
}

if (!empty($carrier)) {
    $sql_carrier = " AND t.recipientDomain LIKE '%$carrier%' ";
    $sql_filtro .= $sql_carrier;
}

if (!empty($mode)) {
    $sql_input_mode = " AND t.input_mode = '$mode' ";
    $sql_filtro .= $sql_input_mode;
}

if (!empty($type_input)) {
    if (strcmp($type_input, "XMLRPC") == 0) {
        $sql_type_input = " AND t.input_mode = '$type_input' ";
    } else {
        $sql_type_input = " AND  t.input_mode IN ('WEB-IND', 'WEB-GRP', 'UPLOAD') ";
    }
    $sql_filtro .= $sql_type_input;
}

if (!empty($phone)) {
    $sql_phone = " AND t.recipientId = '$phone' ";
    $sql_filtro .= $sql_phone;
}

if (!empty($status)) {
    if (strcmp($status, "QUEUED") == 0) {
        $sql_status = " AND t.status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED') ";
    } else {
        $sql_status = " AND t.status = '$status' ";
    }
    $sql_filtro .= $sql_status;
}

if (!empty($company)) {
    $sql_filtro .= " AND a.company = $company ";
}

if (!empty($listuser)) {
    if (strcmp($listuser, "todos") != 0) {
        $arrUsers = explode("-", $listuser);
        $textUsers = "";
        for ($i = 0; $i < count($arrUsers); $i++) {
            if (empty($textUsers)) {
                $textUsers .= "'$arrUsers[$i]'";
            } else {
                $textUsers .= ", '$arrUsers[$i]'";
            }
        }
        $sql_user = "  AND t.login in ($textUsers) ";
        $sql_filtro .= $sql_user;
    }
}

if (select_type_profile($role) != 1) {
    if (select_type_profile($role) != 2) {
        $sql_login = " AND t.login = '$login' ";
        $sql_filtro .= $sql_login;
    }
    $sql_filtro .= " AND a.company = $company ";
}

header('Pragma: public');
header('Content-Type: text/plain; name=reporte-por-detalle-' . $fecha . '.csv');
header('Content-Transfer-Encoding: BASE64;');
header('Content-Disposition: attachment; filename="reporte-por-detalle-' . $fecha . '.csv"');

$interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

$query = " SELECT "
        . " t.recipientId "
        . " , t.company "
        . " , t.recipientDomain "
        . " , t.status "
        . " , date_sub( t.receivedTime, interval $interval minute) AS receivedTime"
        . " , date_sub( t.dispatchTime, interval $interval minute) AS dispatchTime"
        . " , date_sub( t.deliveryTime, interval $interval minute) AS deliveryTime"
        . " , t.input_mode "
        . " , t.login "
        . " , t.msgText "
        . " , t.errText "
        . " , c.nom_corto as company "
        . " FROM trafficMT t "
        . " INNER JOIN account a on t.login = a.login "
        . " INNER JOIN company c on a.company = c.id "
        . " WHERE 1 "
        . "? ";
        $stmt = $dbh->query($sql);
        $stmt->execute(array($sql_filtro));

echo "Celular;";
echo "Operador;";
echo "Estado;";
echo "Fecha de Carga;";
echo "Fecha de Envio;";
echo "Fecha de Despacho;";
echo "Tipo de Envío;";
echo "Tipo de Ingreso;";
echo "Usuario;";
echo "Company;";
echo "Mensaje;";
echo "\n";

while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    if ($row['status'] == "NOPUSHED" || $row['status'] == "FAILED" || $row['status'] == "REJECTED" || $row['status'] == "ANULADO") {
        $cause = "[Causa: " . $row['errText'] . "]";
    } else {
        $cause = "";
    }

    echo $row['recipientId'] . ";";
    echo $carrier = return_carrier_name($row['recipientDomain']) . ";";
    echo $status = return_status_name($row['status']) . ";";
    echo $row['receivedTime'] . ";";
    echo return_dispatchtime($row['dispatchTime'], $row['receivedTime']) . ";";
    echo $row['deliveryTime'] . ";";
    echo $type_send = return_type_send_name_new($row['receivedTime'], $row['dispatchTime']) . ";";
    echo $input_mode = return_input_mode_name($row['input_mode']) . ";";
    echo $row['login'] . ";";
    echo $row['company'] . ";";
    echo $row['msgText'] . $cause . ";";
    echo "\n";
}
$dbh=null;
?>
