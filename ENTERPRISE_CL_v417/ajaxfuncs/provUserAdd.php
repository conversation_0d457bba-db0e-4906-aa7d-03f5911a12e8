<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php'); 
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

	$company = $_SESSION["id"]; 
	$login = $_SESSION["user"];
	$groups='';
	$phone = obtener_parameter('phone', 'GET');
	$firstName = obtener_parameter('firstName', 'GET');
	$lastName = obtener_parameter('lastName', 'GET');
	$action = obtener_parameter('action', 'GET');
	
	$carrier = return_carrier($phone);
	
	//("INSERT into provUser (company, phone, carrier, name1, name2, created, login) values ('$company', '$phone', '$carrier', '$firstName', '$lastName', UTC_TIMESTAMP(), '$login')"); 
	try {
		$sql = "INSERT INTO provUser (company, phone, carrier, name1, name2, created, login) VALUES (:company, :phone, :carrier, :firstName, :lastName, UTC_TIMESTAMP(), :login)";
		$stmt = $dbh->prepare($sql);
		
		$stmt->bindParam(':company', $company, PDO::PARAM_INT);
		$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
		$stmt->bindParam(':carrier', $carrier, PDO::PARAM_STR);
		$stmt->bindParam(':firstName', $firstName, PDO::PARAM_STR);
		$stmt->bindParam(':lastName', $lastName, PDO::PARAM_STR);
		$stmt->bindParam(':login', $login, PDO::PARAM_STR);
		
		$stmt->execute();
		
		
	} catch (PDOException $e) {
		
		error_log("Error en la inserción en provUser: " . $e->getMessage());
	}
	?>

	<div class="alert_big"><img src="images/icons/confirm.png" /> El Abonado ha sido creado exitosamente.</div>

	<?php
	if($action == "Y") { 
	?>
     <br />
     <b><u>Asociar Abonados:</u></b>
	<br />
	<?php
	//$sql = ("SELECT * FROM provGroup WHERE company = $company");

	$sql = "SELECT * FROM provGroup WHERE company = :company";
    $stmt = $dbh->prepare($sql);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->execute();
	?>
            <br />
			<table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
			<tr>
                    <th></th>
                    <th>ASOCIAR</th>
                    <th>ACCI&Oacute;N</th>
			</tr>
			<?php 
			$i = 0;
			while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) { 
			$res = $i%2;
			if($res == 0) { $class= ""; } else { $class= "table-list-tr"; } 
						
			?>
			<tr class="<?=$class?>">
				<td id="users">
					<input type="checkbox" name="groups" onClick="provUserAsoc(this.checked, '<?=$phone;?>', '<?=$row['id']?>');">
				</td>
                <td><?=$row['name'] ?></td>
				<?php 
				$id= $row['id'];
					if(empty($id)){
					$id=0;
				}
				?>
                <td>
					<div style="width:20px;" id="idgroup<?=$id?>"></div>
				</td>
			</tr>
			<?php $i++;  
			}
			?>
			</table>
            <br />
			<br />
            <span onclick="javascript:document.location.href='mcs.php?seccion=provUsers'" class="buttom">Finalizar</span>
      <?php } 

	  $dbh=null;
//	  $action = "phone => '".$phone."',  name => '".$firstName." ".$lastName."', groups => '".$groups."'"; 
	  //log_tmc($company, "Aprovisionamiento", "Crear Abonado", $action, $login); 
	  ?>


     

