<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$filename = obtener_parameter("filetxt", "POST");
$id_process = obtener_parameter("idfile", "POST");
$date_received = obtener_parameter("datelocal", "POST");

?>
<div class="alert_big">
<?php
if (!empty($_COOKIE['cookie']) && !empty($company)) {
	if (validacion_quota_sms()) {
		$format = "Y-m-d H:i:s";
		$date_received = return_date_after_format_gmt($date_received,$format);
		$id = insertarMtDesdeTablaTemporal($id_process ,$date_received);
		if($id > 0){
			$status = "PROCESSED";
			updateProcessedFile($id_process, $status);
			$result = updateDetailProcessToProcessed($id_process, $status);
			?>
			<img src="images/icons/confirm.png">
			El archivo <b><?= $filename ?></b> ha sido cargado exitosamente para su posterior envio.
			<?php
		}else{
			?>
			<img src="images/icons/error.png"/> Error al ingresar los SMS.
			<?php
		}
	}else{
		?>
		<img src="images/icons/error.png"/> Se ha excedido la quota de mensajes disponibles, contacte al administrador.
		<?php
	}
}else{
	?>
	<img src="images/icons/alert.png"> Su sesi&oacute;n ha expirado. Vuelva a ingresar <a href="index.php">aqu&iacute;</a>
	<?php
}
//$action = $company . " ==> " . utf8_decode($filename);
?>
</div>