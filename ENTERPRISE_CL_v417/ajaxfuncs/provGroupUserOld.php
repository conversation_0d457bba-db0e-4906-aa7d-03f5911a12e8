<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];

$number_phone = obtener_parameter('phone', 'GET');
$nombre = obtener_parameter('nombre', 'GET');
$idGroup = obtener_parameter('idGroup', 'GET');
$date = obtener_parameter('sendDateFrom', 'GET');
$p = obtener_parameter('p', 'GET');

//	if(strlen($phone) == 8)
//	{
//		$phone=$phone;
//	}
//	if(strlen($phone) == 9)
//	{
//	
//		$phone=substr($phone,1,8);
//	}
//	if(strlen($phone) == 11)
//	{
//		$phone=substr($phone,3,8);
//	}
$sql_filtro = '';
if (!empty($number_phone)) {
    $sql_phone = "AND phone LIKE '%$number_phone%'";
    $sql_filtro .= $sql_phone;
}

if (!empty($nombre)) {
    $sql_name = "AND (name1 LIKE '%$nombre%' OR name2 LIKE '%$nombre%')";
    $sql_filtro .= $sql_name;
}

if (isset($p)) {
    if ($p == 0) {
        $page = 1;
    } else {
        $page = $p;
    }
} else {
    $page = 1;
}

$rows_for_page = 30;

$rows_from = (($page * $rows_for_page) - $rows_for_page);

$url = $idGroup;

// $sql1 = "SELECT group_concat(phone separator ',') as ids FROM UserGroup WHERE idGroup = $idGroup";
// $row1 = mysql_fetch_object(mysql_($sql1));
// $arrayGrp = split(',', $row1->ids);
// $query = "SELECT * FROM provUser WHERE 1 $sql_filtro  LIMIT $rows_from, $rows_for_page";
// $sql2 = mysql_($query);

// $total_rows = mysql_num_rows(mysql_("SELECT * FROM provUser WHERE 1 $sql_filtro"));

// $total_pages = ceil($total_rows / $rows_for_page);

try {
    $sql1 = "SELECT group_concat(phone separator ',') as ids FROM UserGroup WHERE idGroup = :idGroup";
    $stmt1 = $dbh->prepare($sql1);
    $stmt1->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    $stmt1->execute();
    
    $row1 = $stmt1->fetch(PDO::FETCH_OBJ);
    $arrayGrp = explode(',', $row1->ids);
} catch (PDOException $e) {
    echo "Error al obtener la lista de teléfonos: " . $e->getMessage();
}

try {
    $query = "SELECT * FROM provUser WHERE 1 $sql_filtro LIMIT :rows_from, :rows_for_page";
    $stmt2 = $dbh->prepare($query);
    $stmt2->bindParam(':rows_from', $rows_from, PDO::PARAM_INT);
    $stmt2->bindParam(':rows_for_page', $rows_for_page, PDO::PARAM_INT);
    $stmt2->execute();
    
    // Puedes usar $stmt2 para iterar sobre los resultados
} catch (PDOException $e) {
    echo "Error al ejecutar la consulta en provUser: " . $e->getMessage();
}
try {
    $total_rows_query = "SELECT count(*) as total_rows FROM provUser WHERE 1 $sql_filtro";
    $stmt3 = $dbh->prepare($total_rows_query);
    $stmt3->execute();
    
    $total_rows_result = $stmt3->fetch(PDO::FETCH_OBJ);
    $total_rows = $total_rows_result->total_rows;
    
    $total_pages = ceil($total_rows / $rows_for_page);
} catch (PDOException $e) {
    echo "Error al obtener el total de filas en provUser: " . $e->getMessage();
}

?>


<?php
if ($stmt2->rowCount() > 0) {
    ?>
    <br />
    <?php pag_pages_ajax($page, $total_pages, $url, "provGroupUserOld"); ?>
    <br />
    <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
        <tr>
            <th>ASOCIAR</th>
            <th>M&Oacute;VIL</th>
            <th>OPERADOR</th>
            <th>NOMBRE COMPLETO</th>
            <th>ACCI&Oacute;N</th>
        </tr>
        <?php
        $i = 0;
        $checked = "";
        while ($row = $stmt2->fetch(PDO::FETCH_ASSOC)) {
            $res = $i % 2;
            if ($res == 0) {
                $class = "";
            } else {
                $class = "table-list-tr";
            }
            if (in_array($row['phone'], $arrayGrp)) {
                $checked = "checked";
            } else {
                $checked = "";
            }
            ?>
            <tr class="<?= $class ?>">
                <td class="baseTd" id="users">
                    <input type="checkbox" name="groups" onClick="provGroupAsoc(this.checked, '<?= $row['phone'] ?>', '<?= $idGroup ?>');" <?= $checked ?> >
                </td>
                <td><?= $row['phone'] ?></td>
                <td><?php $carrier = return_carrier($row['phone']); ?> <?= $carrier = return_carrier_name($carrier); ?></td>
                <td><?= ucwords(strtolower($row['name1'])) ?> <?= ucwords(strtolower($row['name2'])) ?></td>
                <td><div style="width:20px;" id="idphone<?= $row['phone']; ?>"></div></td>
            </tr>
            <?php $i++;
        }
        ?>
    </table>
    <br /><br />
    <a href="?seccion=provGroupAdd" class="styleButtonOne">
        <span class="buttom">CREAR NUEVO GRUPO</span>
    </a>
    <a href="?seccion=provGroups" class="styleButtonOne">
        <span class="buttom">VOLVER</span>
    </a>
<?php } else { ?>
    <div class="alert_small">
        <img src="images/icons/error.png"> No se encontraron coincidencias.
    </div>
<?php }
$dbh=null;
 ?>