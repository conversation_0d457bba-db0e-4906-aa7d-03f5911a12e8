<?php
	
include('../dbConn.php');
include('../includes/php/general_funcs.php'); 
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php'); 
	
	$company  = $_SESSION["id"]; 
	
	if($_POST['action'] == "Y") { 
		
          
		//$sql 	= ("SELECT * FROM provGroup WHERE company = $company");
          $query = "SELECT * FROM provGroup WHERE company = :company";
          $stmt = $dbh->prepare($query);
          $stmt->bindParam(':company', $company, PDO::PARAM_INT);
          $stmt->execute();

		if($stmt->rowCount() > 0) { ?>
		
		<div id="provUserGroupId">
          
          <input type="hidden" name="groupEmpty" id="groupEmpty" value="N">
          <br />
          <table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
               <tr>
                    <th>Asociar</th>
                    <th>Grupo</th>
               </tr>
               <?php $i = 0;
                    while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $res = $i%2;
                    if($res == 0) { $class= ""; } else { $class= "table-list-tr"; } ?>
               <tr class="<?=$class?>">
                    <td><input type="checkbox" name="groups[]" value="<?=$row['id'].","?>"></td>
                    <td><?=$row['name'];?></td>
               </tr>
               <?php $i++; } ?>
               </table>
         
          <?php } else { ?>
          
          	<input type="hidden" name="groupEmpty" id="groupEmpty" value="Y">
          
          <div class="alert_small">
			  <img src="images/icons/error.png"> No existen Grupos creados.
			  <br />
			  <br />
			  Para crear un nuevo grupo ingrese <a href="">aqu&iacute;</a>.</div>
          
          <?php } ?>
	
     <?php } else { ?>
     
     	   
     
     <?php } 
     $dbh=null?>
     
	</div>



	
