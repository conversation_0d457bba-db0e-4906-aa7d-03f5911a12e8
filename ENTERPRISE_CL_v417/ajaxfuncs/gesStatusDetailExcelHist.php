<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$fecha				= return_date_now_format('Ymd');
$company			= $_SESSION["id"];
$login					= $_SESSION["user"];
$date					= $_GET['date'];
$carrier				= $_GET['carrier'];
$mode				= $_GET['mode'];
$role					= return_role($login);

if(!empty($sendDateFrom))
{
	$sql_date = "AND timestamp >= '$sendDateFrom 00:00:00' AND timestamp <= '$sendDateTo 23:59:59'";
}

if(!empty($carrier))
{
	$sql_carrier = "AND carrier LIKE '%$carrier%'";
}

if(!empty($input_mode))
{
	$sql_input_mode = "AND input_mode = '$input_mode'";
}

if(!empty($phone))
{
	$sql_phone = "AND phone = '$phone'";
}


if(!empty($status))
{
	if($status == "QUEUED")
	{
		$sql_status = "AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED')";
	} else {
		$sql_status = "AND status = '$status'";
	}
}

if($role == "admin")
{
	$sql_login = ""; 
} else {
	$sql_login = "AND login = '$login'";
}

if(!empty($user))
{
	$sql_user = "AND login = '$user'";
}

header( 'Pragma: public' );
header( 'Content-Type: text/plain; name=reporte-consolidado-'.$fecha.'.csv' );
header( 'Content-Transfer-Encoding: BASE64;' );
header( 'Content-Disposition: attachment; filename="reporte-consolidado-'.$fecha.'.csv"');

$query = "SELECT * FROM provUser";
$stmt = $dbh->query($sql);
$stmt->execute();

echo "Celular;";
echo "nombre1;";
echo "nombre2;";
echo "\n";

while($row = $stmt->fetch(PDO::FETCH_ASSOC)){
	$dateSend = return_dispatchtime($row['dispatchTime'], $row['timestamp']);
	echo $row['phone'].";";
	echo $row['name1'].";";
	echo $row['name2'].";";
	echo "\n";
}
$dbh=null;

?>
