<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $login = $_SESSION["user"];
            $company = $_SESSION["id"];
            $profile = $_SESSION["profile"];
            $offset = $_SESSION["OFFSET_COMPANY"];

            $selTelefono = obtener_parameter("number", "POST");
            $selIdCompany = obtener_parameter("idcompany", "POST");
            $selMotivo = obtener_parameter("motivo", "POST");

            if (select_type_profile($profile) != 1) {
                $selIdCompany = $company;
            }

            $query = "";
            $query .= " SELECT ";
            $query .= " COUNT(*) AS CANT ";
            $query .= " FROM ";
            $query .= " blacklist b ";
            $query .= " WHERE 1 ";
            $query .= " AND b.number_blacklist = ? ";
            $query .= " AND b.id_company = ? ";

            $stmt = $dbh->prepare($query);
            $stmt->execute(array($selTelefono,$selIdCompany));
            $result = $stmt->fetch(PDO::FETCH_OBJ);
           
            $cant = $result->CANT;
            if ($cant == 0) {
                //seguimos
                $query = "";
                $query .= " INSERT INTO blacklist ";
                $query .= " ( ";
                $query .= " id_account ";
                $query .= " , id_company ";
                $query .= " , number_blacklist ";
                $query .= " , detail_blacklist ";
                $query .= " , create_date ";
                $query .= " ) VALUES ( ";
                $query .= " (select id from account where login=?) ";
                $query .= " , ? ";
                $query .= " , ''  ";
                $query .= " , '? - (?)' ";
                $query .= " , UTC_TIMESTAMP() ";
                $query .= " ) ";

                $stmt = $dbh->prepare($query);
                $stmt->execute(array($login,$selIdCompany,$selTelefono,$selMotivo,$login));
                               
                $id = $dbh->lastInsertId();

                if ($id > 0) {
                    $response["result"] = "OK";
                    $response["data"] = $id;
                    $response["message"] = "Datos insertados";
                } else {
                    $response["result"] = "ERROR";
                    $response["data"] = null;
                    $response["message"] = "Error al insertar los datos.";
                }
            } else {
                $response["result"] = "ERROR";
                $response["data"] = null;
                $response["message"] = "Error el numero ya se encuentra registrado en el blacklist.";
                //usuario existe
            }
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
header('Content-Type: application/json');
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
?>
