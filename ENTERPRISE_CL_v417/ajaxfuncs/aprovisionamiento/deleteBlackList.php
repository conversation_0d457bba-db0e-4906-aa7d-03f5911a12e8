<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $login = $_SESSION["user"];

            $id = obtener_parameter("id", "GET");

            $query = "";
            $query .= " SELECT COUNT(*) CANT ";
            $query .= " FROM blacklist ";
            $query .= " WHERE id_blacklist = $id ";
//si falla o no da resultado,es por la validacion
            $stmt = $dbh->prepare($query);
            $stmt->execute(array($id));
            $result = $stmt->fetch(PDO::FETCH_OBJ);

            if ($result!== false) {                
                $cant = $result->CANT;

                if (!empty($cant)) {
                    /*
                      se agrega update para que el trigger agrege esos datos a la tabla de historico
                     */
                    $query = "";
                    $query .= " UPDATE blacklist ";
                    $query .= " SET id_account = (select id from account where login=?), ";
                    $query .= " detail_blacklist = 'SE ELIMINA REGISTRO - (?)' ";
                    $query .= " WHERE id_blacklist = ? ";

                    $stmt = $dbh->prepare($query);
                    $stmt->execute(array($login,$login,$id));
                   

                    $query = "";
                    $query .= " DELETE ";
                    $query .= " FROM blacklist ";
                    $query .= " WHERE id_blacklist = ? ";

                    $stmt = $dbh->prepare($query);
                    $stmt->execute(array($id));
                   
                    

                    $response["result"] = "OK";
                    $response["data"] = 1;
                    $response["message"] = "Se ha eliminado el registro del blacklist.";
                } else {
                    $response["result"] = "ERROR";
                    $response["message"] = "Registro no encontrado para eliminación.";
                }
            } else {
                $response["result"] = "ERROR";
                $response["message"] = "Registro no encontrado para eliminación.";
            }
        } else {
            $response["result"] = "ERROR";
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);




