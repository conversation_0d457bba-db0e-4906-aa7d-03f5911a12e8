<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $login = $_SESSION["user"];
            $company = $_SESSION["id"];
            $profile = $_SESSION["profile"];
            $offset = $_SESSION["OFFSET_COMPANY"];
            $interval = convertirOffset($_SESSION["OFFSET_COMPANY"], -1);

            $selcompany = obtener_parameter("company", "GET");
            $filtertext = obtener_parameter("txtfilter", "GET");
            $page = obtener_parameter("page", "GET");
            $rows = obtener_parameter("rows", "GET");
            $cant_rows_page = 10;
            if ($rows == null) {
                $cant_rows_page = 10;
            } else {
                $cant_rows_page = $rows;
            }

            $limit = 0;
            $sql_filtro = "";
            if (!empty($page)) {
                $limit = $page;
                if ($limit > 0) {
                    $limit -= 1;
                }
            } else {
                $limit = 0;
            }

            if (select_type_profile($profile) != 1) {
                if (select_type_profile($profile) != 2) {
                    $sql_filtro .= " AND a.login = '$login' ";
                }
                $sql_filtro .= " AND COALESCE(c.id, 0) = $company ";
            }

            if (!empty($selcompany)) {
                $sql_filtro .= " AND COALESCE(c.id, 0) = $selcompany ";
            }

            if (!empty($filtertext)) {
                $sql_filtro .= "AND ( ";
                $sql_filtro .= "	b.number_blacklist LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR a.login LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR COALESCE(b.detail_blacklist,'') LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR c.nom_corto LIKE '%$filtertext%' ";
                $sql_filtro .= " ) ";
            }

            $query = "";

            $query .= " SELECT ";
            $query .= " b.id_blacklist ";
            $query .= " , b.number_blacklist ";
            $query .= " , b.detail_blacklist ";
            $query .= " , date_sub( b.create_date, interval $interval minute) as create_date ";
            $query .= " , a.login ";
            $query .= " , c.nom_corto ";
            $query .= " FROM blacklist b ";
            $query .= " INNER JOIN account a ON a.id = b.id_account ";
            $query .= " INNER JOIN company c ON c.id = b.id_company ";
            $query .= " WHERE 1 ";
            $query .= " $sql_filtro ORDER by a.id DESC ";

            //Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
            header('Content-Type: application/json');

            $rawdata = array();
            $i = 0;

            $offset_rows = $limit * $cant_rows_page;
            $cant_rows = get_count_row_query($query);
            
            $textsql = "? LIMIT ? OFFSET ?";
            $stmt = $dbh->prepare($textsql);
            $stmt->execute(array($query,$cant_rows_page,$offset_rows));
            $result = $stmt->fetchAll(PDO::FETCH_OBJ);
            if ($result!== false) {
                foreach($result as $row){
                    $fila = array();
                    $fila["id"] = $row->id_blacklist;
                    $fila["number"] = $row->number_blacklist;
                    $fila["detail"] = $row->detail_blacklist;
                    $fila["date"] = $row->create_date;
                    $fila["user"] = $row->login;
                    $fila["company"] = $row->nom_corto;
                    $rawdata[$i] = $fila;
                    $i++;
                }
                $response["result"] = "OK";
                $response["data"] = $rawdata;
                $response["totalRows"] = $cant_rows;
                $response["message"] = "Datos obtenidos";
            } else {
                $response["result"] = "ERROR";
                $response["data"] = null;
                $response["message"] = "Error al obtener los datos.";
            }
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
?>
