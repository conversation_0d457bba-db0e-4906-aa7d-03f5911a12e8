<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $login = $_SESSION["user"];
            $company = $_SESSION["id"];
            $profile = $_SESSION["profile"];

            $selIdUser = obtener_parameter("idUser", "GET");

            $sql_filtro = "";

            if (!empty($selIdUser)) {
                $sql_filtro .= " AND A.id = $selIdUser";
            }

            if (select_type_profile($profile) != 1) {
                if (select_type_profile($profile) != 2) {
                    $sql_filtro .= " AND A.login = '$login' ";
                }
                $sql_filtro .= " AND COALESCE(A.company, 0) = $company ";
                $sql_filtro .= " AND COALESCE(P.id_profile, 0)  NOT IN (1,5) ";
            }

            $query = "";
            $query .= " SELECT ";
            $query .= " A.login AS username ";
            $query .= " , A.id AS iduser ";
            $query .= " , A.name AS nombres ";
            $query .= " , A.last_name AS apellidos ";
            $query .= " , A.mail AS mail ";
            $query .= " , A.phone AS telefono ";
            $query .= " , A.unit AS unidad ";
            $query .= " , A.enabled AS activo ";
            $query .= " , C.id AS idcompany ";
            $query .= " , C.nom_corto AS company ";
            $query .= " , T.id_time_zone AS idtimezone ";
            $query .= " , T.description AS timezone ";
            $query .= " , P.id_profile AS idperfil ";
            $query .= " , P.desc_profile AS perfil ";
            $query .= " FROM account A ";
            $query .= " LEFT JOIN account_profile AP ON A.id = AP.id_account ";
            $query .= " LEFT JOIN profile P on AP.id_profile = P.id_profile ";
            $query .= " LEFT JOIN company C ON A.company = C.id ";
            $query .= " LEFT JOIN time_zone T ON A.id_time_zone = T.id_time_zone ";
            $query .= " WHERE 1 ";
            $query .= " $sql_filtro ORDER by A.id DESC ";

            //Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
            header('Content-Type: application/json');

            $rawdata = array();
            $i = 0;
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $result = $stmt->fetchAll(PDO::FETCH_OBJ);
            foreach($result as $row){
                $fila = array();
                $fila["username"] = $row->username;
                $fila["nombres"] = $row->nombres;
                $fila["apellidos"] = $row->apellidos;
                $fila["mail"] = $row->mail;
                $fila["telefono"] = $row->telefono;
                $fila["unidad"] = $row->unidad;
                $fila["activo"] = $row->activo;
                $fila["company"] = $row->company;
                $fila["timezone"] = $row->timezone;
                $fila["perfil"] = $row->perfil;
                $fila["iduser"] = $row->iduser;
                $fila["idtimezone"] = $row->idtimezone;
                $fila["idcompany"] = $row->idcompany;
                $fila["idperfil"] = $row->idperfil;

                $rawdata[$i] = $fila;
                $i++;
            }
            $response["result"] = "OK";
            $response["data"] = $rawdata;
            $response["message"] = "Datos obtenidos";
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
