<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {

            $id = obtener_parameter("id", "GET");
            $mail = "";
            $query = "";
            $query = "SELECT mail FROM account WHERE id = ?";

            $stmt = $dbh->prepare($query);
            $stmt->execute(array($id));
            $result = $stmt->fetch(PDO::FETCH_OBJ);
            if ($result != null) {
                
                    $mail = $result->mail;
                
                if (!empty($mail)) {
                    //$pass = generateRandomString(15);
                    $pass = "123456";
                    $query = "";
                    $query .= " UPDATE account ";
                    $query .= " SET ";
                    $query .= " password = ? ";
                    $query .= " , change_pass = 1 ";
                    $query .= " WHERE id=? ";

                    $stmt = $dbh->prepare($query);
                    $stmt->execute(array($pass,$id));
                     $result = $stmt->fetch(PDO::FETCH_OBJ);
                   

                    $sendmail = 0;
                    if (defined("SEND_MAIL_PASS")) {
                        $sendmail = SEND_MAIL_PASS;
                        if ($sendmail == 1) {
                            $sendResponse = sendMailPass($pass, $mail);
                        }
                    }
                    $response["result"] = "OK";
                    $response["data"] = 1;
                    $response["message"] = "La contraseña del usuario ha sido reiniciada y enviada al mail registrado.";
                } else {
                    $response["result"] = "ERROR";
                    $response["message"] = "mail del usuario en blanco.";
                }
            } else {
                $response["result"] = "ERROR";
                $response["message"] = "Usuario sin mail registrado.";
            }
        } else {
            $response["result"] = "ERROR";
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);




