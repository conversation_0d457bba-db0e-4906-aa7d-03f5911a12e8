<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
$cant = 0;
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $company = $_SESSION["id"];
            $response["result"] = "ERROR";
            $selIdUser = obtener_parameter("iduser", "POST");
            $selUsername = obtener_parameter("username", "POST");
            $selNombres = obtener_parameter("nombres", "POST");
            $selApellidos = obtener_parameter("apellidos", "POST");
            $selMail = obtener_parameter("mail", "POST");
            $selTelefono = obtener_parameter("telefono", "POST");
            $selUnidad = obtener_parameter("unidad", "POST");
            $selIdCompany = obtener_parameter("idcompany", "POST");
            $selIdTimeZone = obtener_parameter("idtimezone", "POST");
            $selActivo = obtener_parameter("activo", "POST");
            $selIdPerfil = obtener_parameter("idperfil", "POST");

            if ($selIdCompany == "undefined" || $selIdCompany == null || empty($selIdCompany)) {
                $selIdCompany = $company;
            }

            $query = "";
            $query .= " SELECT ";
            $query .= " count(*) AS CANT ";
            $query .= " FROM ";
            $query .= " account ";
            $query .= " WHERE ";
            $query .= " id = ?";

            $stmt = $dbh->prepare($query);
            $stmt->execute(array($selIdUser ));
            $result = $stmt->fetch(PDO::FETCH_OBJ);
            $cant = $result->CANT;
            if ($cant > 0) {
                $cant = 0;
                $query = "";
                $query .= " SELECT ";
                $query .= " count(*) AS CANT ";
                $query .= " FROM ";
                $query .= " account ";
                $query .= " WHERE ";
                $query .= " login = ? ";
                $query .= " AND id NOT IN (?) ";


                $stmt = $dbh->prepare($query);
                $stmt->execute(array($selUsername,$selIdUser ));
                $result = $stmt->fetch(PDO::FETCH_OBJ);
               
                $cant=$result->CANT;
                if ($cant == 0) {
                    $query = "";
                    $query .= " UPDATE account ";
                    $query .= " SET ";
                    $query .= " login =  ?";
                    $query .= " ,company = ? ";
                    $query .= " ,enabled = ? ";
                    $query .= " ,id_time_zone = ?";
                    $query .= " ,name = ? ";
                    $query .= " ,last_name = ? ";
                    $query .= " ,mail = ? ";
                    $query .= " ,phone = ? ";
                    $query .= " ,unit = ? ";
                    $query .= " ,update_time = UTC_TIMESTAMP() ";
                    $query .= " WHERE id = ? ";
                    
                    $stmt = $dbh->prepare($query);
                    $stmt->execute(array($selUsername,$selIdCompany,$selActivo,$selIdTimeZone,$selNombres,
                    $selApellidos,$selMail,$selTelefono,$selUnidad,$selIdUser));
                    

                    $filas_afectadas = $stmt->rowCount();
                    //Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
                    header('Content-Type: application/json');
                    if ($filas_afectadas > 0) {
                        $query = "";
                        $query .= " SELECT COUNT(*) AS CANT ";
                        $query .= " FROM account_profile ";
                        $query .= " WHERE id_account = ? ";

                        $stmt = $dbh->prepare($query);
                        $stmt->execute(array($selIdUser ));
                        $result = $stmt->fetch(PDO::FETCH_OBJ);
                        $cant = $result->CANT;
                        $id = 0;
                        if ($cant > 0) {
                            $query = "";
                            $query .= " SELECT id_accprof AS id";
                            $query .= " FROM account_profile ";
                            $query .= " WHERE id_account = ? LIMIT 1 ";

                            $stmt = $dbh->prepare($query);
                            $stmt->execute(array($selIdUser));
                            $result = $stmt->fetch(PDO::FETCH_OBJ);

                            $id = $result->id;
                            $query = "";
                            $query .= " UPDATE account_profile ";
                            $query .= " SET id_profile = ? ";
                            $query .= " WHERE id_accprof = ? ";

                            $stmt = $dbh->prepare($query);
                            $stmt->execute(array($selIdPerfil,$id));
                            $res = 1;
                        } else {
                            $query = "";
                            $query .= " INSERT INTO account_profile ( ";
                            $query .= " id_account, id_profile ";
                            $query .= " ) VALUES ( ";
                            $query .= " ?, ? ";
                            $query .= " ) ";
                            $stmt = $dbh->prepare($query);
                            $stmt->execute(array($selIdUser,$selIdPerfil));
                            $res = 1;
                        }
                        if ($res > 0) {
                            $response["result"] = "OK";
                            $response["data"] = "$id";
                            $response["message"] = "Datos actualizados";
                        } else {
                            $response["result"] = "ERROR";
                            $response["data"] = "0";
                            $response["message"] = "Error al agregar el perfil del usuario";
                        }
                    } else {
                        $response["data"] = "0";
                        $response["message"] = "Error al actualizar el usuario";
                    }
                } else {
                    //login ya usado debe ingresar otro
                    $response["result"] = "ERROR";
                    $response["data"] = null;
                    $response["message"] = "El login ingresado ya esta siendo utilizado.";
                }
            } else {
                //usuario inexistente
                $response["result"] = "ERROR";
                $response["data"] = null;
                $response["message"] = "El id usuario a actualizar no existe.";
            }
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
?>
