<?php

include("../../dbConn.php");
include("../../includes/php/general_funcs.php");
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $login = $_SESSION["user"];
            $company = $_SESSION["id"];
            $profile = $_SESSION["profile"];
            $id_profile = $_SESSION["id_profile"];
            $offset = $_SESSION["OFFSET_COMPANY"];

            $selcompany = obtener_parameter("company", "GET");
            $selprofile = obtener_parameter("profile", "GET");
            $filtertext = obtener_parameter("txtfilter", "GET");
            $page = obtener_parameter("page", "GET");
            $rows = obtener_parameter("rows", "GET");
            $cant_rows_page = 10;
            if ($rows == null) {
                $cant_rows_page = 10;
            } else {
                $cant_rows_page = $rows;
            }

            $limit = 0;
            $sql_filtro = "";
            if (!empty($page)) {
                $limit = $page;
                if ($limit > 0) {
                    $limit -= 1;
                }
            } else {
                $limit = 0;
            }
            if (select_type_profile($profile) != 1) {
                if (select_type_profile($profile) != 2) {
                    $sql_filtro .= " AND A.login = '$login' ";
                }
                $sql_filtro .= " AND COALESCE(A.company, 0) = $company ";
                $sql_filtro .= " AND COALESCE(P.id_profile, 0)  NOT IN (1,5) ";
            }
            if ($id_profile != 1) {
                $sql_filtro .= " AND COALESCE(P.id_profile, 0)  NOT IN (6) ";
            }

            if (!empty($selcompany)) {
                $sql_filtro .= " AND COALESCE(C.id, 0) = $selcompany ";
            }

            if (!empty($selprofile)) {
                $sql_filtro .= " AND COALESCE(P.id_profile, 0) = $selprofile ";
            }

            if (!empty($filtertext)) {
                $sql_filtro .= "AND ( ";
                $sql_filtro .= "	A.login LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR CONCAT(CONCAT(A.name,' '), A.last_name) LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR A.mail LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR A.phone LIKE '%$filtertext%' ";
                $sql_filtro .= "	OR A.unit LIKE '%$filtertext%' ";
                $sql_filtro .= " ) ";
            }

            // $sql_filtro .= " AND COALESCE(P.id_profile, 0) NOT IN (4) ";

            
                    
            $query = "";
            $query .= " SELECT ";
            $query .= " A.login AS username ";
            $query .= " , A.id AS iduser ";
            $query .= " , A.name AS nombres ";
            $query .= " , A.last_name AS apellidos ";
            $query .= " , A.mail AS mail ";
            $query .= " , A.phone AS telefono ";
            $query .= " , A.unit AS unidad ";
            $query .= " , A.enabled AS activo ";
            $query .= " , C.nom_corto AS company ";
            $query .= " , T.description AS timezone ";
            $query .= " , P.desc_profile AS perfil ";
            $query .= " FROM account A ";
            $query .= " LEFT JOIN account_profile AP ON A.id = AP.id_account ";
            $query .= " LEFT JOIN profile P on AP.id_profile = P.id_profile ";
            $query .= " LEFT JOIN company C ON A.company = C.id ";
            $query .= " LEFT JOIN time_zone T ON A.id_time_zone = T.id_time_zone ";
            $query .= " WHERE 1 ";
            $query .= " $sql_filtro ORDER by A.id DESC ";

            //Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
            header('Content-Type: application/json');

            $rawdata = array();
            $i = 0;

            // Preparar la consulta
            $stmt2 = $dbh->prepare($query);

            // Ejecutar la consulta
            $stmt2->execute();

            // Obtener el número de filas
            $cant_rows = $stmt2->rowCount();

            $offset_rows = $limit * $cant_rows_page;
            $stmt = $dbh->prepare("$query LIMIT $cant_rows_page  OFFSET $offset_rows");
           // $stmt->bindValue(':cant_rows_page', $cant_rows_page, PDO::PARAM_INT);
            //$stmt->bindValue(':offset_rows', $offset_rows, PDO::PARAM_INT);
            $stmt->execute(array());

                       
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $fila = array();
                $fila["username"] = $row["username"];
                $fila["nombres"] = $row["nombres"];
                $fila["apellidos"] = $row["apellidos"];
                $fila["mail"] = $row["mail"];
                $fila["telefono"] = $row["telefono"];
                $fila["unidad"] = $row["unidad"];
                $fila["activo"] = $row["activo"];
                $fila["company"] = $row["company"];
                $fila["timezone"] = $row["timezone"];
                $fila["perfil"] = $row["perfil"];
                $fila["iduser"] = $row["iduser"];

                $rawdata[$i] = $fila;
                $i++;
            }
            $response["result"] = "OK";
            $response["data"] = $rawdata;
            $response["totalRows"] = $cant_rows;
            $response["message"] = "Datos obtenidos";
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);
?>
