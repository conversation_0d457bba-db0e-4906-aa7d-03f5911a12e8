<?php

include('../../dbConn.php');
include('../../includes/php/general_funcs.php');
include ('../../includes/php/general_function_control.php');
include ('../../includes/php/general_function_return.php');
include ('../../includes/php/general_function_page.php');

$response = array();
try {
    if (isset($_SESSION["auth"])) {
        if ($_SESSION["auth"] == "1") {
            $company = $_SESSION["id"];
            $selIdUser = obtener_parameter("iduser", "POST");
            $selUsername = obtener_parameter("username", "POST");
            $selNombres = obtener_parameter("nombres", "POST");
            $selApellidos = obtener_parameter("apellidos", "POST");
            $selMail = obtener_parameter("mail", "POST");
            $selTelefono = obtener_parameter("telefono", "POST");
            $selUnidad = obtener_parameter("unidad", "POST");
            $selIdCompany = obtener_parameter("idcompany", "POST");
            $selIdTimeZone = obtener_parameter("idtimezone", "POST");
            $selActivo = obtener_parameter("activo", "POST");
            $selIdPerfil = obtener_parameter("idperfil", "POST");

            if ($selIdCompany == "undefined" || $selIdCompany == null || empty($selIdCompany)) {
                $selIdCompany = $company;
            }

            $query = "";
            $query .= " SELECT ";
            $query .= " COUNT(*) AS CANT ";
            $query .= " FROM ";
            $query .= " account ";
            $query .= " WHERE ";
            $query .= " id = ? ";
            $stmt = $dbh->prepare($query);
            $stmt->execute(array($selIdUser));
            $result = $stmt->fetch(PDO::FETCH_OBJ);
            $cant = $result->CANT;
            if ($cant == 0) {
                //seguimos
                $query = "";
                $query .= " SELECT ";
                $query .= " COUNT(*) AS CANT ";
                $query .= " FROM ";
                $query .= " account ";
                $query .= " WHERE ";
                $query .= " login = ? ";
                
                $stmt = $dbh->prepare($query);
                $stmt->execute(array($selUsername));
                $result = $stmt->fetch(PDO::FETCH_OBJ);
                $cant = $result->CANT;
                if ($cant == 0) {
                    //$pass = generateRandomString(15);
                    $pass = "not used";
                    $encriptPass = $pass;

                    $query = "";
                    $query .= " INSERT INTO account ";
                    $query .= " ( ";
                    $query .= " login, password, ";
                    $query .= " company, enabled, created, ";
                    $query .= " id_time_zone, name, last_name, ";
                    $query .= " mail, phone, unit, change_pass ";
                    $query .= " ) VALUES ( ";
                    $query .= " ?, ? , ? , ?,UTC_TIMESTAMP() , ?, ?, ?  ";
                    $query .= " , ?, ?, ?, 1";
                    $query .= " ) ";

                    $stmt = $dbh->prepare($query);
                    $stmt->execute(array($selUsername,$encriptPass,$selIdCompany,$selActivo,$selIdTimeZone,
                    $selNombres,$selApellidos,$selMail,$selTelefono,$selUnidad));
                    $id = $dbh->lastInsertId();
                    

                    $sendmail = 0;
                    if ($id > 0) {
                        $query = "";
                        $query .= " INSERT INTO account_profile ";
                        $query .= " ( ";
                        $query .= " id_account, id_profile ";
                        $query .= " )VALUES( ";
                        $query .= " ?, ? ";
                        $query .= " ) ";

                        $stmt = $dbh->prepare($query);
                        $stmt->execute(array($id,$selIdPerfil));
                        $id_accprof = $dbh->lastInsertId();

                        $responseMail = resetPassword($id);
                        $jsonResponse = json_decode($responseMail);
                        if ($jsonResponse->code == 0) {
                            $response["result"] = "OK";
                            $response["data"] = $id;
                            $response["message"] = "Datos insertados";
                        } else {
                            $response["result"] = "ERROR";
                            $response["data"] = null;
                            $response["message"] = "Error al intentar enviar la contraseña";
                        }
                    } else {
                        $response["result"] = "ERROR";
                        $response["data"] = null;
                        $response["message"] = "Error al insertar los datos.";
                    }
                } else {
                    $response["result"] = "ERROR";
                    $response["data"] = null;
                    $response["message"] = "El username ya esta en uso favor de ingresar otro valor.";
                    //login ya usado debe ingresar otro
                }
            } else {
                $response["result"] = "ERROR";
                $response["data"] = null;
                $response["message"] = "Error el identificador de usuario ya existe.";
                //usuario existe
            }
        } else {
            $response["result"] = "ERROR";
            $response["data"] = null;
            $response["message"] = "error de autentificacion.";
            redirectLogOff();
        }
    } else {
        $response["result"] = "ERROR";
        $response["data"] = null;
        $response["message"] = "error de autentificacion.";
        redirectLogOff();
    }
} catch (Exception $exc) {
    error_log($exc->getTraceAsString());
    $response["result"] = "ERROR";
    $response["data"] = null;
    $response["message"] = "Error al generar los datos.";
}
//Seteamos el header de "content-type" como "JSON" para que jQuery lo reconozca como tal
header('Content-Type: application/json');
//Devolvemos el array pasado a JSON como objeto
echo json_encode($response, JSON_FORCE_OBJECT);

$dbh=null;
