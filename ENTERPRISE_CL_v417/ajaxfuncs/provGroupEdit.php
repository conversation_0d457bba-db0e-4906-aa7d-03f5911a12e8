<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');
$usrAsoc = '';
$company = $_SESSION["id"];
$login = $_SESSION["user"];
$name = obtener_parameter('name', 'GET');
$idGroup = obtener_parameter('idGroup', 'GET');
$oldNameGrp = return_name_group($company, $idGroup);

//$sql =("SELECT name FROM provGroup WHERE company = $company AND name = '$name' AND id != $idGroup");

$sql = "SELECT name FROM provGroup WHERE company = :company AND name = :name AND id != :idGroup";
    $stmt = $dbh->prepare($sql);
    
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->bindParam(':name', $name, PDO::PARAM_STR);
    $stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
    
    $stmt->execute();
    
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<div class="alert_big"> 

    <?php if (!empty($row['name'])) { ?>

        <img src="images/icons/error.png"> El Grupo <b><?= $name ?></b> ya existe, favor probar nuevamente.

    <?php } else {

        //("UPDATE provGroup SET name = '$name', created = UTC_TIMESTAMP()  WHERE company = $company AND id = $idGroup");
        
        try {
            $sql = "UPDATE provGroup SET name = :name, created = UTC_TIMESTAMP() WHERE company = :company AND id = :idGroup";
            $stmt = $dbh->prepare($sql);
            
            $stmt->bindParam(':name', $name, PDO::PARAM_STR);
            $stmt->bindParam(':company', $company, PDO::PARAM_INT);
            $stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
            
            $stmt->execute();
            
            echo "Actualización exitosa en provGroup";
        } catch (PDOException $e) {
            echo "Error en la actualización: " . $e->getMessage();
        }
        ?>

        <img src="images/icons/confirm.png"> Los cambios han sido realizado exitosamente.

<?php } ?>


</div> 

<?php
$dbh=null;
$action = "idGroup => '" . $idGroup . "', oldName => '" . $oldNameGrp . "', newName => " . $name . "', groups => '" . $usrAsoc . "'";
//	log_tmc($company, "Aprovisionamiento", "Editar Grupo", $action, $login); 
?>