<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
if (isset($_POST['idGroup'])) {
	$idGroup = $_POST['idGroup'];
} else {
	$idGroup = '';
}
if (isset($_POST['msgtext'])) {
	$msgtext = $_POST['msgtext'];
} else {
	$msgtext = '';
}
$format = "d-m-Y H:i:s";
$dif_hour = "-0 hour";
$date_now = return_date_now();
$fecha = return_date_now_format($format);
?>

<div class="alert_big"> 
	<?php
	
	add_process($company, "WEB-GRP", $idGroup, $login);
	$process = return_process($company, "WEB-GRP");
	if (!empty($_COOKIE['cookie']) && !empty($_SESSION["id"])) {
		if (validate_format_datetime($fecha)) {
			if (validate_time_available_send_message($fecha)) {
				if (validacion_quota_sms()) {
					// $query_group = "SELECT * "
					// 		. " FROM UserGroup "
					// 		. " WHERE idGroup = $idGroup AND company = $company"; 
					// $sql = mysql_($query_group) or die("Error en Base de Datos" . mysql_error());
					// while ($row = mysql_fetch_array($sql)) {
					// 	$phone = $row['phone'];
					// 	$carrier = return_carrier($phone);
					// 	$query_insert="INSERT into trafficMT ("
					// 			. "company, recipientId, recipientDomain"
					// 			. ", status, login, receivedTime, dispatchTime, msgText"
					// 			. ", input_mode, input_process"
					// 			. ") values("
					// 			. "'$company', '$phone', '$carrier'"
					// 			. ", 'QUEUED', '$login', UTC_TIMESTAMP(), UTC_TIMESTAMP() "
					// 			. ", '$msgtext', 'WEB-GRP', '$process')";
					// 	mysql_($query_insert) or die("Error al realizar el env&iacute;o (Codigo Error: " . mysql_errno() . ")" . del_process($company, $process) . "");
					// }

					try {
						$query_group = "SELECT * FROM UserGroup WHERE idGroup = :idGroup AND company = :company";
						$stmt_group = $dbh->prepare($query_group);
						$stmt_group->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
						$stmt_group->bindParam(':company', $company, PDO::PARAM_INT);
						$stmt_group->execute();
					
						while ($row = $stmt_group->fetch(PDO::FETCH_ASSOC)) {
							$phone = $row['phone'];
							$carrier = return_carrier($phone);
					
							$query_insert = "INSERT INTO trafficMT (company, recipientId, recipientDomain, status, login, receivedTime, dispatchTime, msgText, input_mode, input_process) VALUES (:company, :phone, :carrier, 'QUEUED', :login, UTC_TIMESTAMP(), UTC_TIMESTAMP(), :msgtext, 'WEB-GRP', :process)";
							$stmt_insert = $dbh->prepare($query_insert);
					
							$stmt_insert->bindParam(':company', $company, PDO::PARAM_INT);
							$stmt_insert->bindParam(':phone', $phone, PDO::PARAM_STR);
							$stmt_insert->bindParam(':carrier', $carrier, PDO::PARAM_STR);
							$stmt_insert->bindParam(':login', $login, PDO::PARAM_STR);
							$stmt_insert->bindParam(':msgtext', $msgtext, PDO::PARAM_STR);
							$stmt_insert->bindParam(':process', $process, PDO::PARAM_STR);
					
							$stmt_insert->execute();
						}
					
						echo "Inserciones exitosas en trafficMT";
					} catch (PDOException $e) {
						echo "Error en las inserciones en trafficMT: " . $e->getMessage();
						// Aquí puedes ejecutar del_process si es necesario
						del_process($company, $process);
					}
					
					?> 
					<img src="images/icons/confirm.png"> Su <b>mensaje</b> ha despachado exitosamente.
					<?php
				} else {
					?>
					<img src="images/icons/error.png"/> Se ha excedido la quota de mensajes disponibles, contacte al administrador.
					<?php
				}
			} else {
				?>
				<img src="images/icons/error.png"/> No se puede agregar mensajes, ya que esta fuera de los limites de disponibilidad del servicio.
				<?php
			}
		} else {
			?>
			<img src="images/icons/error.png"/> El formato de la fecha es incorrecto.
			<?php
		}
	} else {
		?>
		<img src="images/icons/alert.png"> Su sesi&oacute;n ha expirado. Vuelva a ingresar <a href="index.php">aqu&iacute;</a>
		<?php
	}
	$action = "idGroup => " . $idGroup . "',  msgtext => '" . $msgtext . "'";
	$dbh=null;
//	log_tmc($company, "Envio SMS", "Grupal Inmediato", $action, $login);
	?>
</div>