<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');
include('../config.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];

$phone = $_GET['phone'];
if (strlen($phone) == 8) {
    $phone = $phone;
}
if (strlen($phone) == 9) {

    $phone = substr($phone, 1, 8);
}
if (strlen($phone) == 11) {
    $phone = substr($phone, 3, 8);
}
$carrier = return_carrier($phone);
$nombre = $_GET['nombre'];
$apellido = $_GET['apellido'];
$idGroup = $_GET['idGroup'];

//("INSERT into provUser (company, phone, carrier, name1, name2, created, login) VALUES ('$company', '$phone', '$carrier', '$nombre', '$apellido', UTC_TIMESTAMP(), '$login')") or die(mysql_error());
try {
    $query = "INSERT INTO provUser (company, phone, carrier, name1, name2, created, login) VALUES (:company, :phone, :carrier, :name1, :name2, UTC_TIMESTAMP(), :login)";
    $stmt = $dbh->prepare($query);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
    $stmt->bindParam(':carrier', $carrier, PDO::PARAM_STR);
    $stmt->bindParam(':name1', $nombre, PDO::PARAM_STR);
    $stmt->bindParam(':name2', $apellido, PDO::PARAM_STR);
    $stmt->bindParam(':login', $login, PDO::PARAM_STR);
    
    $stmt->execute();
    
    
} catch (PDOException $e) {
    echo "Error en la inserción en provUser: " . $e->getMessage();
}
//("INSERT into UserGroup (company, phone, idGroup, login) VALUES ('$company', '$phone', '$idGroup', '$login')") or die(mysql_error());
try {
    $query = "INSERT INTO UserGroup (company, phone, idGroup, login) VALUES (:company, :phone, :idGroup, :login)";
$stmt = $dbh->prepare($query);
$stmt->bindParam(':company', $company, PDO::PARAM_INT);
$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
$stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
$stmt->bindParam(':login', $login, PDO::PARAM_STR);
    
    $stmt->execute();
    
} catch (PDOException $e) {
    error_log( $e->getMessage());
}
$dbh=null;
?>

<div class="alert_big">
    <img src="images/icons/confirm.png"> La operaci&oacute;n ha sido realizada exitosamente.</div>
<br />
<br />
<a href="?seccion=provGroupAdd" class="styleButtonOne">
    <span class="buttom">CREAR NUEVO GRUPO</span>
</a>
<a href="?seccion=provGroups" class="styleButtonOne">
    <span class="buttom">VOLVER</span>
</a>