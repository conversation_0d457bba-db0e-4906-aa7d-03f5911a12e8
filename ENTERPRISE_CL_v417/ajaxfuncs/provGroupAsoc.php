<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');
$usrAsoc = '';
$company = $_SESSION["id"];
$login = $_SESSION["user"];

$phone = obtener_parameter('phone', 'GET');
$idGroup = obtener_parameter('idGroup', 'GET');
$action = obtener_parameter('action', 'GET');
?>

<?php

if ($action == "Y") {
    //("INSERT into UserGroup (company, phone, idGroup, login) VALUES ('$company', '$phone', '$idGroup', '$login')") or die(mysql_error());
    try {
        $sql = "INSERT INTO UserGroup (company, phone, idGroup, login) VALUES (:company, :phone, :idGroup, :login)";
        $stmt = $dbh->prepare($sql);
        
        $stmt->bindParam(':company', $company, PDO::PARAM_STR);
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
        $stmt->bindParam(':login', $login, PDO::PARAM_STR);
        
        $stmt->execute();
        
        echo "Inserción exitosa";
    } catch (PDOException $e) {
        echo "Error: " . $e->getMessage();
    }
    $texAction = "Asociar";
    ?>

    <img src="images/icons/confirm.png" />

    <?php

} else {

    //("DELETE FROM UserGroup WHERE phone = '$phone' AND company = '$company' AND idGroup = $idGroup") or die(mysql_error());
    try {
        $sql = "DELETE FROM UserGroup WHERE phone = :phone AND company = :company AND idGroup = :idGroup";
        $stmt = $dbh->prepare($sql);
        
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':company', $company, PDO::PARAM_STR);
        $stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
        
        $stmt->execute();
        
        echo "Eliminación exitosa";
    } catch (PDOException $e) {
        echo "Error: " . $e->getMessage();
    }
   
    
    
    
    
    
    $texAction = "Desasociar";
    ?>

    <img src="images/icons/delete.png" />

<?php } ?>

<?php
$dbh=null;
$action = "idGroup => '" . $idGroup . "', Asociar Usuario(s) => '" . $usrAsoc . "'";
//log_tmc($company, "Aprovisionamiento", "Asociar Usuarios al Grupo", $action, $login); 
?>