<?php

include ('../includes/php/general_funcs.php'); 
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php'); 

	$company   			= $_SESSION["id"]; 
	if(isset($_GET['phone'])){
		$phone = $_GET['phone'];
	}else{
		$phone = '';
	}
	if(isset($_GET['oldpassword'])){
		$oldPassword = $_GET['oldpassword'];
	}else{
		$oldPassword = '';
	}
	if(isset($_GET['password'])){
		$newPassword = $_GET['password'];
	}else{
		$newPassword = '';
	}
	$login = $_SESSION["user"];
	
	try {
		$query = "SELECT password FROM account WHERE company = :company AND login = :login AND password = :oldPassword";
		$stmt = $dbh->prepare($query);
		$stmt->bindParam(':company', $company, PDO::PARAM_INT);
		$stmt->bindParam(':login', $login, PDO::PARAM_STR);
		$stmt->bindParam(':oldPassword', $oldPassword, PDO::PARAM_STR);
		$stmt->execute();
		
		$row = $stmt->fetch(PDO::FETCH_ASSOC);
		$password = $row['password'];
		
		// Ahora, $password contiene la contraseña
	} catch (PDOException $e) {
		echo "Error en la consulta en account: " . $e->getMessage();
	}?>
	
	<div class="alert_big">
	
	<?php 	if(empty($password)) { ?>
	
			<img src="images/icons/confirm.png"> La <b>Actual Password</b> no coincide con la digitada.
          
          <?php } else { 

			// mysql_("UPDATE account SET password = '$newPassword' WHERE login = '$login' AND company = $company") or die(mysql_error());
			// $dbPor1 = dbMysql ("************", "tmc_bchile_all");
			// mysql_("UPDATE account SET password = '$newPassword' WHERE login = '$login' AND company = $company", $dbPor1) or die(mysql_error());
			try {
				// Primera actualización en la base de datos actual
				$query = "UPDATE account SET password = :newPassword WHERE login = :login AND company = :company";
				$stmt = $dbh->prepare($query);
				$stmt->bindParam(':newPassword', $newPassword, PDO::PARAM_STR);
				$stmt->bindParam(':login', $login, PDO::PARAM_STR);
				$stmt->bindParam(':company', $company, PDO::PARAM_INT);
				$stmt->execute();
			
				// Segunda actualización en otra base de datos (ejemplo)
				$dbPor1 = new PDO("mysql:host=************;dbname=tmc_bchile_all", $username, $password);
				$dbPor1->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
			
				$queryPor1 = "UPDATE account SET password = :newPassword WHERE login = :login AND company = :company";
				$stmtPor1 = $dbPor1->prepare($queryPor1);
				$stmtPor1->bindParam(':newPassword', $newPassword, PDO::PARAM_STR);
				$stmtPor1->bindParam(':login', $login, PDO::PARAM_STR);
				$stmtPor1->bindParam(':company', $company, PDO::PARAM_INT);
				$stmtPor1->execute();
			
				echo "Actualización exitosa en account";
			} catch (PDOException $e) {
				echo "Error en la actualización en account: " . $e->getMessage();
			}
			
			?>
               
               <img src="images/icons/confirm.png"> Los cambios han sido realizados exitosamente.

		<?php } ?>

	</div>
     
     <?php 
	 $dbh=null;
	$action = "oldPassword => '".$oldPassword."',  newPassword => '".$newPassword."'";  
//	log_tmc($company, "Soporte", "Cambiar Password", $action, $login); 
	?>
