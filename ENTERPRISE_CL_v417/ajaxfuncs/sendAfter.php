<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
if (isset($_POST['phone'])) {
	$phone = $_POST['phone'];
} else {
	$phone = '';
}
if (isset($_POST['sendDateAfter'])) {
	$date = $_POST['sendDateAfter'];
	if( strlen($date)<17){
		$date .=":00";
	}
} else {
	$date = '';
}
if (isset($_POST['msgtext'])) {
	$msgtext = $_POST['msgtext'];
} else {
	$msgtext = '';
}


$format_date = "Y-m-d H:i:s";
$date_now = return_date_now_format($format_date);
	

$format = "d-m-Y H:i:s";
$dif_hour = "-0 hour";    

$fecha_gmt = return_date_after_gmt($date);
$fecha_local = return_date_after_format($date, $format);

$carrier = return_carrier($phone);
$content='';
?>

<div class="alert_big">
	<?php
	if (!empty($_COOKIE['cookie']) && !empty($_SESSION["id"])) {
//		if ($carrier != "UNKNOWN") {
			if (validate_format_datetime($fecha_local)) {
				if (validate_time_available_send_message($fecha_local)) {
					if (validacion_quota_sms()) {
						add_process($company, "WEB-IND", $content, $login);
						$process = return_process($company, "WEB-IND");
						// $query="INSERT into trafficMT ("
						// 		. "company, recipientId, recipientDomain"
						// 		. ", status, login, receivedTime, msgText"
						// 		. ", input_process, dispatchTime"
						// 		. ") values  ("
						// 		. " '$company', '$phone', '$carrier',"
						// 		. " 'QUEUED', '$login', UTC_TIMESTAMP(), '$msgtext'"
						// 		. ", '$process', '$fecha_gmt')";
						// ($query) 
						// 	or die("Error al realizar el env&iacute;o (Codigo Error: " . mysql_errno() . " - " . mysql_error() . " )" 
						// 		. del_process($company, $process) . "");

						try {
							$query = "INSERT INTO trafficMT (company, recipientId, recipientDomain, status, login, receivedTime, msgText, input_process, dispatchTime) VALUES (:company, :recipientId, :recipientDomain, 'QUEUED', :login, UTC_TIMESTAMP(), :msgText, :input_process, :dispatchTime)";
							$stmt = $dbh->prepare($query);
							
							$stmt->bindParam(':company', $company, PDO::PARAM_INT);
							$stmt->bindParam(':recipientId', $phone, PDO::PARAM_STR);
							$stmt->bindParam(':recipientDomain', $carrier, PDO::PARAM_STR);
							$stmt->bindParam(':login', $login, PDO::PARAM_STR);
							$stmt->bindParam(':msgText', $msgtext, PDO::PARAM_STR);
							$stmt->bindParam(':input_process', $process, PDO::PARAM_STR);
							$stmt->bindParam(':dispatchTime', $fecha_gmt, PDO::PARAM_STR);
							
							$stmt->execute();
							
							echo "Inserción exitosa en trafficMT";
						} catch (PDOException $e) {
							echo "Error en la inserción en trafficMT: " . $e->getMessage();
							// Aquí puedes ejecutar del_process si es necesario
							del_process($company, $process);
						}
						

						?>
						<img src="images/icons/confirm.png"> Su <b>mensaje</b> ha sido agendado exitosamente.
						<?php
					} else {
						?>
						<img src="images/icons/error.png"/> Se ha excedido la quota de mensajes disponibles, contacte al administrador.
						<?php
					}
				} else {
					?>
					<img src="images/icons/error.png"/> La fecha de agendamiento esta fuera de los limites de disponibilidad del servicio.
					<?php
				}
			} else {
				?>
				<img src="images/icons/error.png"/> El formato de la fecha de agendamiento es incorrecto.
				<?php
			}
//		} else {
			?>
			<!--
			<img src="images/icons/error.png"> 
			El m&oacute;vil <b>< ?=$phone ? ></b> no est&aacute; dentro de los rangos v&aacute;lidos 
			de numeraci&oacute;n, favor intentar con otro n&uacute;mero.
			-->
			<?php
//		}
	} else {
		?>
		<img src="images/icons/alert.png"> Su sesi&oacute;n ha expirado. Vuelva a ingresar <a href="index.php">aqu&iacute;</a>
		<?php
	}
	$dbh=null;
//	 $action = "phone => '" . $phone . "',  date => '" . $date_after . "', msgtext => '" . $msgtext . "'";
//	log_tmc($company, "Envio SMS", "Individual Agendado", $action, $login);	
	?>
</div>