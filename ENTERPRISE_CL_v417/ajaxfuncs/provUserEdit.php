<?php
	
include('../dbConn.php');
include('../includes/php/general_funcs.php'); 
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');
	
	$company		= $_SESSION["id"];; 
	$phone = obtener_parameter('phone', 'GET');

	$firstName = obtener_parameter('firstName','GET');
	$lastName = obtener_parameter('lastName','GET');
	$groups = substr_replace( obtener_parameter('groups','GET'), '', -1, 1);
	$login = $_SESSION["user"];
	// $query = "SELECT * FROM provUser WHERE company = $company AND phone = $phone";
	// $sql =($query);
	
	// $row	= mysql_fetch_array($sql);
	
	// $oldName	= $row['name1']." ".$row['name2'];
	// $query = "UPDATE provUser SET name1 = '$firstName', name2 = '$lastName', login = '$login' WHERE phone = $phone AND company = $company";
	// mysql_($query) or die (mysql_error());

	try {
		// Consulta SELECT
		$query = "SELECT * FROM provUser WHERE company = :company AND phone = :phone";
		$stmt = $dbh->prepare($query);
		$stmt->bindParam(':company', $company, PDO::PARAM_INT);
		$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
		$stmt->execute();
		
		$row = $stmt->fetch(PDO::FETCH_ASSOC);
		
		$oldName = $row['name1'] . " " . $row['name2'];
		
		// Actualización
		$query = "UPDATE provUser SET name1 = :firstName, name2 = :lastName, login = :login WHERE phone = :phone AND company = :company";
		$stmt = $dbh->prepare($query);
		$stmt->bindParam(':firstName', $firstName, PDO::PARAM_STR);
		$stmt->bindParam(':lastName', $lastName, PDO::PARAM_STR);
		$stmt->bindParam(':login', $login, PDO::PARAM_STR);
		$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
		$stmt->bindParam(':company', $company, PDO::PARAM_INT);
		$stmt->execute();
		
		
	} catch (PDOException $e) {
	
		error_log("Error en la consulta o actualización:  " . $e->getMessage());
	}

	if(!empty($groups)) { 
	
		$groupLog = $groups;
	
		//("DELETE FROM UserGroup WHERE company = $company AND phone = $phone ") or die (mysql_error());
		try {
			// Eliminación en la tabla "UserGroup"
			$query = "DELETE FROM UserGroup WHERE company = :company AND phone = :phone";
			$stmt = $dbh->prepare($query);
			$stmt->bindParam(':company', $company, PDO::PARAM_INT);
			$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
			$stmt->execute();
			
			
		} catch (PDOException $e) {
			
			error_log("Error en la eliminación en UserGroup:  " . $e->getMessage());
		}
		$groups = explode(',', $groups);
	
		for($i = 0; $i < count($groups); $i++) {
			
			$group = $groups[$i];
			//("INSERT into UserGroup (company, phone, idGroup, login) values ('$company', '$phone', '$group', '$login')") or die (mysql_error());	
			try {
				$query = "INSERT INTO UserGroup (company, phone, idGroup, login) VALUES (:company, :phone, :group, :login)";
				$stmt = $dbh->prepare($query);
				
				$stmt->bindParam(':company', $company, PDO::PARAM_INT);
				$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
				$stmt->bindParam(':group', $group, PDO::PARAM_INT);
				$stmt->bindParam(':login', $login, PDO::PARAM_STR);
				
				$stmt->execute();
				
				echo "Inserción exitosa en UserGroup";
			} catch (PDOException $e) {
				echo "Error en la inserción en UserGroup: " . $e->getMessage();
				error_log("Error en la inserción en UserGroup: " . $e->getMessage());
			}
		}
		
	} else { 
	
		$groupLog = "No";
	
		//("DELETE FROM UserGroup WHERE company = $company AND phone = $phone") or die (mysql_error());
		try {
			// Eliminación en la tabla "UserGroup"
			$query = "DELETE FROM UserGroup WHERE company = :company AND phone = :phone";
			$stmt = $dbh->prepare($query);
			$stmt->bindParam(':company', $company, PDO::PARAM_INT);
			$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
			$stmt->execute();
			
			
		} catch (PDOException $e) {
			
			error_log("Error en la eliminación en UserGroup:  " . $e->getMessage());
		}
	} ?>
     
	<?php 
	$dbh=null;
	$action = "oldName => '".$oldName."',  newName==> '".$firstName." ".$lastName."', groups => '".$groupLog."'"; 
//	log_tmc($company, "Aprovisionamiento", "Editar Abonado", $action, $login); 
	?>
	
	<div class="alert_big">
		<img src="images/icons/confirm.png"> 
		Los cambios han sido realizados exitosamente.
	</div>
