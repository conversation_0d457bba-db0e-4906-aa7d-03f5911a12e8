<?php
	
include('../dbConn.php');
include('../includes/php/general_funcs.php'); 
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php'); 
	
	$company		= $_SESSION["id"];
	$phone			= $_GET['phone'];
	$login			= $_SESSION["user"];
	
	//("DELETE FROM provUser WHERE phone = $phone AND company = $company") or die(mysql_error());
	//("DELETE FROM UserGroup WHERE phone = $phone AND company = $company") or die(mysql_error());
	try {
		// Eliminación en la tabla "provUser"
		$sql1 = "DELETE FROM provUser WHERE phone = :phone AND company = :company";
		$stmt1 = $dbh->prepare($sql1);
		
		$stmt1->bindParam(':phone', $phone, PDO::PARAM_STR);
		$stmt1->bindParam(':company', $company, PDO::PARAM_INT);
		
		$stmt1->execute();
		
		// Eliminación en la tabla "UserGroup"
		$sql2 = "DELETE FROM UserGroup WHERE phone = :phone AND company = :company";
		$stmt2 = $dbh->prepare($sql2);
		
		$stmt2->bindParam(':phone', $phone, PDO::PARAM_STR);
		$stmt2->bindParam(':company', $company, PDO::PARAM_INT);
		
		$stmt2->execute();
		
		echo "Eliminaciones exitosas en provUser y UserGroup";
	} catch (PDOException $e) {
		echo "Error en las eliminaciones: " . $e->getMessage();
		error_log("Error en las eliminaciones: " . $e->getMessage());
	}
	
	try {
		$sql = "DELETE FROM UserGroup WHERE phone = :phone AND company = :company AND idGroup = :idGroup";
		$stmt = $dbh->prepare($sql);
		
		$stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
		$stmt->bindParam(':company', $company, PDO::PARAM_STR);
		$stmt->bindParam(':idGroup', $idGroup, PDO::PARAM_INT);
		
		$stmt->execute();
		
		
	} catch (PDOException $e) {
		
		error_log("Error en las eliminaciones: " . $e->getMessage());
		
	}

	
	?>
	<img src="images/icons/confirm.png" /> Eliminado
     
    <?php 
	$dbh=null;
	$action = "phone => '".$phone."'";  
//	log_tmc($company, "Aprovisionamiento", "Eliminar Abonado", $action, $login); 
	?>
