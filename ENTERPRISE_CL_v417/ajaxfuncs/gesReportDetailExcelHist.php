<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$company = $_SESSION["id"];
$login = $_SESSION["user"];
$fecha = return_date_now_format('Ymd');
$date = $_GET['date'];
$phone = $_GET['phone'];
$carrier = $_GET['op'];
$status = $_GET['status'];
$mode = $_GET['mode'];
$user = $_GET['user'];
$cc = $_GET['cc'];
$role = return_role($login);

if (!empty($sendDateFrom)) {
    $sql_date = "AND timestamp >= '$sendDateFrom 00:00:00' AND timestamp <= '$sendDateTo 23:59:59'";
}

if (!empty($carrier)) {
    $sql_carrier = "AND carrier LIKE '%$carrier%'";
}

if (!empty($input_mode)) {
    $sql_input_mode = "AND input_mode = '$input_mode'";
}

if (!empty($phone)) {
    $sql_phone = "AND phone = '$phone'";
}

if (!empty($cc)) {
    $sql_cc = "AND cc = '$cc'";
}

if (!empty($status)) {
    if ($status == "QUEUED") {
        $sql_status = "AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED')";
    } else {
        $sql_status = "AND status = '$status'";
    }
}

if (!empty($status)) {
    if ($status == "QUEUED") {
        $sql_status = "AND status IN ('TRANS', 'PENDING', 'PROCESANDO', 'SENDING', 'QUEUED')";
    } else {
        $sql_status = "AND status = '$status'";
    }
}

if ($role == "admin") {
    $sql_login = "";
} else {
    $sql_login = "AND login = '$login'";
}

if (!empty($user)) {
    $sql_user = "AND login = '$user'";
}

header('Pragma: public');
header('Content-Type: text/plain; name=reporte-por-detalle-' . $fecha . '.csv');
header('Content-Transfer-Encoding: BASE64;');
header('Content-Disposition: attachment; filename="reporte-por-detalle-' . $fecha . '.csv"');

$query="SELECT * FROM trafficMT WHERE substring(timestamp,1,10) = ? ? ? ? ? ? ?";

echo "Celular;";
echo "Operador;";
echo "Estado;";
echo "Fecha de Envio;";
echo "Fecha de Despacho;";
echo "Tipo de Envío;";
echo "Tipo de Ingreso;";
echo "Mensaje;";
echo "Usuario;";
echo "Creado;";
echo "\n";
$stmt = $dbh->query($sql);
$stmt->execute(array($date, $sql_carrier, $sql_input_mode, $sql_phone, $sql_status, $sql_user, $sql_login));

while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $dateSend = return_dispatchtime($row['dispatchTime'], $row['timestamp']);
    echo $row['phone'] . ";";
    echo $carrier = return_carrier_name($row['carrier']) . ";";
    echo $status = return_status_name($row['status']) . ";";
    echo $dateSend = return_date_send($dateSend) . ";";
    echo return_date_send($row['deliverytime']) . ";";
    echo $type_send = return_type_send_name_new($row['receivedTime'], $row['dispatchTime']) . ";";
    echo $input_mode = return_input_mode_name($row['input_mode']) . ";";
    echo $row['msgtext'] . ";";
    echo $row['login'] . ";";
    echo $row['timestamp'] . ";";
    echo "\n";
}
$dbh=null;
?>
