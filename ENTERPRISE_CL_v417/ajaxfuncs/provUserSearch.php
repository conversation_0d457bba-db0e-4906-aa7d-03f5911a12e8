<?php

include('../dbConn.php');
include('../includes/php/general_funcs.php'); 
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php'); 

	$company 	= $_SESSION["id"]; 
	$login	  		= $_SESSION["user"]; 
	$phone    		= $_GET['phone'];
	if(strlen($phone) == 8)
	{
		$phone=$phone;
	}
	if(strlen($phone) == 9)
	{
	
		$phone=substr($phone,1,8);
	}
	if(strlen($phone) == 11)
	{
		$phone=substr($phone,3,8);
	}
	$first_name    = $_GET['first_name'];
	$last_name    = $_GET['last_name'];
	$sql_filtro = "";
	if(!empty($phone)) { $sql_phone = "AND phone LIKE '%$phone%'"; $sql_filtro.= $sql_phone;} 
	if(!empty($first_name)) { $sql_first_name = "AND name1 LIKE '%$first_name%'"; $sql_filtro.= $sql_first_name; } 
	if(!empty($last_name)) { $sql_last_name = "AND name2 LIKE '%$last_name%'"; $sql_filtro.= $sql_last_name; } 
	// $query  = " SELECT ";
	// $query .= " * ";
	// $query .= " FROM provUser ";
	// $query .= " WHERE company = $company $sql_filtro";
	// $sql 	= ($query); 
	$query = "SELECT * FROM provUser WHERE company = :company $sql_filtro";
    $stmt = $dbh->prepare($query);
    $stmt->bindParam(':company', $company, PDO::PARAM_INT);
    $stmt->execute();
		
	if($stmt->rowCount()== 0) { ?>

		<div class="alert_big"><img src="images/icons/alert.png"> No se encontraron resultados.</div>

	<?php } else {  ?>

	<table width="100%" border="0" cellpadding="0" cellspacing="0" class="table-list">
	<tr>
		<th>M&Oacute;VIL</th>
		<th>OPERADOR</th>
		<th>NOMBRE</th>
		<th>APELLIDO</th>
		<th>CREADO POR</th>
		<th>FECHA</th>
		<th>ACCI&Oacute;N</th>
	</tr>
     <?php $i = 0;
		while($row = $stmt->fetch(PDO::FETCH_ASSOC)) { 
		$res = $i%2;
		  
		if($res == 0) { $class= ""; } else { $class= "table-list-tr"; }
	
	?>
	<tr class="<?=$class?>">
		<td><?=$row['phone'] ?></td>
		<td><?=$carrier = return_carrier_name($row['carrier']);?></td>
		<td><?=$row['name1'] ?></td>
		<td><?=$row['name2'] ?></td>
		<td><?=$row['login'] ?></td>
		<td><?=$row['created'] ?></td>
		<td>
			<div id="delete<?=$row['phone'];?>"> 
				<a href="?seccion=provUserEdit&id=<?=$row['phone']?>" title="Editar Abonado">
					<img src="images/icons/edit_user.png">
				</a>  
				<a href="?seccion=provUserAsoc&phone=<?=$row['phone']?>" title="Asociar Abonado a Grupo">
					<img src="images/icons/list.png" >
				</a> 
				<img src="images/icons/delete.png" onclick="provUserDel(<?=$row['phone']?>); return false" title="Eliminar Abonado" class="img_button">
			</div>
		</td>
	</tr>
     <?php $i++; }
	 $dbh=null; ?>
	</table>

	<?php } ?>
