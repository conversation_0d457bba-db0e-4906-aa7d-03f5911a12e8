<?php
include('dbConn.php');
$response = array();
$code = $_SESSION["code"];
$password = filter_input(INPUT_POST, 'password');


if ($code == $password) {
                    $response["result"] = "1";
                    $response["message"] = "Redireccionando a la generacion del token.";
                } else {
                    $response["result"] = "2";
                    $response["message"] = "El codigo ingresado no corresponde al enviado";
                    $response["hideMessage"] =  "El codigo ingresado no corresponde al enviado" ;
                }
$responseJson = json_encode($response, JSON_FORCE_OBJECT);
echo $responseJson;
$dbh=null;
?>
