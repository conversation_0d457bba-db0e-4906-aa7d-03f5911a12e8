<?php
setcookie("cookie", "mcs", time() + 1200,"","",true,true);
include ('includes/php/general_funcs.php');
include ('includes/php/sections_funcs.php');
include ('config.php');
$login = $_SESSION["user"];
$id_account = $_SESSION["id_account"];
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>

<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>MCS | <?= COMPANY_NAME ?></title>
		<LINK REL="stylesheet" TYPE="text/css" HREF="../includes/style/style.css"/>
			<script language="javascript" type="text/javascript" src="../includes/javascript/functions.js"></script>
			<script type="text/javascript" src="../jquery-3.7.1.min.js"></script>
			<script language="javascript">
				//  Developed by Roshan Bhattarai 
				//  Visit http://roshanbh.com.np for this script and more.
				//  This notice MUST stay intact for legal use

				$(document).ready(function ()
				{
					$("#chpass_form").submit(function ()
					{
						//remove all the class add the messagebox classes and start fading
						
						//check the username exists or not from ajax
						var token = sessionStorage.getItem("token");
						var config = JSON.parse( sessionStorage.getItem('config') );
//						var sendData = {
//							passAct: $('#passAct').val(), 
//							passNew: $('#passNew').val(),
//							passReNew: $('#passReNew').val(),
//							rand: Math.random()	
//						};
						var passNew = $('#passNew').val();
						var passReNew = $('#passReNew').val();
						console.log(passNew);
						console.log(passReNew);
						var jsonData = {
							"id":$('#idAccount').val(),
							"username":$('#login').val(),
							"password": $('#passNew').val(),
							"oldPassword":$('#passAct').val()
						};
						var regular = /^[0-9a-zA-Z]{5,15}$/;
						if (regular.test(passNew)){
							if (regular.test(passReNew)){
						if( passNew === passReNew ){
							$.ajax({
								url: config.urlServiceAccount + "/api/user/changePass",
								type: 'POST',
								data: JSON.stringify(jsonData), 
								contentType: "application/json;charset=UTF-8",
								headers: {
									"Authorization": token
								},
								beforeSend: function (xhr) {
									$("#msgbox").removeClass().addClass('messagebox').text('Validando ...').fadeIn(1000);
								},
								
								success: function (data, textStatus, jqXHR) {
									if (data.code == '0')
									{
										$("#msgbox").html('Ingresando ...').addClass('messageboxok').fadeTo(900, 1, function (){
											document.location = 'mcs.php?seccion=home';
										});
									} else {
										$("#msgbox").html(data.message).addClass('messageboxerror').fadeTo(900, 1);
									}
								},
								error: function (jqXHR, textStatus, errorThrown) {
									console.log("error en envio de cambio de password");
									$("#msgbox").html("Error al enviar los datos").addClass('messageboxerror').fadeTo(900, 1);
								}
							});
						}else{
							$("#msgbox").html('Los Password nuevos no coinciden').addClass('messageboxerror').fadeTo(900, 1);
						}
					}else{
							$("#msgbox").html('Ingrese Password solo con letras o numeros largo de 5 a 15 caracteres').addClass('messageboxerror').fadeTo(900, 1);
						}
				}else{
							$("#msgbox").html('Ingrese Password solo con letras o numeros largo de 5 a 15 caracteres').addClass('messageboxerror').fadeTo(900, 1);
						}
						return false; //not to post the  form physically
					});
					
				});
			</script>
	</head>
	<body>
		<table width="100%" style="height:100%;" cellpadding="0" cellspacing="0">
			<tr>
				<td height="150" bgcolor="#58585a">
					<table width="900" height="150" align="center" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td height="100" width="100" valign="middle" align="left">
								<img src="images/logo.png" />
							</td>
							<td height="100" width="800" valign="middle" align="right">
								
							</td>
						</tr>
						<tr>
							<td height="50" valign="bottom" colspan="2">

							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td align="center" valign="top" height="400" bgcolor="#f0f0f0">
					<table width="900" border="0">
						<tr>
							<td align="left" valign="top" style="padding-top: 30px; padding-bottom: 30px;">
								<table width="900" border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td width="450" align="left" valign="top">
											<img src="images/intro.jpg" />
											<div style="width: 255px; height: 50px; background-color:#d10660; color:#fff; padding: 10px; text-align: center;">
												<span style="font-family: 'Merriweather Sans', sans-serif;	font-size: 20px; ">Mobile Corporate Suite<br /> <?= COMPANY_NAME ?>
												</span>
											</div>
										</td>
										<td width="450">
											<span class="container-text-title">Cambio de Contrase&ntilde;a</span>
											<br /><br />
											<form method="post" action="" id="chpass_form">
												<table width="300" border="0" cellpadding="0" cellspacing="0">
													<tr>
														<td>User</td>
														<td id="tdLogin" height="30" align="left">
															<input type="hidden" id="idAccount" value="<?=$id_account?>"></input>
															<input type="hidden" id="login" value="<?=$login?>"></input>
															<?=$login?>  
														</td>
													</tr>
													<tr>
														<td>Contrase&ntilde;a Actual</td>
														<td height="30" align="left">
															<input name="passAct" type="password" id="passAct" value="" maxlength="30"  autocomplete="off" />
														</td>
													</tr>
													<tr>
														<td>Contrase&ntilde;a nueva</td>
														<td height="30" align="left">
															<input name="passNew" type="password" id="passNew" value="" maxlength="30"  autocomplete="off" />
														</td>
													</tr>
													<tr>
														<td>Reingrese contrase&ntilde;a</td>
														<td height="30" align="left">
															<input name="passReNew" type="password" id="passReNew" value="" maxlength="30"  autocomplete="off" />
														</td>
													</tr>
													<tr>
														<td></td>
														<td height="30" align="left" valign="top"><br/>
															<input name="Submit" type="submit" id="submit" value="Guardar" class="buttom" />
														</td>
													</tr>
												</table>
												<table>
													<tr>
														<td height="50" align="left" valign="top">
															<br/>
															<span id="msgbox" style="display: none"></span>
														</td>
													</tr>
												</table>
											</form>
										</td>
									</tr>
								</table>

							</td>
						</tr>
					</table>

				</td>
			</tr>
			<tr>
				<td height="6" bgcolor="#cccccc" align="center"></td>
			</tr>
			<tr>
				<td height="100" bgcolor="#58585a" align="center"><?php copy_rights(); ?></td>
			</tr>
		</table>
	</body>
</html>
