<?php
	setcookie("cookie","mcs",time()+ 600);
	include ('includes/php/general_funcs.php');
	include ('includes/php/general_funcs_extra.php');
	include ('includes/php/sections_funcs.php');
	include ('includes/php/sections_funcs_extra.php');
	include ('dbConn.php');
	include ('config.php');
   
   if(empty($_COOKIE['cookie'])) { 
		header('Location: expire_session.php');
	}
	
	if(empty($_SESSION["id"])) {
			   header('Location: expire_session.php');
	}
	
	?>

    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">
	<head>
	
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>MCS | <?=COMPANY_NAME?></title>
     <link rel="stylesheet" href="includes/style/style.css" type="text/css" media="screen" />
     <link rel="stylesheet" type="text/css" media="all" href="includes/javascript/calendar/calendar-green.css" title="win2k-cold-1" />
     <script type="text/javascript" src="includes/javascript/calendar/calendar.js"></script>
     <script type="text/javascript" src="includes/javascript/calendar/calendar-es.js"></script>
     <script type="text/javascript" src="includes/javascript/calendar/calendar-setup.js"></script>
     <script type="text/javascript" src="includes/javascript/extra_funcs.js"></script>
     <script type="text/javascript" src="includes/javascript/functions.js"></script>
	</head><body>
 <table width="100%" style="height:100%;" cellpadding="0" cellspacing="0">
<tr>
	<td height="150" bgcolor="#58585a">
    <table width="900" height="150" align="center" border="0" cellpadding="0" cellspacing="0">
    <tr>
    	<td height="100" width="100" valign="middle" align="left"><img src="images/logo.png" /></td>
        <td height="100" width="600" valign="middle" align="left"></td>
        <td height="100" width="200" valign="middle" align="right"><?php session(); ?></td>
	</tr>
    <tr>
    	<td height="50" valign="bottom" colspan="3"></td>
	</tr>
    </table>
    </td>
</tr>
<tr>
	<td height="6" bgcolor="#cccccc" align="center"></td>
</tr>
<tr>
	<td align="center" valign="top" height="400" bgcolor="#f0f0f0">
    <table width="900" border="0">
    <tr>
    	<td align="left" valign="top" style="padding-top: 30px; padding-bottom: 30px;">
        <br />
        <?php
		$login = $_SESSION["user"];
		
		$query ="SELECT * FROM account WHERE login = ?";
		$stmt = $dbh->prepare($query);
    	$stmt->execute(array($login));
    	$result = $stmt->fetch(PDO::FETCH_OBJ);
		$dbh=null;
		
		if(empty($result->login)) { ?>
        <div class="alert_big">El usuario <b><?=$login;?></b> no est&aacute; habilitado. Favor verificar y volver a ingresar <a href='expire_session.php'><b>aqu&iacute;</b></a>.</div>
        <?php
		} else {
			header('location: mcs.php?seccion=sendNow');
		}
		?>
        </td>
	</tr>
    </table>
    
    </td>
</tr>
<tr>
	<td height="6" bgcolor="#cccccc" align="center"></td>
</tr>
<tr>
	<td height="100" bgcolor="#58585a" align="center"><?php copy_rights(); ?></td>
    
    
    
</tr>
</table>
	</body>
	</html>