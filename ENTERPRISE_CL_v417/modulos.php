<?php

switch ($_GET["seccion"]) {
    /*
     * home
     */
    case "home":
        home();
        break;
    /*
     * send
     */
    case "sendNow":
        sendNow();
        break;
    case "sendAfter":
        sendAfter();
        break;
    case "sendNowGroup":
        sendNowGroup();
        break;
    case "sendAfterGroup":
        sendAfterGroup();
        break;
    case "sendFile":
        sendFile();
        break;
    case "sendFileValue":
        sendFileValue();
        break;
    case "sendFileConvinated":
        sendFileConvinated();
        break;
    case "sendFileConvinatedValue":
        sendFileConvinatedValue();
        break;
    /*
     * provisioning
     */
    case "provUsers":
        provUsers();
        break;
    case "provUserSearch";
        provUserSearch();
        break;
    case "provUserAdd";
        provUserAdd();
        break;
    case "provUserDel":
        provUserDel();
        break;
    case "provUserLoadFile":
        provUserLoadFile();
        break;
    case "provUserLoadFileValue":
        provUserLoadFileValue();
        break;
    case "provUserEdit":
        provUserEdit();
        break;
    case "provUserAsoc":
        provUserAsoc();
        break;
    case "provUserAsocOk":
        provUserAsocOk();
        break;
    case "provGroups":
        provGroups();
        break;
    case "provGroupAdd":
        provGroupAdd();
        break;
    case "provGroupEdit":
        provGroupEdit();
        break;
    case "provGroupDel":
        provGroupDel();
        break;
    case "provGroupAsoc":
        provGroupAsoc();
        break;
    /*
     * report old
     */
    case "gesReport":
        gesReport();
        break;
    case "gesReportDetail":
        gesReportDetail();
        break;
    case "gesStatus":
        gesStatus();
        break;
    case "gesStatusDetail":
        gesStatusDetail();
        break;
    case "gesReportHist":
        gesReportHist();
        break;
    case "gesReportDetailHist":
        gesReportDetailHist();
        break;
    case "gesStatusHist":
        gesStatusHist();
        break;
    case "gesStatusDetailHist":
        gesStatusDetailHist();
        break;
    case "gesReportMo":
        gesReportMo();
        break;
    case "gesReportDetailMo":
        gesReportDetailMo();
        break;
    case "gesFileReport":
        gesFileReport();
        break;
    case "gesFileReportDetail":
        gesFileReportDetail();
        break;
    /*
     * send
     */
    case "sopPass":
        sopPass();
        break;
    case "sopConf":
        sopConf();
        break;
    case "sopEmail":
        sopEmail();
        break;
    case "sopManual":
        sopManual();
        break;
    /*
     * maintenance
     */
    case "mantenanceBlackList":
        maintenanceBlacklist();
        break;
    case "userMaintenance":
        maintenanceUser();
        break;
    /*
     * campaign
     */
    case "loadCampaignByFile":
        loadCampaignByFile();
        break;
    case "listPendingCampaign":
        listPendingCampaign();
        break;
    /*
     * Report
     */
    case "gesCustom":
        gesCustom();
        break;
    case "gesBillingDetail":
        gesBillingDetail();
        break;
    case "gesBillingConsolidated":
        gesBillingConsolidated();
        break;
    case "gesLoadHistorialDetail":
        gesLoadHistorialDetail();
        break;
    case "gesLoadHistorialConsolidated":
        gesLoadHistorialConsolidated();
        break;
    case "gesLoadDailyDetail":
        gesLoadDailyDetail();
        break;
    case "gesLoadDailyConsolidated":
        gesLoadDailyConsolidated();
        break;
    case "gesLoadDetailMo":
        gesLoadDetailMo();
        break;
    case "gesReportQueueReport":
        gesReportQueueReport();
        break;
    
}
?>
