encoding_source="ISO-8859-1"
encoding_destination="UTF-8"
path=/home/<USER>/workspaces/GitLab-TIAXA/DEMOS/DEMO_MX/web_php
directory_source=$path/DEMO_MX_ISO-8859-1
directory_destination=$path/DEMO_MX_UTF8
file=expire_session.php
CONVERT="iconv -f $encoding_source -t $encoding_destination//TRANSLIT "
function_convert()
{
	cd_directory=$1
	mkdir_directory=$2
	extension=$3
	for file in *.$extension; do
		source=$cd_directory/$file
		destination=$mkdir_directory/$file
		$CONVERT "$source" -o "$destination"
		echo "parse: $destination"
	done
}
function_crea_directorio()
{
	directory=$1
	if [ -d $directory ];
	then
		echo "$directory existe."
	else
	mkdir -p $directory
		echo "$directory creado."
	fi
}
extension="php"

# se cambian archivos sueltos
cd_directory="$directory_source"
mkdir_directory="$directory_destination"
cd $cd_directory
function_convert $cd_directory $mkdir_directory $extension

# se conviierte archivos directorio ajaxfuncs
directory="ajaxfuncs"
cd_directory="$directory_source/$directory"
mkdir_directory="$directory_destination/$directory"
cd $cd_directory
function_crea_directorio $mkdir_directory
function_convert $cd_directory $mkdir_directory $extension

# se conviierte archivos directorio includes/php
directory="includes/php"
cd_directory="$directory_source/$directory"
mkdir_directory="$directory_destination/$directory"
cd $cd_directory
function_crea_directorio $mkdir_directory
function_convert $cd_directory $mkdir_directory $extension

extension="js"
directory="includes/javascript"
cd_directory="$directory_source/$directory"
mkdir_directory="$directory_destination/$directory"
cd $cd_directory
function_crea_directorio $mkdir_directory
function_convert $cd_directory $mkdir_directory $extension

directory="includes/javascript/calendar"
cd_directory="$directory_source/$directory"
mkdir_directory="$directory_destination/$directory"
cd $cd_directory
function_crea_directorio $mkdir_directory
function_convert $cd_directory $mkdir_directory $extension


exit 0

