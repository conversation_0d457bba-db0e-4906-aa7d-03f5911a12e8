config:
  service: opensips
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 0
  task_config:
    cpu: "256"
    memory: "512"
    volumes:
      - name: converted-audios
    containerDefinitions:
      - name: statemachine-asterisk
        image: ${STATEMACHINE_IMG}
        environment:
          - name: "COMPlus_ThreadPool_ForceMinWorkerThreads"
            value: "100"
          - name: "CONFIG__MQTT__HOST"
            value: "127.0.0.1"
          - name: "IS_AWS"
            value: "true"
          - name: "CONFIG__MQTT__CREDENTIALS__PASSWORD"
            value: "NoviTech"
          - name: "CONFIG__SERILOG__MINIMUMLEVEL__OVERRIDE__StateWebServer.SqsService"
            value: "Information"
          - name: "CORECLR_ENABLE_PROFILING"
            value: "0"
          - name: "CONFIG__SERILOG__MINIMUMLEVEL__DEFAULT"
            value: "Information"
          - name: "CONFIG__SQSPUBLISH__QUEUEURL"
            value: "${SQS_EVENT_URL}"
          - name: "CONFIG__SQS__RegionEndpoint"
            value: "${SQS_REGION}"
          - name: "CONFIG__QUEUEPROVIDER"
            value: "MQTT"
          - name: "ARI_PASSWORD"
            value: "yothere"
          - name: "CONFIG__SQSDIALOUT__QUEUEURL"
            value: "${SQS_DIALOUT_URL}"
          - name: "CONFIG__SQSPUBLISH__WorkerCount"
            value: "2"
          - name: "CONFIG__SQS__QueueURL"
            value: "${SQS_URL}"
          - name: "CONFIG__MQTT__TOPIC"
            value: "topic/event/asterisk"
          - name: "CONFIG__BLACKLIST__ConnectionString"
            value: "blacklist_connectionstring"
          - name: "CONFIG__BLACKLIST__Enabled"
            value: "false"
          - name: "CONFIG__REDIS__HOST"
            value: "127.0.0.1"
          - name: "CONFIG__SQSDIALOUT__regionEndpoint"
            value: "${SQS_DIALOUT_REGION}"
          - name: "CONFIG__ROUTING__DefaultRouteResult__DialOut_SipServers__10.69.105.10__0"
            value: "*********"
          - name: "CONFIG__MQTT__PORT"
            value: "1883"
          - name: "CONFIG__MQTT__CREDENTIALS__USERNAME"
            value: "telegraf"
          - name: "CONFIG__ROUTING__DefaultRouteResult__DialOut_SipServers__10.69.105.40__0"
            value: "***********"
          - name: "CONFIG__SQSPUBLISH__regionEndpoint"
            value: "${SQS_EVENT_REGION}"
          - name: "NEW_RELIC_APP_NAME"
            value: "entel-IVR-StateMachine-Production"
          - name: "ARI_USERNAME"
            value: "ari4java"

        dependsOn:
          - containerName: telegraf
            condition: START
          - containerName: redis
            condition: START
          - containerName: mqtt
            condition: START
        logConfiguration:
          logDriver: awsfirelens
          options:
            region: "${CLOUDWATCH_REGION}"
            delivery_stream: "not_used"
            Name: "kinesis_firehose"
      - name: asterisk
        image: ${ASTERISK_IMG}
        essential: true
        environment:
          - name: SIP_PROXY_A
            value: "${SIP_PROXY_A}"
          - name: SIP_PROXY_B
            value: "${SIP_PROXY_B}"
          - name: IS_AWS
            value: "true"
        mountPoints:
          - sourceVolume: converted-audios
            containerPath: /opt/asterisk/var/lib/asterisk/sounds/pt-br
        dependsOn:
          - containerName: "audio-sync"
            condition: START
        logConfiguration:
          logDriver: awslogs
          options:
            awslogs-group: ivrcloud
            awslogs-region: "${CLOUDWATCH_REGION}"
            awslogs-stream-prefix: "asterisk"
      - name: redis
        image: redis/redis-stack-server:latest
        portMappings:
          # appears to be unnecessary
          - containerPort: 6379
            protocol: tcp
        essential: true
      - name: mqtt
        # this can cause issues
        image: ${MQTT_IMG}
        logConfiguration:
          logDriver: awslogs
          options:
            awslogs-group: ivrcloud
            awslogs-region: "${CLOUDWATCH_REGION}"
            awslogs-stream-prefix: "mqtt"
      - name: audio-sync
        image: amazon/aws-cli
        command:
          - bash
          - -c
          - |
            '
            aws s3 sync ${AUDIO_BUCKET_CONVERTED} ${DEST_FOLDER} && echo OK > /healthcheck;
            while sleep 900; do aws s3 sync ${AUDIO_BUCKET_CONVERTED} ${DEST_FOLDER} && echo OK > /healthcheck; done
            '
        healthCheck:
          retries: 3
          command:
            - "bash"
            - -c
            - "test -f /healthcheck"
          timeout: 5
          interval: 300
        environment:
          - name: AUDIO_BUCKET_CONVERTED
            value: ${S3_AUDIO_BUCKET_CONVERTED}
          - name: DEST_FOLDER
            value: converted-audios
        mountPoints:
          - sourceVolume: converted-audios
            containerPath: /converted-audios
      - name: forward
        image: ${FORWARDER_IMG}
        essential: true
        environment:
          - name: ARI_PASSWORD
            value: yothere
          - name: "ASTERISK_URL"
            value: "http://127.0.0.1:8088"
          - name: "MQTT_HOSTNAME"
            value: "127.0.0.1"
          - name: "MQTT_PASSWORD"
            value: "NoviTech"
          - name: "NEW_RELIC_HOME"
            value: "/app-dir/node_modules/newrelic"
          - name: "NEW_RELIC_ENABLED"
            value: "false"
          - name: "NEW_RELIC_AUDIT_LOG_ENABLED"
            value: "true"
          - name: "QUEUE_PROVIDER"
            value: "MQTT"
          - name: "MQTT_USERNAME"
            value: "telegraf"
          - name: "NEW_RELIC_LOG_ENABLED"
            value: "true"
          - name: "NEW_RELIC_NO_CONFIG_FILE"
            value: "true"
          - name: "EXTERNIP"
            value: "127.0.0.1"
          - name: "MQTT_TOPIC"
            value: "topic/event/asterisk"
          - name: "NEW_RELIC_LOG_LEVEL"
            value: "INFO"
          - name: "NEW_RELIC_APP_NAME"
            value: "Asterisk-Node.js"
          - name: "NEW_RELIC_LICENSE_KEY"
            value: "32d12e2be747d6f09797032b48d1863de83154d4"
          - name: "ARI_USERNAME"
            value: "ari4java"
      - name: telegraf-otel
        image: ${TELEGRAF_IMG}
        environment:
          - name: "TELEGRAF_OUTPUTS_0_password"
            value: "${TIMESCALEDB_PASSWORD}"
          - name: "TELEGRAF_INPUTS_0_type"
            value: "opentelemetry"
          - name: "TELEGRAF_OUTPUTS_0_type"
            value: "postgres"
          - name: "TELEGRAF_CONFIG_interval"
            value: "60s"
          - name: "TELEGRAF_CONFIG_flush_interval"
            value: "60s"
          - name: "TELEGRAF_CONFIG_round_interval"
            value: "true"
          - name: "TELEGRAF_CONFIG_flush_jitter"
            value: "1s"
          - name: "TELEGRAF_OUTPUTtabase_name"
            value: "tsdb"
          - name: "TELEGRAF_CONFIG_metric_batch_size"
            value: "500"
          - name: "TELEGRAF_OUTPUTS_0_user"
            value: "tsdbadmin"
          - name: "TELEGRAF_CONFIG_debug"
            value: "false"
          - name: "TELEGRAF_CONFIG_precision"
            value: "60s"
          - name: "TELEGRAF_OUTPUTS_0_schema"
            value: "ivrcloud_otel"
          - name: "TELEGRAF_CONFIG_omit_hostname"
            value: "false"
          - name: "TELEGRAF_CONFIG_quiet"
            value: "true"
          - name: "TELEGRAF_CONFIG_metric_buffer_limit"
            value: "2000000"
          - name: "TELEGRAF_OUTPUTS_0_port"
            value: "48532"
          - name: "TELEGRAF_OUTPUTS_0_server"
            value: "************"
        logConfiguration:
          logDriver: awslogs
          options:
            awslogs-group: ivrcloud
            awslogs-region: "${CLOUDWATCH_REGION}"
            awslogs-stream-prefix: "telegrafotel"
      - name: telegraf
        image: ${TELEGRAF_IMG}
        environment:
          - name: "TELEGRAF_OUTPUTS_0_password"
            value: "${TIMESCALEDB_PASSWORD}"
          - name: "TELEGRAF_INPUTS_0_type"
            value: "httplistener"
          - name: "TELEGRAF_CONFIG_interval"
            value: "60s"
          - name: "TELEGRAF_OUTPUTS_0_type"
            value: "postgres"
          - name: "TELEGRAF_CONFIG_flush_interval"
            value: "60s"
          - name: "TELEGRAF_CONFIG_round_interval"
            value: "true"
          - name: "TELEGRAF_CONFIG_flush_jitter"
            value: "1s"
          - name: "TELEGRAF_CONFIG_metric_batch_size"
            value: "500"
          - name: "TELEGRAF_OUTPUTS_0_database_name"
            value: "tsdb"
          - name: "TELEGRAF_CONFIG_debug"
            value: "false"
          - name: "TELEGRAF_CONFIG_precision"
            value: "60s"
          - name: "TELEGRAF_OUTPUTS_0_user"
            value: "tsdbadmin"
          - name: "TELEGRAF_INPUTS_0_host"
            value: "localhost"
          - name: "TELEGRAF_OUTPUTS_0_schema"
            value: "ivrcloud"
          - name: "TELEGRAF_CONFIG_omit_hostname"
            value: "false"
          - name: "TELEGRAF_CONFIG_quiet"
            value: "true"
          - name: "TELEGRAF_CONFIG_metric_buffer_limit"
            value: "2000000"
          - name: "TELEGRAF_OUTPUTS_0_port"
            value: "48532"
          - name: "TELEGRAF_OUTPUTS_0_server"
            value: "************"
        logConfiguration:
          logDriver: awslogs
          options:
            awslogs-group: ivrcloud
            awslogs-region: "${CLOUDWATCH_REGION}"
            awslogs-stream-prefix: "telegraf"
      - name: "logrouter"
        image: "${LOGROUTER_IMG}"
        essential: true
        environment:
          - name: "fluentbit_outputs_0_logs_type"
            value: "firehose"
          - name: "fluentbit_outputs_0_cdr_stream_name"
            value: "${LOGROUTER_CDR_STREAM_NAME}"
          - name: "MODE"
            value: "cloud"
          - name: "fluentbit_outputs_0_match"
            value: "statemachineivr"
          - name: "fluentbit_outputs_0_logs_region"
            value: "${LOGROUTER_REGION}"
          - name: "fluentbit_outputs_0_cdr_type"
            value: "firehose"
          - name: "fluentbit_outputs_0_cdr_region"
            value: "${LOGROUTER_CDR_REGION}"
          - name: "fluentbit_outputs_0_logs_stream_name"
            value: "${LOGROUTER_STREAM_NAME}"
        firelensConfiguration:
          type: "fluentbit"
          options:
            "config-file-type": "file"
            "config-file-value": "/fluent-bit/configs/parse-json.conf"
            "enable-ecs-log-metadata": "true"
