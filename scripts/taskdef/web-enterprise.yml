config:
  service: web-enterprise
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: converted-audios
    containerDefinitions:
      - name: app
        image: ${ENTERPRISE_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "web-enterprise"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "web-enterprise"
        portMappings:
          - containerPort: 80   # NGINX escucha en el puerto 80 para el tráfico del ALB
        healthCheck:
          command: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
          interval: 30
          timeout: 5
          retries: 3
          startPeriod: 60

    
