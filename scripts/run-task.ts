import { ECSClient, RunTaskCommand, TaskOverride } from '@aws-sdk/client-ecs'
import { AssumeRoleCommand, STSClient } from '@aws-sdk/client-sts'
import { fromTokenFile } from '@aws-sdk/credential-providers'
import { promises as fsp } from 'fs'
import { parse as yamlParse } from 'yaml'

const assumeRole = async (roleArn: string, sessionName: string) => {
    console.log(`calling assume role using ${process.env.AWS_OIDC_ROLE_ARN} as web identty`)
    const client = new STSClient({
        credentials: fromTokenFile({
            roleArn: process.env.AWS_OIDC_ROLE_ARN,
            roleSessionName: sessionName
        }),
    })
    console.log(`trying to assume role ${roleArn}`)
    try {
        const r = await client.send(new AssumeRoleCommand({ RoleArn: roleArn, RoleSessionName: sessionName }))
        console.log(`assume role result: ${r}`)
        const creds = r.Credentials!
        return {
            accessKeyId: creds.AccessKeyId!,
            secretAccessKey: creds.SecretAccessKey!,
            sessionToken: creds.SessionToken!,
            expiration: creds.Expiration!
        }
    } catch (ex) {
        console.log("error assuming role", ex)
        throw ex;
    }
}

const getConfig = async (filename: string) => {
    const buffer = await fsp.readFile(filename, { encoding: "utf-8" })
    return yamlParse(buffer)
}

type TaskConfig = {
    cluster_name: string
    role_arn: string
    family: string
    containerOverrides: any
    count?: number
    overrides: TaskOverride
}
type TaskDefFile = {
    config: TaskConfig,
}

const run = async () => {
    const taskCfg: TaskDefFile = await getConfig("task.yml")
    const creds = await assumeRole(taskCfg.config.role_arn, "ecs")
    var ecs = new ECSClient({
        credentials: creds
    });
    const runResult = await ecs.send(new RunTaskCommand({
        taskDefinition: taskCfg.config.family,
        cluster: taskCfg.config.cluster_name,
        count: taskCfg.config.count || 1,
        overrides: taskCfg.config.overrides
    }))
    console.log("run result", JSON.stringify(runResult, a => a, 4))
}

const execution = run();
execution.catch(console.error)
execution.finally(() => console.log("completed"))