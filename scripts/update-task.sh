#!/bin/bash



apt-get update -y 2>&1 1>/dev/null && apt-get install gettext -y 2>&1 1>/dev/null

source dotenv
envsubst < task-template.yml > scripts/task.yml
envsubst < cluster-config.yml > scripts/cluster.yml
cd scripts/
cat cluster.yml task.yml
# move dev file to main file
npm install
export AWS_WEB_IDENTITY_TOKEN_FILE="/tmp/aws-web-identity-token-file"
echo ${BITBUCKET_STEP_OIDC_TOKEN} > ${AWS_WEB_IDENTITY_TOKEN_FILE}
echo "using oidc token $BITBUCKET_STEP_OIDC_TOKEN from file ${AWS_WEB_IDENTITY_TOKEN_FILE}"
npx ts-node update-service.ts