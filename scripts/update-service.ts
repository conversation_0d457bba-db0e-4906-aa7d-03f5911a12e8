import { STSClient, AssumeRoleCommand } from '@aws-sdk/client-sts'
import { CapacityProviderStrategyItem, Container, ContainerDefinition, DescribeServicesCommand, DescribeTaskDefinitionCommand, ECSClient, PropagateTags, RegisterTaskDefinitionCommand, RegisterTaskDefinitionCommandInput, UpdateServiceCommand } from '@aws-sdk/client-ecs'
import { parse as yamlParse } from 'yaml'
import { promises as fsp } from 'fs'
import { fromTokenFile } from '@aws-sdk/credential-providers'

type ClusterConfig = {
    config: {
        role_arn: string
        cluster: string
        region: string
    }
}
type TaskConfig = Omit<RegisterTaskDefinitionCommandInput, "containerDefinitions"> & {
    containerDefinitions: Array<
        { command_type: undefined } & ContainerDefinition |
        { command_type: "delete", name: string }
    >
}
type TaskDefFile = {
    config: {


        task_config: TaskConfig
        service: string
        service_config: {
            desiredCount?: number
            propagateTags?: PropagateTags
            enableECSManagedTags?: boolean
            capacityProviderStrategy?: CapacityProviderStrategyItem[]
            forceNewDeployment?: boolean
            healthCheckGracePeriodSeconds?: number
        }
    }
}
const assumeRole = async (roleArn: string, session_name = "default") => {
    console.log(`calling assume role using ${process.env.AWS_OIDC_ROLE_ARN} as web identty`)
    const client = new STSClient({
        credentials: fromTokenFile({
            roleArn: process.env.AWS_OIDC_ROLE_ARN,
            roleSessionName: session_name
        }),
    })
    console.log(`trying to assume role ${roleArn}`)
    try {
        const r = await client.send(new AssumeRoleCommand({ RoleArn: roleArn, RoleSessionName: session_name }))
        console.log(`assume role result: ${r}`)
        const creds = r.Credentials!
        return {
            accessKeyId: creds.AccessKeyId!,
            secretAccessKey: creds.SecretAccessKey!,
            sessionToken: creds.SessionToken!,
            expiration: creds.Expiration!
        }
    } catch (ex) {
        console.log("error assuming role", ex)
        throw ex;
    }
}
const getConfig = async (filename: string) => {
    const buffer = await fsp.readFile(filename, { encoding: "utf-8" })
    return yamlParse(buffer)
}

const run = async () => {
    const clusterCfg: ClusterConfig = (await getConfig("cluster.yml"))
    const taskCfg: TaskDefFile = (await getConfig("task.yml"))
    const creds = await assumeRole(clusterCfg.config.role_arn)
    const ecs = new ECSClient({
        credentials: creds,
        region: clusterCfg.config.region
    })
    const services = await ecs.send(new DescribeServicesCommand({ cluster: clusterCfg.config.cluster, services: [taskCfg.config.service] }))
    if (!services.services || services.services.length !== 1) {
        console.error("service not found", clusterCfg.config.cluster, taskCfg.config.service)
        throw new Error(`service not found: ${clusterCfg.config.cluster} / ${taskCfg.config.service}`)
    }
    const svc = services.services[0]

    const currentTaskDefinition = await ecs.send(new DescribeTaskDefinitionCommand({ taskDefinition: svc.taskDefinition! }))
    if (!currentTaskDefinition.taskDefinition) {
        throw new Error(`task definition not found: ${svc.taskDefinition}`)
    }
    const remoteTask = currentTaskDefinition.taskDefinition
    const { task_config } = taskCfg.config
    var definitions = task_config.containerDefinitions.filter(a => a.command_type === undefined);

    var containerResult: Array<ContainerDefinition> = []
    for (const c of remoteTask.containerDefinitions || []) {
        var newDef = definitions.find(a => a.name === c.name)
        if (newDef) {
            console.log("merging container", c, newDef)
            containerResult.push(mergeContainerDefinition(c, newDef))
        } else {
            console.log(`container ${c.name} is not configured in taskdef.yml.`)
        }
    }
    for (const c of definitions) {
        const existingDef = remoteTask.containerDefinitions.findIndex(a => a.name === c.name);
        if (existingDef === -1) {
            console.log(`add new container to task: ${c.name}`)
            containerResult.push(c)
        }
    }
    for (const { name } of task_config.containerDefinitions.filter(a => a.command_type === "delete")) {
        var existingIdx = containerResult.findIndex(x => x.name === name)
        if (existingIdx >= 0) {
            console.log(`removing container ${name} from task definition`)
            containerResult.splice(existingIdx)
        }
        else {
            console.log(`container ${name} does not exist in actual task definition`)
        }
    }

    var newTaskDef: RegisterTaskDefinitionCommandInput = {
        cpu: task_config.cpu || remoteTask.cpu,
        memory: task_config.memory || remoteTask.memory,
        requiresCompatibilities: remoteTask.compatibilities,
        enableFaultInjection: remoteTask.enableFaultInjection,
        ephemeralStorage: remoteTask.ephemeralStorage,
        executionRoleArn: remoteTask.executionRoleArn,
        family: remoteTask.family,
        inferenceAccelerators: remoteTask.inferenceAccelerators,
        ipcMode: remoteTask.ipcMode,
        networkMode: remoteTask.networkMode,
        pidMode: remoteTask.pidMode,
        placementConstraints: remoteTask.placementConstraints,
        proxyConfiguration: remoteTask.proxyConfiguration,
        runtimePlatform: remoteTask.runtimePlatform,
        taskRoleArn: remoteTask.taskRoleArn,
        volumes: task_config.volumes || remoteTask.volumes,
        containerDefinitions: containerResult
    }
    console.log("creating new task definition", JSON.stringify(newTaskDef))
    const newTask = await ecs.send(new RegisterTaskDefinitionCommand(newTaskDef))
    console.log("task defiinition created", newTask.taskDefinition.taskDefinitionArn)
    const { service_config } = taskCfg.config
    const updateServiceCmd = new UpdateServiceCommand({
        availabilityZoneRebalancing: svc.availabilityZoneRebalancing,
        desiredCount: service_config.desiredCount === undefined ? svc.desiredCount : service_config.desiredCount,
        capacityProviderStrategy: svc.capacityProviderStrategy,
        deploymentConfiguration: svc.deploymentConfiguration,
        enableECSManagedTags: svc.enableECSManagedTags,
        enableExecuteCommand: svc.enableExecuteCommand,
        forceNewDeployment: false,
        healthCheckGracePeriodSeconds: svc.healthCheckGracePeriodSeconds,
        loadBalancers: svc.loadBalancers,
        networkConfiguration: svc.networkConfiguration,
        placementConstraints: svc.placementConstraints,
        placementStrategy: svc.placementStrategy,
        platformVersion: svc.platformVersion,
        propagateTags: svc.propagateTags,
        serviceRegistries: svc.serviceRegistries,
        cluster: clusterCfg.config.cluster,
        service: taskCfg.config.service,
        taskDefinition: newTask.taskDefinition?.taskDefinitionArn!
    })
    console.log("updating service with new task definition")
    const ret = await ecs.send(updateServiceCmd)
    console.log("ecs update service result", ret)

}
const mergeContainerDefinition = (a: ContainerDefinition, b: Partial<ContainerDefinition & { command_type?: string }>) => {
    let x = { ...b }
    delete x.command_type
    const s: ContainerDefinition = { ...a, ...x }
    return s
}
const r = run();
r.catch(console.error)
r.finally(() => console.log("completed"))