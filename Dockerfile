# Usa PHP 5.6 con FPM
FROM php:5.6-fpm

# Instala Nginx
# Actualiza las fuentes de APT para usar el archivo de Debian, ya que "Stretch" (la base de esta imagen) está EOL.
# Luego, instala los paquetes y limpia la caché en una sola capa para optimizar.
# Se agrega --allow-unauthenticated porque las llaves GPG del repositorio de archivo están expiradas.
RUN sed -i -e 's/deb.debian.org/archive.debian.org/g' \
           -e 's|security.debian.org/debian-security|archive.debian.org/debian-security|g' \
           -e '/stretch-updates/d' /etc/apt/sources.list \
    && apt-get update && apt-get install -y --allow-unauthenticated --no-install-recommends nginx curl && rm -rf /var/lib/apt/lists/*
# Configura la zona horaria
RUN echo "date.timezone=America/Santiago" > /usr/local/etc/php/conf.d/timezone.ini

# Instala extensiones de PHP
RUN docker-php-ext-install mysql mysqli pdo pdo_mysql

# Copia archivo php.ini personalizado
COPY php.ini /usr/local/etc/php/php.ini

# Elimina la configuración por defecto de Nginx
RUN rm /etc/nginx/sites-enabled/default

# Copia la configuración de Nginx para la aplicación
COPY nginx.conf /etc/nginx/sites-available/default
RUN ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default

# Copia el código fuente
COPY ENTERPRISE_CL_v417/ /var/www/html/

# Copia el script de inicio
COPY start.sh /start.sh
RUN chmod +x /start.sh

# Ajusta permisos
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Expone el puerto 80 para Nginx
EXPOSE 80

# Inicia los servicios
CMD ["/start.sh"]
