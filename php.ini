[PHP]

;;;;;;;;;;;;;;;;;;;
; Configuración General ;
;;;;;;;;;;;;;;;;;;;
engine = On
short_open_tag = Off
expose_php = Off

;;;;;;;;;;;;;;;;;;;
; Configuración de Errores ;
;;;;;;;;;;;;;;;;;;;
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

;;;;;;;;;;;;;;;;;;;
; Configuración de Memoria y Ejecución ;
;;;;;;;;;;;;;;;;;;;
memory_limit = 256M
max_execution_time = 60
max_input_time = 60

;;;;;;;;;;;;;;;;;;;
; Configuración de Output Buffering ;
;;;;;;;;;;;;;;;;;;;
output_buffering = 8192

;;;;;;;;;;;;;;;;;;;
; Configuración de Subidas de Archivos ;
;;;;;;;;;;;;;;;;;;;
file_uploads = On
post_max_size = 32M
upload_max_filesize = 32M
max_file_uploads = 50

;;;;;;;;;;;;;;;;;;;
; Configuración de Sesiones ;
;;;;;;;;;;;;;;;;;;;
session.use_strict_mode = 1
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_httponly = 1
session.gc_probability = 1
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.cache_limiter = nocache

;;;;;;;;;;;;;;;;;;;
; Configuración de MySQL y MySQLi ;
;;;;;;;;;;;;;;;;;;;
mysqli.allow_persistent = On
mysqli.max_persistent = -1
mysqli.cache_size = 2000
mysqlnd.collect_statistics = On
mysqlnd.collect_memory_statistics = Off

;;;;;;;;;;;;;;;;;;;
; Configuración de OPCache ;
;;;;;;;;;;;;;;;;;;;
[opcache]
opcache.enable=1
opcache.enable_cli=1
opcache.memory_consumption=128
opcache.max_accelerated_files=10000
opcache.validate_timestamps=1
opcache.revalidate_freq=2
opcache.fast_shutdown=1

