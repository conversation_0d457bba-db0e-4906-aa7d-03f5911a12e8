server {
    listen 80;
    server_name localhost;

    root /var/www/html;
    index index.php index.html;

    # Endpoint para el Health Check de ECS/ALB
    location /health {
        access_log off;
        return 200 "Web Enterprise inicializada";
        add_header Content-Type text/plain;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        # El contenedor php-fpm escucha en el puerto 9000
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Denegar acceso a archivos .htaccess
    location ~ /\.ht {
        deny all;
    }
}