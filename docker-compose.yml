version: '3.3'
services:
  web:
    image: 'osdo-php'
    build:
      context: .
    ports:
      - "80:80"
    working_dir: /var/www/html
    # Se elimina el 'command' para que use el CMD del Dockerfile: ["/start.sh"]
    volumes:
      # Monta archivos específicos para desarrollo, usando rutas relativas al directorio raíz
      - ./ENTERPRISE_CL_v417/config.php:/var/www/html/config.php
      - ./ENTERPRISE_CL_v417/index.php:/var/www/html/index.php
